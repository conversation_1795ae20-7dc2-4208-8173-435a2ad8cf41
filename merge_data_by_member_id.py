#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
根据member_id匹配合并提币地址数据和完整数据
只保留在合并后的完整数据.xlsx中存在的member_id
"""

import pandas as pd
import numpy as np
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment
import warnings
warnings.filterwarnings('ignore')

def load_and_merge_data():
    """加载并合并数据"""
    print("=== 加载数据 ===")
    
    # 1. 加载提币地址数据
    print("正在加载提币地址.csv...")
    df_withdraw = pd.read_csv('提币地址.csv')
    print(f"提币地址数据: {len(df_withdraw):,} 行, {df_withdraw['member_id'].nunique():,} 个唯一member_id")
    
    # 2. 加载合并后的完整数据
    print("正在加载合并后的完整数据.xlsx...")
    df_complete = pd.read_excel('合并后的完整数据.xlsx', sheet_name='合并数据')
    print(f"完整数据: {len(df_complete):,} 行, {df_complete['member_id'].nunique():,} 个唯一member_id")
    
    # 3. 获取有效的member_id列表
    valid_member_ids = set(df_complete['member_id'].unique())
    print(f"有效member_id数量: {len(valid_member_ids):,}")
    
    # 4. 筛选提币地址数据，只保留有效的member_id
    print("正在筛选提币地址数据...")
    df_withdraw_filtered = df_withdraw[df_withdraw['member_id'].isin(valid_member_ids)].copy()
    print(f"筛选后提币地址数据: {len(df_withdraw_filtered):,} 行, {df_withdraw_filtered['member_id'].nunique():,} 个唯一member_id")
    
    # 5. 合并数据
    print("正在合并数据...")
    df_merged = pd.merge(
        df_withdraw_filtered,
        df_complete,
        on='member_id',
        how='inner'
    )
    print(f"合并后数据: {len(df_merged):,} 行, {df_merged['member_id'].nunique():,} 个唯一member_id")
    
    # 6. 统计信息
    print("\n=== 数据匹配统计 ===")
    print(f"原始提币地址数据: {len(df_withdraw):,} 行")
    print(f"有效member_id数量: {len(valid_member_ids):,}")
    print(f"筛选后提币地址数据: {len(df_withdraw_filtered):,} 行")
    print(f"最终合并数据: {len(df_merged):,} 行")
    print(f"数据保留率: {len(df_merged)/len(df_withdraw)*100:.2f}%")
    
    # 7. 检查数据完整性
    print("\n=== 数据完整性检查 ===")
    missing_members = valid_member_ids - set(df_withdraw['member_id'].unique())
    if missing_members:
        print(f"完整数据中有 {len(missing_members)} 个member_id在提币地址数据中不存在")
    else:
        print("所有有效member_id都在提币地址数据中存在")
    
    return df_merged, df_withdraw_filtered, df_complete

def analyze_merged_data(df_merged):
    """分析合并后的数据"""
    print("\n=== 合并数据分析 ===")
    
    # 基本统计
    print(f"总记录数: {len(df_merged):,}")
    print(f"唯一用户数: {df_merged['member_id'].nunique():,}")
    print(f"唯一地址数: {df_merged['address'].nunique():,}")
    print(f"唯一币种数: {df_merged['currency'].nunique():,}")
    
    # 时间范围
    df_merged['dt'] = pd.to_datetime(df_merged['dt'], format='%Y%m%d')
    print(f"时间范围: {df_merged['dt'].min().strftime('%Y-%m-%d')} 至 {df_merged['dt'].max().strftime('%Y-%m-%d')}")
    
    # 金额统计
    amount_data = df_merged.dropna(subset=['currency_usdt_amount'])
    if len(amount_data) > 0:
        print(f"有金额数据的交易: {len(amount_data):,} ({len(amount_data)/len(df_merged)*100:.1f}%)")
        print(f"总金额: ${amount_data['currency_usdt_amount'].sum():,.2f} USDT")
        print(f"平均金额: ${amount_data['currency_usdt_amount'].mean():.2f} USDT")
        print(f"中位数金额: ${amount_data['currency_usdt_amount'].median():.2f} USDT")
    
    # 国家分布
    print(f"\n=== 用户国家分布（前10） ===")
    country_dist = df_merged['member_country'].value_counts().head(10)
    for country, count in country_dist.items():
        unique_users = df_merged[df_merged['member_country'] == country]['member_id'].nunique()
        print(f"{country}: {count:,} 笔交易, {unique_users:,} 个用户")
    
    # 币种分布
    print(f"\n=== 币种分布（前10） ===")
    currency_dist = df_merged['currency'].value_counts().head(10)
    for currency, count in currency_dist.items():
        print(f"{currency}: {count:,} 笔交易")
    
    return df_merged

def identify_groups_in_merged_data(df_merged):
    """在合并数据中识别团伙"""
    print("\n=== 团伙识别（基于合并数据） ===")
    
    # 识别团伙地址（3人以上共享）
    address_user_counts = df_merged.groupby('address')['member_id'].nunique()
    group_addresses = address_user_counts[address_user_counts >= 3].index.tolist()
    
    print(f"团伙地址数量: {len(group_addresses):,}")
    print(f"团伙地址占比: {len(group_addresses)/df_merged['address'].nunique()*100:.1f}%")
    
    # 团伙统计
    group_transactions = df_merged[df_merged['address'].isin(group_addresses)]
    print(f"团伙交易数: {len(group_transactions):,}")
    print(f"团伙用户数: {group_transactions['member_id'].nunique():,}")
    
    # 团伙规模分布
    group_sizes = address_user_counts[address_user_counts >= 3]
    print(f"\n=== 团伙规模分布 ===")
    print(f"3-10人团伙: {len(group_sizes[(group_sizes >= 3) & (group_sizes <= 10)]):,}")
    print(f"11-50人团伙: {len(group_sizes[(group_sizes >= 11) & (group_sizes <= 50)]):,}")
    print(f"51-100人团伙: {len(group_sizes[(group_sizes >= 51) & (group_sizes <= 100)]):,}")
    print(f"100人以上团伙: {len(group_sizes[group_sizes > 100]):,}")
    print(f"最大团伙规模: {group_sizes.max()}")
    
    return group_addresses, group_transactions

def create_comprehensive_excel_report(df_merged, df_complete, group_addresses):
    """创建综合Excel报告"""
    print("\n=== 生成综合Excel报告 ===")
    
    wb = openpyxl.Workbook()
    wb.remove(wb.active)
    
    # 1. 数据概览
    create_overview_sheet(wb, df_merged, df_complete)
    
    # 2. 完整合并数据（前10000行作为样本）
    create_merged_data_sheet(wb, df_merged)
    
    # 3. 用户汇总信息
    create_user_summary_sheet(wb, df_merged)
    
    # 4. 团伙分析
    create_group_analysis_sheet(wb, df_merged, group_addresses)
    
    # 5. 国家分析
    create_country_analysis_sheet(wb, df_merged)
    
    # 6. 币种分析
    create_currency_analysis_sheet(wb, df_merged)
    
    # 7. 金额分析
    create_amount_analysis_sheet(wb, df_merged)
    
    filename = '基于member_id匹配的完整分析报告.xlsx'
    wb.save(filename)
    print(f"综合Excel报告已保存: {filename}")
    
    return filename

def create_overview_sheet(wb, df_merged, df_complete):
    """创建数据概览工作表"""
    ws = wb.create_sheet("数据概览")
    
    # 标题
    ws['A1'] = '基于member_id匹配的数据分析概览'
    ws['A1'].font = Font(size=16, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center')
    ws.merge_cells('A1:D1')
    
    # 基本统计
    ws['A3'] = '数据匹配统计'
    ws['A3'].font = Font(size=14, bold=True)
    
    amount_data = df_merged.dropna(subset=['currency_usdt_amount'])
    
    stats = [
        ['统计项目', '数值', '说明'],
        ['合并后总记录数', f"{len(df_merged):,}", '匹配成功的提币记录'],
        ['唯一用户数', f"{df_merged['member_id'].nunique():,}", '有提币记录的用户'],
        ['唯一地址数', f"{df_merged['address'].nunique():,}", '使用的提币地址'],
        ['唯一币种数', f"{df_merged['currency'].nunique():,}", '涉及的币种'],
        ['有金额数据记录', f"{len(amount_data):,}", f"占比 {len(amount_data)/len(df_merged)*100:.1f}%"],
        ['总金额(USDT)', f"${amount_data['currency_usdt_amount'].sum():,.2f}", '所有有金额记录的总和'],
        ['平均金额(USDT)', f"${amount_data['currency_usdt_amount'].mean():.2f}", '单笔交易平均金额'],
        ['时间跨度', f"{df_merged['dt'].min().strftime('%Y-%m-%d')} 至 {df_merged['dt'].max().strftime('%Y-%m-%d')}", '数据时间范围'],
        ['', '', ''],
        ['数据来源统计', '', ''],
        ['原始提币地址数据', '238,309', '原始CSV文件记录数'],
        ['有效用户数据', f"{len(df_complete):,}", 'Excel文件中的用户数'],
        ['数据保留率', f"{len(df_merged)/238309*100:.2f}%", '最终保留的数据比例']
    ]
    
    for i, row_data in enumerate(stats, 4):
        for j, value in enumerate(row_data, 1):
            cell = ws.cell(row=i, column=j, value=value)
            if i == 4 or i == 14:  # 表头行
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')
    
    # 调整列宽
    for col in range(1, 5):
        ws.column_dimensions[chr(64 + col)].width = 25

def create_merged_data_sheet(wb, df_merged):
    """创建合并数据工作表（样本数据）"""
    ws = wb.create_sheet("合并数据样本")
    
    # 标题
    ws['A1'] = f'合并数据样本（前10,000条记录）'
    ws['A1'].font = Font(size=16, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center')
    ws.merge_cells('A1:O1')
    
    # 选择关键列
    key_columns = [
        'member_id', 'digital_id', 'currency', 'address', 'currency_usdt_amount',
        'dt', 'member_country', 'kyc_country', 'last_login_country',
        'first_operation_country', 'state', 'txid', 'actual_amount', 'fee', 'origin_x'
    ]
    
    sample_df = df_merged[key_columns].head(10000)
    
    # 表头
    for col, header in enumerate(key_columns, 1):
        cell = ws.cell(row=3, column=col, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')
    
    # 填充数据
    for row_idx, (_, row_data) in enumerate(sample_df.iterrows(), 4):
        for col_idx, value in enumerate(row_data, 1):
            if col_idx == 6:  # dt列
                if pd.notna(value) and hasattr(value, 'strftime'):
                    ws.cell(row=row_idx, column=col_idx, value=value.strftime('%Y-%m-%d'))
                else:
                    ws.cell(row=row_idx, column=col_idx, value=str(value) if pd.notna(value) else '')
            else:
                ws.cell(row=row_idx, column=col_idx, value=str(value) if pd.notna(value) else '')
    
    # 调整列宽
    for col in range(1, len(key_columns) + 1):
        ws.column_dimensions[chr(64 + col)].width = 15
    
    print(f"合并数据样本导出完成（前10,000条）")

def create_user_summary_sheet(wb, df_merged):
    """创建用户汇总信息工作表"""
    ws = wb.create_sheet("用户汇总信息")
    
    # 标题
    ws['A1'] = '用户汇总信息'
    ws['A1'].font = Font(size=16, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center')
    ws.merge_cells('A1:L1')
    
    # 按用户汇总
    user_summary = df_merged.groupby('member_id').agg({
        'digital_id': 'first',
        'address': 'nunique',
        'currency': 'nunique',
        'currency_usdt_amount': ['count', 'sum', 'mean'],
        'member_country': 'first',
        'kyc_country': 'first',
        'last_login_country': 'first',
        'dt': ['min', 'max', 'count']
    }).round(2)
    
    # 重命名列
    user_summary.columns = [
        'digital_id', 'unique_addresses', 'unique_currencies', 'transactions_with_amount',
        'total_amount', 'avg_amount', 'member_country', 'kyc_country', 'last_login_country',
        'first_transaction', 'last_transaction', 'total_transactions'
    ]
    
    user_summary = user_summary.reset_index()
    
    # 表头
    headers = list(user_summary.columns)
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=3, column=col, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')
    
    # 填充数据（前5000个用户）
    sample_users = user_summary.head(5000)
    for row_idx, (_, row_data) in enumerate(sample_users.iterrows(), 4):
        for col_idx, value in enumerate(row_data, 1):
            if col_idx in [10, 11]:  # 日期列
                if pd.notna(value) and hasattr(value, 'strftime'):
                    ws.cell(row=row_idx, column=col_idx, value=value.strftime('%Y-%m-%d'))
                else:
                    ws.cell(row=row_idx, column=col_idx, value=str(value) if pd.notna(value) else '')
            else:
                ws.cell(row=row_idx, column=col_idx, value=str(value) if pd.notna(value) else '')
    
    # 调整列宽
    for col in range(1, len(headers) + 1):
        ws.column_dimensions[chr(64 + col)].width = 15
    
    print(f"用户汇总信息导出完成（前5,000个用户）")

def create_group_analysis_sheet(wb, df_merged, group_addresses):
    """创建团伙分析工作表"""
    ws = wb.create_sheet("团伙分析")

    # 标题
    ws['A1'] = f'团伙分析（共{len(group_addresses)}个团伙地址）'
    ws['A1'].font = Font(size=16, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center')
    ws.merge_cells('A1:J1')

    if len(group_addresses) == 0:
        ws['A3'] = '未发现团伙地址（3人以上共享地址）'
        return

    # 分析每个团伙地址
    group_analysis = []
    for address in group_addresses:
        group_data = df_merged[df_merged['address'] == address]
        members = group_data['member_id'].unique()

        # 国家分布
        countries = group_data['member_country'].value_counts()
        main_country = countries.index[0] if len(countries) > 0 else 'Unknown'

        # 金额统计
        amount_data = group_data.dropna(subset=['currency_usdt_amount'])

        group_info = {
            'address': address,
            'member_count': len(members),
            'total_transactions': len(group_data),
            'currencies': list(group_data['currency'].unique()),
            'main_country': main_country,
            'country_diversity': len(group_data['member_country'].unique()),
            'total_amount': amount_data['currency_usdt_amount'].sum() if len(amount_data) > 0 else 0,
            'avg_amount': amount_data['currency_usdt_amount'].mean() if len(amount_data) > 0 else 0,
            'date_range': f"{group_data['dt'].min().strftime('%Y-%m-%d')} 至 {group_data['dt'].max().strftime('%Y-%m-%d')}",
            'active_days': (group_data['dt'].max() - group_data['dt'].min()).days + 1
        }
        group_analysis.append(group_info)

    # 按成员数量排序
    group_analysis.sort(key=lambda x: x['member_count'], reverse=True)

    # 表头
    headers = ['团伙地址', '成员数', '总交易数', '币种数', '主要币种', '主要国家', '国家多样性', '总金额(USDT)', '平均金额(USDT)', '活动时间']
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=3, column=col, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')

    # 填充数据
    for i, group in enumerate(group_analysis, 4):
        ws.cell(row=i, column=1, value=group['address'])
        ws.cell(row=i, column=2, value=group['member_count'])
        ws.cell(row=i, column=3, value=group['total_transactions'])
        ws.cell(row=i, column=4, value=len(group['currencies']))
        ws.cell(row=i, column=5, value=', '.join(group['currencies'][:3]))
        ws.cell(row=i, column=6, value=group['main_country'])
        ws.cell(row=i, column=7, value=group['country_diversity'])
        ws.cell(row=i, column=8, value=f"{group['total_amount']:.2f}")
        ws.cell(row=i, column=9, value=f"{group['avg_amount']:.2f}")
        ws.cell(row=i, column=10, value=group['date_range'])

        # 大型团伙着色
        if group['member_count'] >= 50:
            for col in range(1, 11):
                ws.cell(row=i, column=col).fill = PatternFill(start_color='FFE6E6', end_color='FFE6E6', fill_type='solid')

    # 调整列宽
    for col in range(1, 11):
        ws.column_dimensions[chr(64 + col)].width = 15

    print(f"团伙分析导出完成（{len(group_analysis)}个团伙）")

def create_country_analysis_sheet(wb, df_merged):
    """创建国家分析工作表"""
    ws = wb.create_sheet("国家分析")

    # 标题
    ws['A1'] = '用户国家分布分析'
    ws['A1'].font = Font(size=16, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center')
    ws.merge_cells('A1:H1')

    # 按国家统计
    country_stats = df_merged.groupby('member_country').agg({
        'member_id': 'nunique',
        'address': 'nunique',
        'currency': 'nunique',
        'currency_usdt_amount': ['count', 'sum', 'mean'],
        'dt': ['min', 'max']
    }).round(2)

    country_stats.columns = ['unique_users', 'unique_addresses', 'unique_currencies',
                           'transactions_with_amount', 'total_amount', 'avg_amount',
                           'first_transaction', 'last_transaction']

    country_stats = country_stats.reset_index()
    country_stats = country_stats.sort_values('unique_users', ascending=False)

    # 表头
    headers = ['国家', '用户数', '地址数', '币种数', '有金额交易数', '总金额(USDT)', '平均金额(USDT)', '首次交易', '最后交易']
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=3, column=col, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')

    # 填充数据
    for i, (_, row) in enumerate(country_stats.iterrows(), 4):
        ws.cell(row=i, column=1, value=row['member_country'])
        ws.cell(row=i, column=2, value=row['unique_users'])
        ws.cell(row=i, column=3, value=row['unique_addresses'])
        ws.cell(row=i, column=4, value=row['unique_currencies'])
        ws.cell(row=i, column=5, value=row['transactions_with_amount'])
        ws.cell(row=i, column=6, value=f"{row['total_amount']:.2f}")
        ws.cell(row=i, column=7, value=f"{row['avg_amount']:.2f}")
        ws.cell(row=i, column=8, value=row['first_transaction'].strftime('%Y-%m-%d') if pd.notna(row['first_transaction']) and hasattr(row['first_transaction'], 'strftime') else str(row['first_transaction']) if pd.notna(row['first_transaction']) else '')
        ws.cell(row=i, column=9, value=row['last_transaction'].strftime('%Y-%m-%d') if pd.notna(row['last_transaction']) and hasattr(row['last_transaction'], 'strftime') else str(row['last_transaction']) if pd.notna(row['last_transaction']) else '')

    # 调整列宽
    for col in range(1, 10):
        ws.column_dimensions[chr(64 + col)].width = 15

    print(f"国家分析导出完成（{len(country_stats)}个国家）")

def create_currency_analysis_sheet(wb, df_merged):
    """创建币种分析工作表"""
    ws = wb.create_sheet("币种分析")

    # 标题
    ws['A1'] = '币种使用分析'
    ws['A1'].font = Font(size=16, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center')
    ws.merge_cells('A1:H1')

    # 按币种统计
    currency_stats = df_merged.groupby('currency').agg({
        'member_id': 'nunique',
        'address': 'nunique',
        'member_country': 'nunique',
        'currency_usdt_amount': ['count', 'sum', 'mean'],
        'dt': ['min', 'max']
    }).round(2)

    currency_stats.columns = ['unique_users', 'unique_addresses', 'unique_countries',
                            'transactions_with_amount', 'total_amount', 'avg_amount',
                            'first_transaction', 'last_transaction']

    currency_stats = currency_stats.reset_index()
    currency_stats = currency_stats.sort_values('unique_users', ascending=False)

    # 表头
    headers = ['币种', '用户数', '地址数', '国家数', '有金额交易数', '总金额(USDT)', '平均金额(USDT)', '首次交易', '最后交易']
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=3, column=col, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')

    # 填充数据
    for i, (_, row) in enumerate(currency_stats.iterrows(), 4):
        ws.cell(row=i, column=1, value=row['currency'])
        ws.cell(row=i, column=2, value=row['unique_users'])
        ws.cell(row=i, column=3, value=row['unique_addresses'])
        ws.cell(row=i, column=4, value=row['unique_countries'])
        ws.cell(row=i, column=5, value=row['transactions_with_amount'])
        ws.cell(row=i, column=6, value=f"{row['total_amount']:.2f}")
        ws.cell(row=i, column=7, value=f"{row['avg_amount']:.2f}")
        ws.cell(row=i, column=8, value=row['first_transaction'].strftime('%Y-%m-%d') if pd.notna(row['first_transaction']) and hasattr(row['first_transaction'], 'strftime') else str(row['first_transaction']) if pd.notna(row['first_transaction']) else '')
        ws.cell(row=i, column=9, value=row['last_transaction'].strftime('%Y-%m-%d') if pd.notna(row['last_transaction']) and hasattr(row['last_transaction'], 'strftime') else str(row['last_transaction']) if pd.notna(row['last_transaction']) else '')

    # 调整列宽
    for col in range(1, 10):
        ws.column_dimensions[chr(64 + col)].width = 15

    print(f"币种分析导出完成（{len(currency_stats)}个币种）")

def create_amount_analysis_sheet(wb, df_merged):
    """创建金额分析工作表"""
    ws = wb.create_sheet("金额分析")

    # 标题
    ws['A1'] = '金额分布分析'
    ws['A1'].font = Font(size=16, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center')
    ws.merge_cells('A1:F1')

    # 金额区间分析
    amount_data = df_merged.dropna(subset=['currency_usdt_amount'])

    if len(amount_data) == 0:
        ws['A3'] = '无有效金额数据'
        return

    # 定义金额区间
    amount_ranges = [
        ('0-10U', 0, 10),
        ('10-50U', 10, 50),
        ('50-100U', 50, 100),
        ('100-200U', 100, 200),
        ('200-500U', 200, 500),
        ('500-1000U', 500, 1000),
        ('1000-5000U', 1000, 5000),
        ('5000-10000U', 5000, 10000),
        ('10000U以上', 10000, float('inf'))
    ]

    ws['A3'] = '金额区间分布'
    ws['A3'].font = Font(size=14, bold=True)

    headers = ['金额区间', '交易数量', '占比', '用户数', '总金额(USDT)', '平均金额(USDT)']
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=5, column=col, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')

    for i, (range_name, min_val, max_val) in enumerate(amount_ranges, 6):
        if max_val == float('inf'):
            range_data = amount_data[amount_data['currency_usdt_amount'] >= min_val]
        else:
            range_data = amount_data[(amount_data['currency_usdt_amount'] >= min_val) & (amount_data['currency_usdt_amount'] < max_val)]

        ws.cell(row=i, column=1, value=range_name)
        ws.cell(row=i, column=2, value=len(range_data))
        ws.cell(row=i, column=3, value=f"{len(range_data)/len(amount_data)*100:.1f}%")
        ws.cell(row=i, column=4, value=range_data['member_id'].nunique())
        ws.cell(row=i, column=5, value=f"{range_data['currency_usdt_amount'].sum():.2f}")
        ws.cell(row=i, column=6, value=f"{range_data['currency_usdt_amount'].mean():.2f}" if len(range_data) > 0 else "0")

        # 200U分界线着色
        if min_val < 200:
            for col in range(1, 7):
                ws.cell(row=i, column=col).fill = PatternFill(start_color='E6F3FF', end_color='E6F3FF', fill_type='solid')
        else:
            for col in range(1, 7):
                ws.cell(row=i, column=col).fill = PatternFill(start_color='FFE6E6', end_color='FFE6E6', fill_type='solid')

    # 调整列宽
    for col in range(1, 7):
        ws.column_dimensions[chr(64 + col)].width = 15

    print(f"金额分析导出完成")

def main():
    """主函数"""
    print("开始基于member_id的数据匹配合并...")

    # 1. 加载并合并数据
    df_merged, df_withdraw_filtered, df_complete = load_and_merge_data()

    # 2. 分析合并后的数据
    df_merged = analyze_merged_data(df_merged)

    # 3. 识别团伙
    group_addresses, group_transactions = identify_groups_in_merged_data(df_merged)

    # 4. 创建综合Excel报告
    filename = create_comprehensive_excel_report(df_merged, df_complete, group_addresses)

    # 5. 保存完整合并数据为CSV
    csv_filename = '基于member_id匹配的完整数据.csv'
    df_merged.to_csv(csv_filename, index=False, encoding='utf-8')
    print(f"完整合并数据已保存为CSV: {csv_filename}")

    print(f"\n✅ 数据匹配合并完成")
    print(f"生成文件:")
    print(f"- {filename}: 综合Excel分析报告")
    print(f"- {csv_filename}: 完整合并数据CSV文件")

    print(f"\n📊 最终统计:")
    print(f"- 原始提币数据: 238,309 条")
    print(f"- 有效用户数据: {len(df_complete):,} 个")
    print(f"- 匹配成功数据: {len(df_merged):,} 条")
    print(f"- 数据保留率: {len(df_merged)/238309*100:.2f}%")
    print(f"- 发现团伙地址: {len(group_addresses):,} 个")

if __name__ == "__main__":
    main()
