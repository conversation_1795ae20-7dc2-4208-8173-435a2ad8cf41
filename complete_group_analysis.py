#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整团伙分析 - 导出所有数据，不做任何截取
"""

import pandas as pd
import numpy as np
from collections import Counter
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment
import warnings
warnings.filterwarnings('ignore')

def analyze_all_groups():
    """分析所有团伙，获取完整数据"""
    print("=== 完整团伙分析 ===")
    
    # 加载数据
    df = pd.read_csv('提币地址.csv')
    df['dt'] = pd.to_datetime(df['dt'], format='%Y%m%d')
    
    print(f"总交易记录: {len(df):,}")
    print(f"唯一账户数: {df['member_id'].nunique():,}")
    print(f"唯一地址数: {df['address'].nunique():,}")
    
    # 识别所有团伙地址（3人以上共享）
    print("\n正在识别所有团伙地址...")
    address_user_counts = df.groupby('address')['member_id'].nunique()
    group_addresses = address_user_counts[address_user_counts >= 3].index.tolist()
    
    print(f"团伙地址数量: {len(group_addresses):,}")
    
    # 获取所有团伙的完整信息
    all_groups = []
    
    print("正在分析每个团伙的详细信息...")
    for i, address in enumerate(group_addresses):
        if (i + 1) % 500 == 0:
            print(f"已处理 {i+1}/{len(group_addresses)} 个团伙地址...")
        
        # 获取该地址的所有数据
        group_data = df[df['address'] == address].copy()
        members = group_data['member_id'].unique()
        
        # 基本统计
        group_info = {
            'group_id': f'G{i+1:04d}',
            'address': address,
            'member_count': len(members),
            'total_transactions': len(group_data),
            'currencies': list(group_data['currency'].unique()),
            'currency_count': group_data['currency'].nunique(),
            'date_start': group_data['dt'].min(),
            'date_end': group_data['dt'].max(),
            'active_days': (group_data['dt'].max() - group_data['dt'].min()).days + 1
        }
        
        # 金额统计
        amount_data = group_data.dropna(subset=['currency_usdt_amount'])
        if len(amount_data) > 0:
            group_info.update({
                'total_amount': amount_data['currency_usdt_amount'].sum(),
                'avg_amount': amount_data['currency_usdt_amount'].mean(),
                'median_amount': amount_data['currency_usdt_amount'].median(),
                'max_amount': amount_data['currency_usdt_amount'].max(),
                'min_amount': amount_data['currency_usdt_amount'].min(),
                'transactions_with_amount': len(amount_data)
            })
            
            # 金额分类统计
            below_200 = amount_data[amount_data['currency_usdt_amount'] < 200]
            above_200 = amount_data[amount_data['currency_usdt_amount'] >= 200]
            
            group_info.update({
                'below_200_count': len(below_200),
                'below_200_amount': below_200['currency_usdt_amount'].sum(),
                'above_200_count': len(above_200),
                'above_200_amount': above_200['currency_usdt_amount'].sum(),
                'below_200_ratio': len(below_200) / len(amount_data) if len(amount_data) > 0 else 0
            })
        else:
            # 无金额数据的情况
            for key in ['total_amount', 'avg_amount', 'median_amount', 'max_amount', 'min_amount', 
                       'transactions_with_amount', 'below_200_count', 'below_200_amount', 
                       'above_200_count', 'above_200_amount', 'below_200_ratio']:
                group_info[key] = 0
        
        # 成员使用频次统计
        member_usage = group_data.groupby('member_id').size()
        group_info.update({
            'max_usage_by_member': member_usage.max(),
            'min_usage_by_member': member_usage.min(),
            'avg_usage_by_member': member_usage.mean(),
            'members_with_high_usage': len(member_usage[member_usage > 5]),
            'members_with_super_high_usage': len(member_usage[member_usage > 10])
        })
        
        # 成员列表
        group_info['members'] = list(members)
        
        all_groups.append(group_info)
    
    print(f"完成所有 {len(all_groups)} 个团伙的分析")
    
    # 按成员数量排序
    all_groups.sort(key=lambda x: x['member_count'], reverse=True)
    
    return all_groups, df

def create_complete_excel_report(all_groups, df):
    """创建包含所有数据的完整Excel报告"""
    print("\n=== 生成完整Excel报告（包含所有数据） ===")
    
    wb = openpyxl.Workbook()
    wb.remove(wb.active)
    
    # 1. 团伙汇总表 - 所有团伙
    create_all_groups_summary_sheet(wb, all_groups)
    
    # 2. 所有成员详情表
    create_all_members_detail_sheet(wb, all_groups)
    
    # 3. 所有团伙地址使用频次
    create_all_usage_frequency_sheet(wb, all_groups, df)
    
    # 4. 团伙规模分析
    create_group_size_analysis_sheet(wb, all_groups)
    
    # 5. 金额行为完整分析
    create_complete_amount_analysis_sheet(wb, all_groups)
    
    # 6. 币种使用完整分析
    create_complete_currency_analysis_sheet(wb, all_groups)
    
    # 7. 时间活动完整分析
    create_complete_time_analysis_sheet(wb, all_groups)
    
    filename = '完整团伙分析报告_所有数据.xlsx'
    wb.save(filename)
    print(f"完整Excel报告已保存: {filename}")
    
    return filename

def create_all_groups_summary_sheet(wb, all_groups):
    """创建所有团伙汇总工作表"""
    ws = wb.create_sheet("所有团伙汇总")
    
    # 标题
    ws['A1'] = f'所有团伙汇总信息（共{len(all_groups)}个团伙）'
    ws['A1'].font = Font(size=16, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center')
    ws.merge_cells('A1:P1')
    
    # 表头
    headers = ['团伙ID', '团伙地址', '成员数', '总交易数', '币种数', '主要币种', '活动天数', 
               '开始日期', '结束日期', '总金额(USDT)', '平均金额(USDT)', '200U以下交易数', 
               '200U以上交易数', '最高单人使用次数', '高频成员数(>5次)', '超高频成员数(>10次)']
    
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=3, column=col, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')
    
    # 填充所有团伙数据
    print(f"正在导出 {len(all_groups)} 个团伙的汇总数据...")
    
    for i, group in enumerate(all_groups, 4):
        ws.cell(row=i, column=1, value=group['group_id'])
        ws.cell(row=i, column=2, value=group['address'])
        ws.cell(row=i, column=3, value=group['member_count'])
        ws.cell(row=i, column=4, value=group['total_transactions'])
        ws.cell(row=i, column=5, value=group['currency_count'])
        ws.cell(row=i, column=6, value=', '.join(group['currencies'][:5]) + ('...' if len(group['currencies']) > 5 else ''))
        ws.cell(row=i, column=7, value=group['active_days'])
        ws.cell(row=i, column=8, value=group['date_start'].strftime('%Y-%m-%d'))
        ws.cell(row=i, column=9, value=group['date_end'].strftime('%Y-%m-%d'))
        ws.cell(row=i, column=10, value=round(group['total_amount'], 2) if group['total_amount'] > 0 else 0)
        ws.cell(row=i, column=11, value=round(group['avg_amount'], 2) if group['avg_amount'] > 0 else 0)
        ws.cell(row=i, column=12, value=group['below_200_count'])
        ws.cell(row=i, column=13, value=group['above_200_count'])
        ws.cell(row=i, column=14, value=group['max_usage_by_member'])
        ws.cell(row=i, column=15, value=group['members_with_high_usage'])
        ws.cell(row=i, column=16, value=group['members_with_super_high_usage'])
        
        # 根据团伙规模着色
        if group['member_count'] >= 100:
            for col in range(1, 17):
                ws.cell(row=i, column=col).fill = PatternFill(start_color='FFE6E6', end_color='FFE6E6', fill_type='solid')
        elif group['member_count'] >= 50:
            for col in range(1, 17):
                ws.cell(row=i, column=col).fill = PatternFill(start_color='FFF2E6', end_color='FFF2E6', fill_type='solid')
    
    # 调整列宽
    for col in range(1, 17):
        ws.column_dimensions[chr(64 + col)].width = 12
    
    print(f"团伙汇总数据导出完成")

def create_all_members_detail_sheet(wb, all_groups):
    """创建所有成员详情工作表"""
    ws = wb.create_sheet("所有成员详情")
    
    # 计算总成员数
    total_members = sum(len(group['members']) for group in all_groups)
    
    # 标题
    ws['A1'] = f'所有团伙成员详情（共{total_members}个成员）'
    ws['A1'].font = Font(size=16, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center')
    ws.merge_cells('A1:H1')
    
    # 表头
    headers = ['团伙ID', '团伙地址', '团伙规模', '成员ID', '成员在团伙中排序', '团伙总交易数', '团伙总金额(USDT)', '团伙主要币种']
    
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=3, column=col, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')
    
    # 填充所有成员数据
    print(f"正在导出 {total_members} 个成员的详细数据...")
    
    row = 4
    for group in all_groups:
        for member_idx, member_id in enumerate(group['members'], 1):
            ws.cell(row=row, column=1, value=group['group_id'])
            ws.cell(row=row, column=2, value=group['address'])
            ws.cell(row=row, column=3, value=group['member_count'])
            ws.cell(row=row, column=4, value=member_id)
            ws.cell(row=row, column=5, value=member_idx)
            ws.cell(row=row, column=6, value=group['total_transactions'])
            ws.cell(row=row, column=7, value=round(group['total_amount'], 2) if group['total_amount'] > 0 else 0)
            ws.cell(row=row, column=8, value=', '.join(group['currencies'][:3]))
            
            # 根据团伙规模着色
            if group['member_count'] >= 100:
                for col in range(1, 9):
                    ws.cell(row=row, column=col).fill = PatternFill(start_color='FFE6E6', end_color='FFE6E6', fill_type='solid')
            elif group['member_count'] >= 50:
                for col in range(1, 9):
                    ws.cell(row=row, column=col).fill = PatternFill(start_color='FFF2E6', end_color='FFF2E6', fill_type='solid')
            
            row += 1
    
    # 调整列宽
    for col in range(1, 9):
        ws.column_dimensions[chr(64 + col)].width = 15
    
    print(f"成员详情数据导出完成")

def create_all_usage_frequency_sheet(wb, all_groups, df):
    """创建所有团伙地址使用频次工作表"""
    ws = wb.create_sheet("所有地址使用频次")
    
    # 计算所有团伙地址的使用频次
    print("正在计算所有团伙地址的使用频次...")
    
    group_addresses = [group['address'] for group in all_groups]
    group_transactions = df[df['address'].isin(group_addresses)]
    group_usage = group_transactions.groupby(['member_id', 'address']).size().reset_index(name='usage_count')
    
    # 添加团伙信息
    address_to_group = {group['address']: group for group in all_groups}
    group_usage['group_id'] = group_usage['address'].map(lambda x: address_to_group[x]['group_id'])
    group_usage['group_size'] = group_usage['address'].map(lambda x: address_to_group[x]['member_count'])
    
    # 标题
    ws['A1'] = f'所有团伙地址使用频次（共{len(group_usage)}个组合）'
    ws['A1'].font = Font(size=16, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center')
    ws.merge_cells('A1:H1')
    
    # 表头
    headers = ['团伙ID', '团伙地址', '团伙规模', '用户ID', '使用次数', '风险等级', '地址类型', '备注']
    
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=3, column=col, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')
    
    # 按使用次数排序
    group_usage = group_usage.sort_values('usage_count', ascending=False)
    
    # 填充所有使用频次数据
    print(f"正在导出 {len(group_usage)} 个使用频次记录...")
    
    for i, (_, row) in enumerate(group_usage.iterrows(), 4):
        usage_count = row['usage_count']
        group_size = row['group_size']
        
        # 风险等级评估
        if usage_count > 20:
            risk_level = '极高风险'
            risk_color = 'FF9999'
        elif usage_count > 10:
            risk_level = '高风险'
            risk_color = 'FFE6E6'
        elif usage_count > 5:
            risk_level = '中风险'
            risk_color = 'FFF2E6'
        elif usage_count > 3:
            risk_level = '低风险'
            risk_color = 'F0F8FF'
        else:
            risk_level = '正常'
            risk_color = None
        
        # 地址类型
        if group_size > 100:
            address_type = '超大型团伙'
        elif group_size > 50:
            address_type = '大型团伙'
        elif group_size > 10:
            address_type = '中型团伙'
        else:
            address_type = '小型团伙'
        
        # 备注
        notes = []
        if usage_count > 50:
            notes.append('极端高频')
        elif usage_count > 20:
            notes.append('超高频')
        elif usage_count > 10:
            notes.append('高频')
        if group_size > 100:
            notes.append('超大团伙')
        
        ws.cell(row=i, column=1, value=row['group_id'])
        ws.cell(row=i, column=2, value=row['address'])
        ws.cell(row=i, column=3, value=group_size)
        ws.cell(row=i, column=4, value=row['member_id'])
        ws.cell(row=i, column=5, value=usage_count)
        ws.cell(row=i, column=6, value=risk_level)
        ws.cell(row=i, column=7, value=address_type)
        ws.cell(row=i, column=8, value=', '.join(notes) if notes else '正常')
        
        # 根据风险等级着色
        if risk_color:
            for col in range(1, 9):
                ws.cell(row=i, column=col).fill = PatternFill(start_color=risk_color, end_color=risk_color, fill_type='solid')
    
    # 调整列宽
    for col in range(1, 9):
        ws.column_dimensions[chr(64 + col)].width = 15
    
    print(f"使用频次数据导出完成")

def create_group_size_analysis_sheet(wb, all_groups):
    """创建团伙规模分析工作表"""
    ws = wb.create_sheet("团伙规模分析")

    # 标题
    ws['A1'] = '团伙规模详细分析'
    ws['A1'].font = Font(size=16, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center')
    ws.merge_cells('A1:F1')

    # 按规模分类统计
    size_ranges = [
        ('3人团伙', 3, 3),
        ('4-5人团伙', 4, 5),
        ('6-10人团伙', 6, 10),
        ('11-20人团伙', 11, 20),
        ('21-50人团伙', 21, 50),
        ('51-100人团伙', 51, 100),
        ('101-200人团伙', 101, 200),
        ('201-500人团伙', 201, 500),
        ('500人以上团伙', 501, float('inf'))
    ]

    ws['A3'] = '团伙规模分布统计'
    ws['A3'].font = Font(size=14, bold=True)

    headers = ['规模范围', '团伙数量', '占比', '总成员数', '总交易数', '平均交易数']
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=5, column=col, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')

    for i, (size_name, min_size, max_size) in enumerate(size_ranges, 6):
        if max_size == float('inf'):
            size_groups = [g for g in all_groups if g['member_count'] >= min_size]
        else:
            size_groups = [g for g in all_groups if min_size <= g['member_count'] <= max_size]

        if size_groups:
            total_members = sum(g['member_count'] for g in size_groups)
            total_transactions = sum(g['total_transactions'] for g in size_groups)
            avg_transactions = total_transactions / len(size_groups)

            ws.cell(row=i, column=1, value=size_name)
            ws.cell(row=i, column=2, value=len(size_groups))
            ws.cell(row=i, column=3, value=f"{len(size_groups)/len(all_groups)*100:.1f}%")
            ws.cell(row=i, column=4, value=total_members)
            ws.cell(row=i, column=5, value=total_transactions)
            ws.cell(row=i, column=6, value=f"{avg_transactions:.1f}")

            # 大型团伙着色
            if min_size >= 100:
                for col in range(1, 7):
                    ws.cell(row=i, column=col).fill = PatternFill(start_color='FFE6E6', end_color='FFE6E6', fill_type='solid')
        else:
            ws.cell(row=i, column=1, value=size_name)
            for col in range(2, 7):
                ws.cell(row=i, column=col, value=0)

    # 调整列宽
    for col in range(1, 7):
        ws.column_dimensions[chr(64 + col)].width = 15

def create_complete_amount_analysis_sheet(wb, all_groups):
    """创建完整金额分析工作表"""
    ws = wb.create_sheet("完整金额分析")

    # 标题
    ws['A1'] = '所有团伙金额分析'
    ws['A1'].font = Font(size=16, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center')
    ws.merge_cells('A1:L1')

    # 表头
    headers = ['团伙ID', '团伙地址', '成员数', '总金额(USDT)', '平均金额(USDT)', '中位数金额(USDT)',
               '最大金额(USDT)', '最小金额(USDT)', '200U以下交易数', '200U以上交易数', '小额占比', '金额行为类型']

    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=3, column=col, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')

    # 填充所有团伙的金额数据
    print(f"正在导出所有团伙的金额分析数据...")

    for i, group in enumerate(all_groups, 4):
        # 金额行为类型
        if group['below_200_ratio'] > 0.8:
            amount_type = '小额主导'
        elif group['below_200_ratio'] < 0.2:
            amount_type = '大额主导'
        elif group['total_amount'] == 0:
            amount_type = '无金额数据'
        else:
            amount_type = '混合型'

        ws.cell(row=i, column=1, value=group['group_id'])
        ws.cell(row=i, column=2, value=group['address'])
        ws.cell(row=i, column=3, value=group['member_count'])
        ws.cell(row=i, column=4, value=round(group['total_amount'], 2))
        ws.cell(row=i, column=5, value=round(group['avg_amount'], 2))
        ws.cell(row=i, column=6, value=round(group['median_amount'], 2))
        ws.cell(row=i, column=7, value=round(group['max_amount'], 2))
        ws.cell(row=i, column=8, value=round(group['min_amount'], 2))
        ws.cell(row=i, column=9, value=group['below_200_count'])
        ws.cell(row=i, column=10, value=group['above_200_count'])
        ws.cell(row=i, column=11, value=f"{group['below_200_ratio']*100:.1f}%")
        ws.cell(row=i, column=12, value=amount_type)

        # 根据金额类型着色
        if amount_type == '大额主导':
            for col in range(1, 13):
                ws.cell(row=i, column=col).fill = PatternFill(start_color='FFE6E6', end_color='FFE6E6', fill_type='solid')
        elif amount_type == '小额主导':
            for col in range(1, 13):
                ws.cell(row=i, column=col).fill = PatternFill(start_color='E6F3FF', end_color='E6F3FF', fill_type='solid')

    # 调整列宽
    for col in range(1, 13):
        ws.column_dimensions[chr(64 + col)].width = 12

    print(f"金额分析数据导出完成")

def create_complete_currency_analysis_sheet(wb, all_groups):
    """创建完整币种分析工作表"""
    ws = wb.create_sheet("完整币种分析")

    # 标题
    ws['A1'] = '所有团伙币种分析'
    ws['A1'].font = Font(size=16, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center')
    ws.merge_cells('A1:H1')

    # 表头
    headers = ['团伙ID', '团伙地址', '成员数', '币种数量', '主要币种', '所有币种', '币种集中度', '币种类型']

    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=3, column=col, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')

    # 填充所有团伙的币种数据
    print(f"正在导出所有团伙的币种分析数据...")

    for i, group in enumerate(all_groups, 4):
        # 币种类型
        if group['currency_count'] == 1:
            currency_type = '单一币种'
        elif group['currency_count'] <= 3:
            currency_type = '少数币种'
        elif group['currency_count'] <= 10:
            currency_type = '多种币种'
        else:
            currency_type = '极多币种'

        # 币种集中度（假设按交易数量计算，这里简化为币种数量的倒数）
        concentration = 1.0 / group['currency_count'] if group['currency_count'] > 0 else 0

        ws.cell(row=i, column=1, value=group['group_id'])
        ws.cell(row=i, column=2, value=group['address'])
        ws.cell(row=i, column=3, value=group['member_count'])
        ws.cell(row=i, column=4, value=group['currency_count'])
        ws.cell(row=i, column=5, value=group['currencies'][0] if group['currencies'] else 'N/A')
        ws.cell(row=i, column=6, value=', '.join(group['currencies']))
        ws.cell(row=i, column=7, value=f"{concentration:.3f}")
        ws.cell(row=i, column=8, value=currency_type)

        # 根据币种类型着色
        if currency_type == '单一币种':
            for col in range(1, 9):
                ws.cell(row=i, column=col).fill = PatternFill(start_color='FFE6E6', end_color='FFE6E6', fill_type='solid')
        elif currency_type == '极多币种':
            for col in range(1, 9):
                ws.cell(row=i, column=col).fill = PatternFill(start_color='E6F3FF', end_color='E6F3FF', fill_type='solid')

    # 调整列宽
    for col in range(1, 9):
        ws.column_dimensions[chr(64 + col)].width = 15

    print(f"币种分析数据导出完成")

def create_complete_time_analysis_sheet(wb, all_groups):
    """创建完整时间分析工作表"""
    ws = wb.create_sheet("完整时间分析")

    # 标题
    ws['A1'] = '所有团伙时间分析'
    ws['A1'].font = Font(size=16, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center')
    ws.merge_cells('A1:J1')

    # 表头
    headers = ['团伙ID', '团伙地址', '成员数', '开始日期', '结束日期', '活动天数',
               '交易密度(笔/天)', '活动类型', '时间集中度', '风险等级']

    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=3, column=col, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')

    # 填充所有团伙的时间数据
    print(f"正在导出所有团伙的时间分析数据...")

    for i, group in enumerate(all_groups, 4):
        # 交易密度
        density = group['total_transactions'] / max(group['active_days'], 1)

        # 活动类型
        if group['active_days'] <= 7:
            activity_type = '短期活动'
        elif group['active_days'] <= 30:
            activity_type = '中短期活动'
        elif group['active_days'] <= 90:
            activity_type = '中期活动'
        else:
            activity_type = '长期活动'

        # 时间集中度（交易密度的归一化）
        if density > 10:
            concentration = '高集中'
        elif density > 5:
            concentration = '中集中'
        elif density > 1:
            concentration = '低集中'
        else:
            concentration = '分散'

        # 风险等级
        risk_score = 0
        if density > 10:
            risk_score += 30
        if group['active_days'] <= 7:
            risk_score += 20
        if group['member_count'] > 100:
            risk_score += 25
        if group['max_usage_by_member'] > 10:
            risk_score += 25

        if risk_score >= 70:
            risk_level = '高风险'
            risk_color = 'FFE6E6'
        elif risk_score >= 40:
            risk_level = '中风险'
            risk_color = 'FFF2E6'
        else:
            risk_level = '低风险'
            risk_color = None

        ws.cell(row=i, column=1, value=group['group_id'])
        ws.cell(row=i, column=2, value=group['address'])
        ws.cell(row=i, column=3, value=group['member_count'])
        ws.cell(row=i, column=4, value=group['date_start'].strftime('%Y-%m-%d'))
        ws.cell(row=i, column=5, value=group['date_end'].strftime('%Y-%m-%d'))
        ws.cell(row=i, column=6, value=group['active_days'])
        ws.cell(row=i, column=7, value=f"{density:.2f}")
        ws.cell(row=i, column=8, value=activity_type)
        ws.cell(row=i, column=9, value=concentration)
        ws.cell(row=i, column=10, value=risk_level)

        # 根据风险等级着色
        if risk_color:
            for col in range(1, 11):
                ws.cell(row=i, column=col).fill = PatternFill(start_color=risk_color, end_color=risk_color, fill_type='solid')

    # 调整列宽
    for col in range(1, 11):
        ws.column_dimensions[chr(64 + col)].width = 12

    print(f"时间分析数据导出完成")

def main():
    """主函数"""
    print("开始完整团伙分析（包含所有数据）...")

    # 1. 分析所有团伙
    all_groups, df = analyze_all_groups()

    # 2. 创建完整Excel报告
    filename = create_complete_excel_report(all_groups, df)

    # 3. 输出统计摘要
    print(f"\n=== 完整分析摘要 ===")
    print(f"团伙总数: {len(all_groups):,}")
    print(f"总成员数: {sum(len(group['members']) for group in all_groups):,}")
    print(f"总交易数: {sum(group['total_transactions'] for group in all_groups):,}")
    print(f"最大团伙规模: {max(group['member_count'] for group in all_groups)}")
    print(f"最小团伙规模: {min(group['member_count'] for group in all_groups)}")

    # 规模分布
    size_100_plus = len([g for g in all_groups if g['member_count'] >= 100])
    size_50_99 = len([g for g in all_groups if 50 <= g['member_count'] < 100])
    size_10_49 = len([g for g in all_groups if 10 <= g['member_count'] < 50])
    size_3_9 = len([g for g in all_groups if 3 <= g['member_count'] < 10])

    print(f"\n=== 规模分布 ===")
    print(f"100人以上团伙: {size_100_plus}")
    print(f"50-99人团伙: {size_50_99}")
    print(f"10-49人团伙: {size_10_49}")
    print(f"3-9人团伙: {size_3_9}")

    print(f"\n✅ 完整团伙分析完成: {filename}")
    print("包含7个工作表，所有数据完整导出:")
    print("1. 所有团伙汇总 - 全部团伙的基本信息")
    print("2. 所有成员详情 - 全部成员的详细信息")
    print("3. 所有地址使用频次 - 全部使用频次记录")
    print("4. 团伙规模分析 - 详细的规模分布")
    print("5. 完整金额分析 - 全部团伙的金额信息")
    print("6. 完整币种分析 - 全部团伙的币种信息")
    print("7. 完整时间分析 - 全部团伙的时间信息")

if __name__ == "__main__":
    main()
