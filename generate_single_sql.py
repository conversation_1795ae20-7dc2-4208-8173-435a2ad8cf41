#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成单条SQL查询语句，包含所有member_id
"""

import pandas as pd

def generate_single_sql(excel_file_path):
    """
    生成包含所有member_id的单条SQL查询
    """
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_file_path)
        member_ids = df.iloc[:, 0].dropna().unique().tolist()
        
        print(f"总共找到 {len(member_ids)} 个唯一的member_id")
        
        # 构建IN子句，每行4个member_id以便阅读
        in_clause_parts = []
        for i in range(0, len(member_ids), 4):
            batch = member_ids[i:i+4]
            line = "    " + ", ".join([f"'{mid}'" for mid in batch])
            in_clause_parts.append(line)
        
        in_clause = ",\n".join(in_clause_parts)
        
        # 构建完整的SQL语句
        sql = f"""-- 单条SQL查询，包含所有 {len(member_ids)} 个member_id
SELECT DISTINCT 
    member_id,
    digital_id,
    last_login_country,
    first_operation_country,
    kyc_country,
    member_country,
    standard_language,
    origin
FROM dw_dim.dim_hierarchy_t_ourbit_member_a_daily
WHERE dt BETWEEN '20250708' AND '20250807'
  AND member_id IN (
{in_clause}
  );"""
        
        return sql
        
    except Exception as e:
        print(f"处理文件时出错: {e}")
        return ""

def save_single_sql_to_file(sql_query, output_file='single_member_query.sql'):
    """
    将单条SQL查询保存到文件
    """
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(sql_query)
        
        print(f"单条SQL查询已保存到文件: {output_file}")
        
    except Exception as e:
        print(f"保存文件时出错: {e}")

if __name__ == "__main__":
    excel_file = "地址数据.xlsx"
    
    # 生成单条SQL查询
    single_query = generate_single_sql(excel_file)
    
    if single_query:
        # 保存到文件
        save_single_sql_to_file(single_query)
        
        # 显示查询的开头和结尾部分
        print("\n" + "="*50)
        print("SQL查询开头部分:")
        print("="*50)
        lines = single_query.split('\n')
        for line in lines[:20]:
            print(line)
        print("...")
        print("SQL查询结尾部分:")
        print("="*50)
        for line in lines[-10:]:
            print(line)
        
        print(f"\n完整的单条SQL查询已保存到 single_member_query.sql 文件中")
        print(f"查询包含 {single_query.count(',')} 个member_id")
    else:
        print("未能生成SQL查询")
