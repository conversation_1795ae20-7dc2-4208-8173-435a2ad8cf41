#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据匹配分析脚本
根据member_id匹配提币地址.csv和合并后的完整数据.xlsx
"""

import pandas as pd
import numpy as np
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment
import warnings
warnings.filterwarnings('ignore')

def load_and_analyze_data():
    """加载和分析两个数据源"""
    print("=== 数据加载和基本分析 ===")
    
    # 加载提币地址数据
    print("正在加载提币地址.csv...")
    df_withdrawal = pd.read_csv('提币地址.csv')
    print(f"提币地址数据: {len(df_withdrawal):,} 行")
    print(f"提币地址列名: {list(df_withdrawal.columns)}")
    print(f"提币地址唯一member_id: {df_withdrawal['member_id'].nunique():,} 个")
    
    # 加载合并后的完整数据
    print("\n正在加载合并后的完整数据.xlsx...")
    df_complete = pd.read_excel('合并后的完整数据.xlsx')
    print(f"完整数据: {len(df_complete):,} 行")
    print(f"完整数据列名: {list(df_complete.columns)}")
    print(f"完整数据唯一member_id: {df_complete['member_id'].nunique():,} 个")
    
    return df_withdrawal, df_complete

def perform_matching_analysis(df_withdrawal, df_complete):
    """执行匹配分析"""
    print("\n=== 执行member_id匹配分析 ===")
    
    # 获取两个数据源的member_id集合
    withdrawal_members = set(df_withdrawal['member_id'].unique())
    complete_members = set(df_complete['member_id'].unique())
    
    print(f"提币地址数据中的唯一member_id: {len(withdrawal_members):,}")
    print(f"完整数据中的唯一member_id: {len(complete_members):,}")
    
    # 计算匹配情况
    matched_members = withdrawal_members & complete_members  # 交集
    withdrawal_only = withdrawal_members - complete_members  # 只在提币数据中
    complete_only = complete_members - withdrawal_members    # 只在完整数据中
    
    print(f"\n=== 匹配结果 ===")
    print(f"匹配的member_id: {len(matched_members):,} 个")
    print(f"只在提币数据中的member_id: {len(withdrawal_only):,} 个")
    print(f"只在完整数据中的member_id: {len(complete_only):,} 个")
    
    # 计算匹配率
    withdrawal_match_rate = len(matched_members) / len(withdrawal_members) * 100
    complete_match_rate = len(matched_members) / len(complete_members) * 100
    
    print(f"\n=== 匹配率 ===")
    print(f"提币数据的匹配率: {withdrawal_match_rate:.2f}%")
    print(f"完整数据的匹配率: {complete_match_rate:.2f}%")
    
    return {
        'matched_members': matched_members,
        'withdrawal_only': withdrawal_only,
        'complete_only': complete_only,
        'withdrawal_match_rate': withdrawal_match_rate,
        'complete_match_rate': complete_match_rate
    }

def create_merged_dataset(df_withdrawal, df_complete, match_results):
    """创建合并后的数据集"""
    print("\n=== 创建合并数据集 ===")
    
    # 只保留匹配的member_id的数据
    matched_withdrawal = df_withdrawal[df_withdrawal['member_id'].isin(match_results['matched_members'])]
    matched_complete = df_complete[df_complete['member_id'].isin(match_results['matched_members'])]
    
    print(f"匹配的提币记录: {len(matched_withdrawal):,} 条")
    print(f"匹配的完整数据记录: {len(matched_complete):,} 条")
    
    # 合并数据 - 左连接，以提币数据为主
    merged_data = pd.merge(
        matched_withdrawal, 
        matched_complete, 
        on='member_id', 
        how='left',
        suffixes=('_withdrawal', '_complete')
    )
    
    print(f"合并后数据: {len(merged_data):,} 条")
    
    return merged_data, matched_withdrawal, matched_complete

def analyze_merged_data(merged_data):
    """分析合并后的数据"""
    print("\n=== 合并数据分析 ===")
    
    # 基本统计
    print(f"合并后总记录数: {len(merged_data):,}")
    print(f"涉及用户数: {merged_data['member_id'].nunique():,}")
    
    # 分析提币行为与用户属性的关系
    analysis_results = {}
    
    # 按国家分析
    if 'member_country' in merged_data.columns:
        country_stats = merged_data.groupby('member_country').agg({
            'member_id': 'nunique',
            'currency_usdt_amount': ['count', 'sum', 'mean']
        }).round(2)
        country_stats.columns = ['用户数', '交易次数', '总金额', '平均金额']
        analysis_results['country_stats'] = country_stats.fillna(0)
        print(f"\n按国家统计 (前10):")
        print(country_stats.nlargest(10, '用户数'))
    
    # 按KYC状态分析
    if 'kyc_country' in merged_data.columns:
        kyc_stats = merged_data.groupby(merged_data['kyc_country'].notna()).agg({
            'member_id': 'nunique',
            'currency_usdt_amount': ['count', 'sum', 'mean']
        }).round(2)
        kyc_stats.columns = ['用户数', '交易次数', '总金额', '平均金额']
        kyc_stats.index = ['未KYC', '已KYC']
        analysis_results['kyc_stats'] = kyc_stats.fillna(0)
        print(f"\n按KYC状态统计:")
        print(kyc_stats)
    
    # 按BD分析
    if 'top_kol_bd_name' in merged_data.columns:
        bd_stats = merged_data.groupby(merged_data['top_kol_bd_name'].notna()).agg({
            'member_id': 'nunique',
            'currency_usdt_amount': ['count', 'sum', 'mean']
        }).round(2)
        bd_stats.columns = ['用户数', '交易次数', '总金额', '平均金额']
        bd_stats.index = ['无BD', '有BD']
        analysis_results['bd_stats'] = bd_stats.fillna(0)
        print(f"\n按BD状态统计:")
        print(bd_stats)
    
    return analysis_results

def create_comprehensive_excel_report(df_withdrawal, df_complete, merged_data, match_results, analysis_results):
    """创建综合Excel报告"""
    print("\n=== 生成综合Excel报告 ===")
    
    wb = openpyxl.Workbook()
    wb.remove(wb.active)
    
    # 1. 匹配概览
    create_matching_overview_sheet(wb, match_results, df_withdrawal, df_complete)
    
    # 2. 合并后的完整数据
    create_merged_data_sheet(wb, merged_data)
    
    # 3. 未匹配的提币用户
    create_unmatched_withdrawal_sheet(wb, df_withdrawal, match_results)
    
    # 4. 未匹配的完整数据用户
    create_unmatched_complete_sheet(wb, df_complete, match_results)
    
    # 5. 数据分析结果
    create_analysis_results_sheet(wb, analysis_results)
    
    # 6. 匹配统计详情
    create_detailed_stats_sheet(wb, merged_data, match_results)
    
    filename = '数据匹配分析报告.xlsx'
    wb.save(filename)
    print(f"综合报告已保存: {filename}")
    
    return filename

def create_matching_overview_sheet(wb, match_results, df_withdrawal, df_complete):
    """创建匹配概览工作表"""
    ws = wb.create_sheet("匹配概览")
    
    # 标题
    ws['A1'] = '数据匹配分析概览'
    ws['A1'].font = Font(size=16, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center')
    ws.merge_cells('A1:D1')
    
    # 基本统计
    ws['A3'] = '基本数据统计'
    ws['A3'].font = Font(size=14, bold=True)
    
    basic_stats = [
        ['数据源', '总记录数', '唯一member_id', '说明'],
        ['提币地址数据', f"{len(df_withdrawal):,}", f"{df_withdrawal['member_id'].nunique():,}", '提币交易记录'],
        ['完整数据', f"{len(df_complete):,}", f"{df_complete['member_id'].nunique():,}", '用户基本信息'],
        ['', '', '', ''],
        ['匹配结果统计', '', '', ''],
        ['匹配的member_id', f"{len(match_results['matched_members']):,}", f"{len(match_results['matched_members']) / max(df_withdrawal['member_id'].nunique(), df_complete['member_id'].nunique()) * 100:.1f}%", '两个数据源都有的用户'],
        ['只在提币数据中', f"{len(match_results['withdrawal_only']):,}", f"{len(match_results['withdrawal_only']) / df_withdrawal['member_id'].nunique() * 100:.1f}%", '有提币记录但无基本信息'],
        ['只在完整数据中', f"{len(match_results['complete_only']):,}", f"{len(match_results['complete_only']) / df_complete['member_id'].nunique() * 100:.1f}%", '有基本信息但无提币记录'],
        ['', '', '', ''],
        ['匹配率分析', '', '', ''],
        ['提币数据匹配率', f"{match_results['withdrawal_match_rate']:.2f}%", '', '提币用户中有基本信息的比例'],
        ['完整数据匹配率', f"{match_results['complete_match_rate']:.2f}%", '', '用户中有提币记录的比例']
    ]
    
    for i, row_data in enumerate(basic_stats, 5):
        for j, value in enumerate(row_data, 1):
            cell = ws.cell(row=i, column=j, value=value)
            if i == 5 or i == 9 or i == 13:  # 表头行
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')
            elif '匹配的member_id' in str(value):  # 匹配行
                cell.fill = PatternFill(start_color='E6F3FF', end_color='E6F3FF', fill_type='solid')
    
    # 调整列宽
    for col in range(1, 5):
        ws.column_dimensions[chr(64 + col)].width = 20

def create_merged_data_sheet(wb, merged_data):
    """创建合并数据工作表"""
    ws = wb.create_sheet("合并后完整数据")
    
    # 标题
    ws['A1'] = f'合并后的完整数据（{len(merged_data):,}条记录）'
    ws['A1'].font = Font(size=16, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center')
    ws.merge_cells('A1:P1')
    
    # 选择关键列进行展示
    key_columns = ['member_id', 'currency', 'address', 'currency_usdt_amount', 'dt', 
                   'member_country', 'kyc_country', 'top_kol_bd_name', 'origin']
    
    # 过滤存在的列
    available_columns = [col for col in key_columns if col in merged_data.columns]
    
    # 表头
    for col, header in enumerate(available_columns, 1):
        cell = ws.cell(row=3, column=col, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')
    
    # 填充数据（前10000条）
    print(f"正在导出合并数据的前10,000条记录...")
    sample_data = merged_data[available_columns].head(10000)
    
    for i, (_, row) in enumerate(sample_data.iterrows(), 4):
        for j, col in enumerate(available_columns, 1):
            value = row[col]
            if pd.isna(value):
                value = 'N/A'
            elif isinstance(value, (int, float)):
                value = f"{value:.2f}" if isinstance(value, float) else str(value)
            else:
                value = str(value)
            ws.cell(row=i, column=j, value=value)
    
    # 调整列宽
    for col in range(1, len(available_columns) + 1):
        ws.column_dimensions[chr(64 + col)].width = 15

def create_unmatched_withdrawal_sheet(wb, df_withdrawal, match_results):
    """创建未匹配的提币用户工作表"""
    ws = wb.create_sheet("未匹配提币用户")
    
    # 标题
    ws['A1'] = f'未匹配的提币用户（{len(match_results["withdrawal_only"]):,}个用户）'
    ws['A1'].font = Font(size=16, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center')
    ws.merge_cells('A1:F1')
    
    # 获取未匹配的提币数据
    unmatched_withdrawal = df_withdrawal[df_withdrawal['member_id'].isin(match_results['withdrawal_only'])]
    
    # 按用户统计
    user_stats = unmatched_withdrawal.groupby('member_id').agg({
        'currency_usdt_amount': ['count', 'sum', 'mean'],
        'currency': 'nunique',
        'address': 'nunique'
    }).round(2)
    user_stats.columns = ['交易次数', '总金额', '平均金额', '币种数', '地址数']
    user_stats = user_stats.fillna(0).reset_index()
    
    # 表头
    headers = ['member_id', '交易次数', '总金额(USDT)', '平均金额(USDT)', '使用币种数', '使用地址数']
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=3, column=col, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')
    
    # 填充数据（前5000个用户）
    print(f"正在导出未匹配提币用户的前5,000个...")
    sample_users = user_stats.head(5000)
    
    for i, (_, row) in enumerate(sample_users.iterrows(), 4):
        ws.cell(row=i, column=1, value=row['member_id'])
        ws.cell(row=i, column=2, value=int(row['交易次数']))
        ws.cell(row=i, column=3, value=f"{row['总金额']:.2f}")
        ws.cell(row=i, column=4, value=f"{row['平均金额']:.2f}")
        ws.cell(row=i, column=5, value=int(row['币种数']))
        ws.cell(row=i, column=6, value=int(row['地址数']))
    
    # 调整列宽
    for col in range(1, 7):
        ws.column_dimensions[chr(64 + col)].width = 15
