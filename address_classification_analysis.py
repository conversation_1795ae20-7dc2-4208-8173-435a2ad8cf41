#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
地址分类分析
重新定义：团伙地址 = 2个账户及以上使用过相同的提币地址
正常地址 = 只有1个账户使用的地址
"""

import pandas as pd
import numpy as np
from collections import Counter
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment
import warnings
warnings.filterwarnings('ignore')

def analyze_address_classification():
    """分析地址分类：正常地址 vs 团伙地址"""
    print("=== 地址分类分析 ===")
    print("团伙定义：2个账户及以上使用过相同的提币地址")
    print("正常地址：只有1个账户使用的地址")
    
    # 加载数据
    df = pd.read_csv('提币地址.csv')
    df['dt'] = pd.to_datetime(df['dt'], format='%Y%m%d')
    
    print(f"\n基础数据统计:")
    print(f"总交易记录: {len(df):,}")
    print(f"唯一账户数: {df['member_id'].nunique():,}")
    print(f"唯一地址数: {df['address'].nunique():,}")
    
    # 计算每个地址被多少个账户使用
    print("\n正在分析地址使用情况...")
    address_user_counts = df.groupby('address')['member_id'].nunique().reset_index()
    address_user_counts.columns = ['address', 'user_count']
    
    # 分类地址
    normal_addresses = address_user_counts[address_user_counts['user_count'] == 1]
    group_addresses = address_user_counts[address_user_counts['user_count'] >= 2]
    
    print(f"\n=== 地址分类结果 ===")
    print(f"正常地址数量: {len(normal_addresses):,} ({len(normal_addresses)/len(address_user_counts)*100:.1f}%)")
    print(f"团伙地址数量: {len(group_addresses):,} ({len(group_addresses)/len(address_user_counts)*100:.1f}%)")
    print(f"地址总数: {len(address_user_counts):,}")
    
    # 团伙地址的详细分布
    print(f"\n=== 团伙地址详细分布 ===")
    group_2_users = group_addresses[group_addresses['user_count'] == 2]
    group_3_users = group_addresses[group_addresses['user_count'] == 3]
    group_4_5_users = group_addresses[(group_addresses['user_count'] >= 4) & (group_addresses['user_count'] <= 5)]
    group_6_10_users = group_addresses[(group_addresses['user_count'] >= 6) & (group_addresses['user_count'] <= 10)]
    group_11_plus_users = group_addresses[group_addresses['user_count'] >= 11]
    
    print(f"2人团伙地址: {len(group_2_users):,} ({len(group_2_users)/len(group_addresses)*100:.1f}%)")
    print(f"3人团伙地址: {len(group_3_users):,} ({len(group_3_users)/len(group_addresses)*100:.1f}%)")
    print(f"4-5人团伙地址: {len(group_4_5_users):,} ({len(group_4_5_users)/len(group_addresses)*100:.1f}%)")
    print(f"6-10人团伙地址: {len(group_6_10_users):,} ({len(group_6_10_users)/len(group_addresses)*100:.1f}%)")
    print(f"11人以上团伙地址: {len(group_11_plus_users):,} ({len(group_11_plus_users)/len(group_addresses)*100:.1f}%)")
    
    return df, address_user_counts, normal_addresses, group_addresses

def analyze_usage_frequency_by_address_type(df, normal_addresses, group_addresses):
    """分析不同类型地址的使用频次"""
    print(f"\n=== 使用频次分析 ===")
    
    # 正常地址的使用频次分析
    normal_address_list = normal_addresses['address'].tolist()
    normal_transactions = df[df['address'].isin(normal_address_list)]
    normal_usage = normal_transactions.groupby(['member_id', 'address']).size().reset_index(name='usage_count')
    
    print(f"\n正常地址使用频次:")
    print(f"正常地址交易总数: {len(normal_transactions):,}")
    print(f"正常地址账户-地址组合数: {len(normal_usage):,}")
    
    normal_usage_1 = normal_usage[normal_usage['usage_count'] == 1]
    normal_usage_2_3 = normal_usage[(normal_usage['usage_count'] >= 2) & (normal_usage['usage_count'] <= 3)]
    normal_usage_3_plus = normal_usage[normal_usage['usage_count'] > 3]
    
    print(f"  仅使用1次: {len(normal_usage_1):,} ({len(normal_usage_1)/len(normal_usage)*100:.1f}%)")
    print(f"  使用2-3次: {len(normal_usage_2_3):,} ({len(normal_usage_2_3)/len(normal_usage)*100:.1f}%)")
    print(f"  使用3次以上: {len(normal_usage_3_plus):,} ({len(normal_usage_3_plus)/len(normal_usage)*100:.1f}%)")
    
    # 团伙地址的使用频次分析
    group_address_list = group_addresses['address'].tolist()
    group_transactions = df[df['address'].isin(group_address_list)]
    group_usage = group_transactions.groupby(['member_id', 'address']).size().reset_index(name='usage_count')
    
    print(f"\n团伙地址使用频次:")
    print(f"团伙地址交易总数: {len(group_transactions):,}")
    print(f"团伙地址账户-地址组合数: {len(group_usage):,}")
    
    group_usage_1 = group_usage[group_usage['usage_count'] == 1]
    group_usage_2_3 = group_usage[(group_usage['usage_count'] >= 2) & (group_usage['usage_count'] <= 3)]
    group_usage_3_plus = group_usage[group_usage['usage_count'] > 3]
    
    print(f"  仅使用1次: {len(group_usage_1):,} ({len(group_usage_1)/len(group_usage)*100:.1f}%)")
    print(f"  使用2-3次: {len(group_usage_2_3):,} ({len(group_usage_2_3)/len(group_usage)*100:.1f}%)")
    print(f"  使用3次以上: {len(group_usage_3_plus):,} ({len(group_usage_3_plus)/len(group_usage)*100:.1f}%)")
    
    return normal_usage, group_usage

def create_address_classification_excel(df, address_user_counts, normal_addresses, group_addresses, normal_usage, group_usage):
    """创建地址分类分析Excel报告"""
    print(f"\n=== 生成地址分类分析Excel报告 ===")
    
    wb = openpyxl.Workbook()
    wb.remove(wb.active)
    
    # 1. 地址分类概览
    create_classification_overview_sheet(wb, df, address_user_counts, normal_addresses, group_addresses)
    
    # 2. 正常地址详情
    create_normal_addresses_sheet(wb, normal_addresses, normal_usage, df)
    
    # 3. 团伙地址详情
    create_group_addresses_sheet(wb, group_addresses, group_usage, df)
    
    # 4. 使用频次对比分析
    create_usage_frequency_comparison_sheet(wb, normal_usage, group_usage)
    
    # 5. 团伙地址规模分析
    create_group_size_analysis_sheet(wb, group_addresses, df)
    
    # 6. 完整地址列表
    create_complete_address_list_sheet(wb, address_user_counts, df)
    
    filename = '地址分类分析报告_正常vs团伙.xlsx'
    wb.save(filename)
    print(f"Excel报告已保存: {filename}")
    
    return filename

def create_classification_overview_sheet(wb, df, address_user_counts, normal_addresses, group_addresses):
    """创建地址分类概览工作表"""
    ws = wb.create_sheet("地址分类概览")
    
    # 标题
    ws['A1'] = '地址分类分析概览'
    ws['A1'].font = Font(size=16, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center')
    ws.merge_cells('A1:D1')
    
    # 基本统计
    ws['A3'] = '基本统计'
    ws['A3'].font = Font(size=14, bold=True)
    
    basic_stats = [
        ['指标', '数值', '占比', '说明'],
        ['总交易记录', f"{len(df):,}", '100.0%', '所有提币交易'],
        ['唯一账户数', f"{df['member_id'].nunique():,}", '100.0%', '不重复的账户'],
        ['唯一地址数', f"{len(address_user_counts):,}", '100.0%', '不重复的提币地址'],
        ['', '', '', ''],
        ['地址分类', '', '', ''],
        ['正常地址', f"{len(normal_addresses):,}", f"{len(normal_addresses)/len(address_user_counts)*100:.1f}%", '只有1个账户使用'],
        ['团伙地址', f"{len(group_addresses):,}", f"{len(group_addresses)/len(address_user_counts)*100:.1f}%", '2个及以上账户使用'],
    ]
    
    for i, row_data in enumerate(basic_stats, 4):
        for j, value in enumerate(row_data, 1):
            cell = ws.cell(row=i, column=j, value=value)
            if i == 4 or i == 9:  # 表头行
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')
            elif '团伙地址' in str(value):  # 团伙地址行
                cell.fill = PatternFill(start_color='FFE6E6', end_color='FFE6E6', fill_type='solid')
    
    # 团伙地址规模分布
    ws['A14'] = '团伙地址规模分布'
    ws['A14'].font = Font(size=14, bold=True)
    
    size_ranges = [
        ('2人团伙', 2, 2),
        ('3人团伙', 3, 3),
        ('4-5人团伙', 4, 5),
        ('6-10人团伙', 6, 10),
        ('11-20人团伙', 11, 20),
        ('21-50人团伙', 21, 50),
        ('51-100人团伙', 51, 100),
        ('100人以上团伙', 101, float('inf'))
    ]
    
    size_headers = ['团伙规模', '地址数量', '占团伙地址比例', '占总地址比例']
    for col, header in enumerate(size_headers, 1):
        cell = ws.cell(row=16, column=col, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')
    
    for i, (size_name, min_size, max_size) in enumerate(size_ranges, 17):
        if max_size == float('inf'):
            size_addresses = group_addresses[group_addresses['user_count'] >= min_size]
        else:
            size_addresses = group_addresses[(group_addresses['user_count'] >= min_size) & (group_addresses['user_count'] <= max_size)]
        
        ws.cell(row=i, column=1, value=size_name)
        ws.cell(row=i, column=2, value=len(size_addresses))
        ws.cell(row=i, column=3, value=f"{len(size_addresses)/len(group_addresses)*100:.1f}%")
        ws.cell(row=i, column=4, value=f"{len(size_addresses)/len(address_user_counts)*100:.1f}%")
        
        # 大型团伙着色
        if min_size >= 50:
            for col in range(1, 5):
                ws.cell(row=i, column=col).fill = PatternFill(start_color='FFE6E6', end_color='FFE6E6', fill_type='solid')
    
    # 调整列宽
    for col in range(1, 5):
        ws.column_dimensions[chr(64 + col)].width = 20

def create_normal_addresses_sheet(wb, normal_addresses, normal_usage, df):
    """创建正常地址详情工作表"""
    ws = wb.create_sheet("正常地址详情")
    
    # 标题
    ws['A1'] = f'正常地址详情（共{len(normal_addresses):,}个地址）'
    ws['A1'].font = Font(size=16, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center')
    ws.merge_cells('A1:H1')
    
    # 使用频次统计
    ws['A3'] = '正常地址使用频次统计'
    ws['A3'].font = Font(size=14, bold=True)
    
    usage_1 = normal_usage[normal_usage['usage_count'] == 1]
    usage_2_3 = normal_usage[(normal_usage['usage_count'] >= 2) & (normal_usage['usage_count'] <= 3)]
    usage_3_plus = normal_usage[normal_usage['usage_count'] > 3]
    
    freq_stats = [
        ['使用频次', '组合数量', '占比', '说明'],
        ['仅使用1次', len(usage_1), f"{len(usage_1)/len(normal_usage)*100:.1f}%", '账户对地址只使用1次'],
        ['使用2-3次', len(usage_2_3), f"{len(usage_2_3)/len(normal_usage)*100:.1f}%", '账户对地址使用2-3次'],
        ['使用3次以上', len(usage_3_plus), f"{len(usage_3_plus)/len(normal_usage)*100:.1f}%", '账户对地址使用3次以上'],
        ['总计', len(normal_usage), '100.0%', '正常地址的所有使用记录']
    ]
    
    for i, row_data in enumerate(freq_stats, 5):
        for j, value in enumerate(row_data, 1):
            cell = ws.cell(row=i, column=j, value=value)
            if i == 5 or i == 9:  # 表头和总计行
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')
            elif '使用3次以上' in str(value):  # 高频使用行
                cell.fill = PatternFill(start_color='FFF2E6', end_color='FFF2E6', fill_type='solid')
    
    # 高频使用的正常地址详情
    if len(usage_3_plus) > 0:
        ws['A12'] = f'高频使用的正常地址（使用3次以上，共{len(usage_3_plus)}个）'
        ws['A12'].font = Font(size=14, bold=True)
        
        high_freq_headers = ['账户ID', '地址', '使用次数', '风险等级']
        for col, header in enumerate(high_freq_headers, 1):
            cell = ws.cell(row=14, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')
        
        # 按使用次数排序
        usage_3_plus_sorted = usage_3_plus.sort_values('usage_count', ascending=False)
        
        for i, (_, row) in enumerate(usage_3_plus_sorted.iterrows(), 15):
            usage_count = row['usage_count']
            
            # 风险等级
            if usage_count > 20:
                risk_level = '高风险'
                risk_color = 'FFE6E6'
            elif usage_count > 10:
                risk_level = '中风险'
                risk_color = 'FFF2E6'
            else:
                risk_level = '低风险'
                risk_color = None
            
            ws.cell(row=i, column=1, value=row['member_id'])
            ws.cell(row=i, column=2, value=row['address'][:30] + '...')
            ws.cell(row=i, column=3, value=usage_count)
            ws.cell(row=i, column=4, value=risk_level)
            
            # 根据风险等级着色
            if risk_color:
                for col in range(1, 5):
                    ws.cell(row=i, column=col).fill = PatternFill(start_color=risk_color, end_color=risk_color, fill_type='solid')
    else:
        ws['A12'] = '未发现高频使用的正常地址'
    
    # 调整列宽
    for col in range(1, 9):
        ws.column_dimensions[chr(64 + col)].width = 15

def create_group_addresses_sheet(wb, group_addresses, group_usage, df):
    """创建团伙地址详情工作表"""
    ws = wb.create_sheet("团伙地址详情")

    # 标题
    ws['A1'] = f'团伙地址详情（共{len(group_addresses):,}个地址）'
    ws['A1'].font = Font(size=16, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center')
    ws.merge_cells('A1:J1')

    # 使用频次统计
    ws['A3'] = '团伙地址使用频次统计'
    ws['A3'].font = Font(size=14, bold=True)

    usage_1 = group_usage[group_usage['usage_count'] == 1]
    usage_2_3 = group_usage[(group_usage['usage_count'] >= 2) & (group_usage['usage_count'] <= 3)]
    usage_3_plus = group_usage[group_usage['usage_count'] > 3]

    freq_stats = [
        ['使用频次', '组合数量', '占比', '说明'],
        ['仅使用1次', len(usage_1), f"{len(usage_1)/len(group_usage)*100:.1f}%", '账户对团伙地址只使用1次'],
        ['使用2-3次', len(usage_2_3), f"{len(usage_2_3)/len(group_usage)*100:.1f}%", '账户对团伙地址使用2-3次'],
        ['使用3次以上', len(usage_3_plus), f"{len(usage_3_plus)/len(group_usage)*100:.1f}%", '账户对团伙地址使用3次以上'],
        ['总计', len(group_usage), '100.0%', '团伙地址的所有使用记录']
    ]

    for i, row_data in enumerate(freq_stats, 5):
        for j, value in enumerate(row_data, 1):
            cell = ws.cell(row=i, column=j, value=value)
            if i == 5 or i == 9:  # 表头和总计行
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')
            elif '使用3次以上' in str(value):  # 高频使用行
                cell.fill = PatternFill(start_color='FFE6E6', end_color='FFE6E6', fill_type='solid')

    # 团伙地址详细列表（显示所有团伙地址）
    ws['A12'] = f'所有团伙地址列表（共{len(group_addresses):,}个）'
    ws['A12'].font = Font(size=14, bold=True)

    headers = ['团伙地址', '用户数', '总交易数', '平均每人交易数', '最高单人使用次数', '高频用户数(>5次)', '主要币种', '活动天数', '团伙类型']
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=14, column=col, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')

    # 按用户数排序，显示所有团伙地址
    group_addresses_sorted = group_addresses.sort_values('user_count', ascending=False)

    print(f"正在导出 {len(group_addresses)} 个团伙地址的详细信息...")

    for i, (_, row) in enumerate(group_addresses_sorted.iterrows(), 15):
        address = row['address']
        user_count = row['user_count']

        # 获取该地址的详细信息
        address_data = df[df['address'] == address]
        address_usage = group_usage[group_usage['address'] == address]

        total_transactions = len(address_data)
        avg_transactions_per_user = total_transactions / user_count
        max_usage = address_usage['usage_count'].max() if len(address_usage) > 0 else 0
        high_freq_users = len(address_usage[address_usage['usage_count'] > 5]) if len(address_usage) > 0 else 0
        main_currency = address_data['currency'].value_counts().index[0] if len(address_data) > 0 else 'N/A'
        active_days = (address_data['dt'].max() - address_data['dt'].min()).days + 1 if len(address_data) > 0 else 0

        # 团伙类型
        if user_count >= 100:
            group_type = '超大型团伙'
            type_color = 'FF9999'
        elif user_count >= 50:
            group_type = '大型团伙'
            type_color = 'FFE6E6'
        elif user_count >= 10:
            group_type = '中型团伙'
            type_color = 'FFF2E6'
        else:
            group_type = '小型团伙'
            type_color = None

        ws.cell(row=i, column=1, value=address[:30] + '...')
        ws.cell(row=i, column=2, value=user_count)
        ws.cell(row=i, column=3, value=total_transactions)
        ws.cell(row=i, column=4, value=f"{avg_transactions_per_user:.1f}")
        ws.cell(row=i, column=5, value=max_usage)
        ws.cell(row=i, column=6, value=high_freq_users)
        ws.cell(row=i, column=7, value=main_currency)
        ws.cell(row=i, column=8, value=active_days)
        ws.cell(row=i, column=9, value=group_type)

        # 根据团伙类型着色
        if type_color:
            for col in range(1, 10):
                ws.cell(row=i, column=col).fill = PatternFill(start_color=type_color, end_color=type_color, fill_type='solid')

    # 调整列宽
    for col in range(1, 10):
        ws.column_dimensions[chr(64 + col)].width = 12

    print(f"团伙地址详情导出完成")

def create_usage_frequency_comparison_sheet(wb, normal_usage, group_usage):
    """创建使用频次对比分析工作表"""
    ws = wb.create_sheet("使用频次对比")

    # 标题
    ws['A1'] = '正常地址 vs 团伙地址使用频次对比'
    ws['A1'].font = Font(size=16, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center')
    ws.merge_cells('A1:F1')

    # 对比统计表
    ws['A3'] = '使用频次对比统计'
    ws['A3'].font = Font(size=14, bold=True)

    comparison_headers = ['使用频次', '正常地址组合数', '正常地址占比', '团伙地址组合数', '团伙地址占比', '差异分析']
    for col, header in enumerate(comparison_headers, 1):
        cell = ws.cell(row=5, column=col, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')

    # 计算各频次的统计
    frequency_ranges = [
        ('仅使用1次', 1, 1),
        ('使用2-3次', 2, 3),
        ('使用4-5次', 4, 5),
        ('使用6-10次', 6, 10),
        ('使用11-20次', 11, 20),
        ('使用20次以上', 21, float('inf'))
    ]

    for i, (freq_name, min_freq, max_freq) in enumerate(frequency_ranges, 6):
        if max_freq == float('inf'):
            normal_count = len(normal_usage[normal_usage['usage_count'] >= min_freq])
            group_count = len(group_usage[group_usage['usage_count'] >= min_freq])
        else:
            normal_count = len(normal_usage[(normal_usage['usage_count'] >= min_freq) & (normal_usage['usage_count'] <= max_freq)])
            group_count = len(group_usage[(group_usage['usage_count'] >= min_freq) & (group_usage['usage_count'] <= max_freq)])

        normal_ratio = normal_count / len(normal_usage) * 100 if len(normal_usage) > 0 else 0
        group_ratio = group_count / len(group_usage) * 100 if len(group_usage) > 0 else 0

        # 差异分析
        if group_ratio > normal_ratio * 1.5:
            diff_analysis = '团伙地址更高频'
        elif normal_ratio > group_ratio * 1.5:
            diff_analysis = '正常地址更高频'
        else:
            diff_analysis = '基本相当'

        ws.cell(row=i, column=1, value=freq_name)
        ws.cell(row=i, column=2, value=normal_count)
        ws.cell(row=i, column=3, value=f"{normal_ratio:.1f}%")
        ws.cell(row=i, column=4, value=group_count)
        ws.cell(row=i, column=5, value=f"{group_ratio:.1f}%")
        ws.cell(row=i, column=6, value=diff_analysis)

        # 高频使用着色
        if min_freq > 10:
            for col in range(1, 7):
                ws.cell(row=i, column=col).fill = PatternFill(start_color='FFE6E6', end_color='FFE6E6', fill_type='solid')
        elif min_freq > 5:
            for col in range(1, 7):
                ws.cell(row=i, column=col).fill = PatternFill(start_color='FFF2E6', end_color='FFF2E6', fill_type='solid')

    # 调整列宽
    for col in range(1, 7):
        ws.column_dimensions[chr(64 + col)].width = 15

def create_group_size_analysis_sheet(wb, group_addresses, df):
    """创建团伙地址规模分析工作表"""
    ws = wb.create_sheet("团伙规模分析")

    # 标题
    ws['A1'] = '团伙地址规模详细分析'
    ws['A1'].font = Font(size=16, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center')
    ws.merge_cells('A1:H1')

    # 规模分布统计
    size_ranges = [
        ('2人团伙', 2, 2),
        ('3人团伙', 3, 3),
        ('4人团伙', 4, 4),
        ('5人团伙', 5, 5),
        ('6-10人团伙', 6, 10),
        ('11-20人团伙', 11, 20),
        ('21-50人团伙', 21, 50),
        ('51-100人团伙', 51, 100),
        ('101-200人团伙', 101, 200),
        ('200人以上团伙', 201, float('inf'))
    ]

    ws['A3'] = '团伙规模分布统计'
    ws['A3'].font = Font(size=14, bold=True)

    headers = ['团伙规模', '地址数量', '占团伙地址比例', '占总地址比例', '总用户数', '总交易数', '平均交易数', '风险等级']
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=5, column=col, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')

    total_addresses = df['address'].nunique()  # 总地址数

    for i, (size_name, min_size, max_size) in enumerate(size_ranges, 6):
        if max_size == float('inf'):
            size_addresses = group_addresses[group_addresses['user_count'] >= min_size]
        else:
            size_addresses = group_addresses[(group_addresses['user_count'] >= min_size) & (group_addresses['user_count'] <= max_size)]

        if len(size_addresses) > 0:
            # 计算统计信息
            total_users = size_addresses['user_count'].sum()

            # 获取这些地址的交易信息
            address_list = size_addresses['address'].tolist()
            address_transactions = df[df['address'].isin(address_list)]
            total_transactions = len(address_transactions)
            avg_transactions = total_transactions / len(size_addresses) if len(size_addresses) > 0 else 0

            # 风险等级
            if min_size >= 100:
                risk_level = '极高风险'
                risk_color = 'FF9999'
            elif min_size >= 50:
                risk_level = '高风险'
                risk_color = 'FFE6E6'
            elif min_size >= 20:
                risk_level = '中风险'
                risk_color = 'FFF2E6'
            else:
                risk_level = '低风险'
                risk_color = None

            ws.cell(row=i, column=1, value=size_name)
            ws.cell(row=i, column=2, value=len(size_addresses))
            ws.cell(row=i, column=3, value=f"{len(size_addresses)/len(group_addresses)*100:.1f}%")
            ws.cell(row=i, column=4, value=f"{len(size_addresses)/total_addresses*100:.1f}%")
            ws.cell(row=i, column=5, value=total_users)
            ws.cell(row=i, column=6, value=total_transactions)
            ws.cell(row=i, column=7, value=f"{avg_transactions:.1f}")
            ws.cell(row=i, column=8, value=risk_level)

            # 根据风险等级着色
            if risk_color:
                for col in range(1, 9):
                    ws.cell(row=i, column=col).fill = PatternFill(start_color=risk_color, end_color=risk_color, fill_type='solid')
        else:
            ws.cell(row=i, column=1, value=size_name)
            for col in range(2, 9):
                ws.cell(row=i, column=col, value=0)

    # 调整列宽
    for col in range(1, 9):
        ws.column_dimensions[chr(64 + col)].width = 12

def create_complete_address_list_sheet(wb, address_user_counts, df):
    """创建完整地址列表工作表"""
    ws = wb.create_sheet("完整地址列表")

    # 标题
    ws['A1'] = f'完整地址列表（共{len(address_user_counts):,}个地址）'
    ws['A1'].font = Font(size=16, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center')
    ws.merge_cells('A1:H1')

    # 表头
    headers = ['地址', '用户数', '地址类型', '总交易数', '主要币种', '活动天数', '平均每人交易数', '风险等级']
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=3, column=col, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')

    # 按用户数排序，显示所有地址
    address_user_counts_sorted = address_user_counts.sort_values('user_count', ascending=False)

    print(f"正在导出 {len(address_user_counts)} 个地址的完整列表...")

    for i, (_, row) in enumerate(address_user_counts_sorted.iterrows(), 4):
        address = row['address']
        user_count = row['user_count']

        # 地址类型
        if user_count == 1:
            address_type = '正常地址'
            type_color = None
        else:
            address_type = f'{user_count}人团伙'
            if user_count >= 100:
                type_color = 'FF9999'
            elif user_count >= 50:
                type_color = 'FFE6E6'
            elif user_count >= 10:
                type_color = 'FFF2E6'
            else:
                type_color = 'F0F8FF'

        # 获取地址详细信息
        address_data = df[df['address'] == address]
        total_transactions = len(address_data)
        main_currency = address_data['currency'].value_counts().index[0] if len(address_data) > 0 else 'N/A'
        active_days = (address_data['dt'].max() - address_data['dt'].min()).days + 1 if len(address_data) > 0 else 0
        avg_transactions_per_user = total_transactions / user_count

        # 风险等级
        if user_count >= 100:
            risk_level = '极高风险'
        elif user_count >= 50:
            risk_level = '高风险'
        elif user_count >= 20:
            risk_level = '中风险'
        elif user_count >= 10:
            risk_level = '低风险'
        else:
            risk_level = '正常'

        ws.cell(row=i, column=1, value=address[:30] + '...')
        ws.cell(row=i, column=2, value=user_count)
        ws.cell(row=i, column=3, value=address_type)
        ws.cell(row=i, column=4, value=total_transactions)
        ws.cell(row=i, column=5, value=main_currency)
        ws.cell(row=i, column=6, value=active_days)
        ws.cell(row=i, column=7, value=f"{avg_transactions_per_user:.1f}")
        ws.cell(row=i, column=8, value=risk_level)

        # 根据地址类型着色
        if type_color:
            for col in range(1, 9):
                ws.cell(row=i, column=col).fill = PatternFill(start_color=type_color, end_color=type_color, fill_type='solid')

    # 调整列宽
    for col in range(1, 9):
        ws.column_dimensions[chr(64 + col)].width = 12

    print(f"完整地址列表导出完成")

def main():
    """主函数"""
    print("开始地址分类分析...")
    print("团伙定义：2个账户及以上使用过相同的提币地址")

    # 1. 分析地址分类
    df, address_user_counts, normal_addresses, group_addresses = analyze_address_classification()

    # 2. 分析使用频次
    normal_usage, group_usage = analyze_usage_frequency_by_address_type(df, normal_addresses, group_addresses)

    # 3. 创建Excel报告
    filename = create_address_classification_excel(df, address_user_counts, normal_addresses, group_addresses, normal_usage, group_usage)

    # 4. 输出最终统计
    print(f"\n=== 最终统计摘要 ===")
    print(f"总地址数: {len(address_user_counts):,}")
    print(f"正常地址数: {len(normal_addresses):,} ({len(normal_addresses)/len(address_user_counts)*100:.1f}%)")
    print(f"团伙地址数: {len(group_addresses):,} ({len(group_addresses)/len(address_user_counts)*100:.1f}%)")

    print(f"\n正常地址使用频次:")
    print(f"  仅使用1次: {len(normal_usage[normal_usage['usage_count'] == 1]):,}")
    print(f"  使用2-3次: {len(normal_usage[(normal_usage['usage_count'] >= 2) & (normal_usage['usage_count'] <= 3)]):,}")
    print(f"  使用3次以上: {len(normal_usage[normal_usage['usage_count'] > 3]):,}")

    print(f"\n团伙地址使用频次:")
    print(f"  仅使用1次: {len(group_usage[group_usage['usage_count'] == 1]):,}")
    print(f"  使用2-3次: {len(group_usage[(group_usage['usage_count'] >= 2) & (group_usage['usage_count'] <= 3)]):,}")
    print(f"  使用3次以上: {len(group_usage[group_usage['usage_count'] > 3]):,}")

    print(f"\n✅ 地址分类分析完成: {filename}")
    print("包含6个工作表，所有数据完整导出:")
    print("1. 地址分类概览 - 整体统计和分布")
    print("2. 正常地址详情 - 正常地址的使用情况")
    print("3. 团伙地址详情 - 所有团伙地址的详细信息")
    print("4. 使用频次对比 - 正常vs团伙地址的使用频次对比")
    print("5. 团伙规模分析 - 详细的团伙规模分布")
    print("6. 完整地址列表 - 所有地址的完整列表")

if __name__ == "__main__":
    main()
