#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细团伙分析脚本
深入分析可疑团伙的行为模式
"""

import pandas as pd
import numpy as np
from collections import defaultdict, Counter
import json
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def load_data():
    """加载数据"""
    df = pd.read_csv('提币地址.csv')
    df['dt'] = pd.to_datetime(df['dt'], format='%Y%m%d')
    return df

def get_shared_addresses(df, min_users=3):
    """获取共享地址信息"""
    address_member_count = df.groupby('address')['member_id'].nunique().reset_index()
    address_member_count.columns = ['address', 'unique_members']
    shared_addresses = address_member_count[address_member_count['unique_members'] >= min_users]
    return shared_addresses

def analyze_group_behavior(df, address, members):
    """分析单个团伙的行为模式"""
    group_data = df[df['member_id'].isin(members)]
    
    # 基本统计
    analysis = {
        'address': address,
        'member_count': len(members),
        'members': list(members),
        'total_transactions': len(group_data),
        'unique_currencies': group_data['currency'].nunique(),
        'currencies': list(group_data['currency'].unique()),
        'date_range': {
            'start': group_data['dt'].min().strftime('%Y-%m-%d'),
            'end': group_data['dt'].max().strftime('%Y-%m-%d'),
            'span_days': (group_data['dt'].max() - group_data['dt'].min()).days + 1
        }
    }
    
    # 金额分析
    amount_data = group_data.dropna(subset=['currency_usdt_amount'])
    if len(amount_data) > 0:
        analysis['amount_stats'] = {
            'total_usdt': amount_data['currency_usdt_amount'].sum(),
            'avg_usdt': amount_data['currency_usdt_amount'].mean(),
            'median_usdt': amount_data['currency_usdt_amount'].median(),
            'max_usdt': amount_data['currency_usdt_amount'].max(),
            'min_usdt': amount_data['currency_usdt_amount'].min(),
            'transactions_with_amount': len(amount_data)
        }
    else:
        analysis['amount_stats'] = None
    
    # 时间模式分析
    daily_counts = group_data.groupby('dt').size()
    analysis['time_patterns'] = {
        'avg_daily_transactions': daily_counts.mean(),
        'max_daily_transactions': daily_counts.max(),
        'active_days': len(daily_counts),
        'peak_activity_date': daily_counts.idxmax().strftime('%Y-%m-%d'),
        'peak_activity_count': daily_counts.max()
    }
    
    # 成员活动分析
    member_activity = group_data.groupby('member_id').agg({
        'id': 'count',
        'currency_usdt_amount': ['sum', 'mean'],
        'dt': ['min', 'max']
    }).round(2)
    
    member_activity.columns = ['transaction_count', 'total_amount', 'avg_amount', 'first_transaction', 'last_transaction']
    member_activity = member_activity.fillna(0)
    
    analysis['member_activity'] = member_activity.to_dict('index')
    
    # 币种偏好
    currency_dist = group_data['currency'].value_counts()
    analysis['currency_distribution'] = currency_dist.to_dict()
    
    # 可疑行为指标
    analysis['suspicious_indicators'] = calculate_suspicious_indicators(group_data, members)
    
    return analysis

def calculate_suspicious_indicators(group_data, members):
    """计算可疑行为指标"""
    indicators = {}
    
    # 1. 时间集中度 - 如果大部分交易在很短时间内完成
    date_range = (group_data['dt'].max() - group_data['dt'].min()).days + 1
    transaction_density = len(group_data) / max(date_range, 1)
    indicators['transaction_density'] = transaction_density
    
    # 2. 金额相似度 - 检查是否有很多相似金额的交易
    amount_data = group_data.dropna(subset=['currency_usdt_amount'])
    if len(amount_data) > 1:
        amounts = amount_data['currency_usdt_amount'].values
        # 计算金额的变异系数
        cv = np.std(amounts) / np.mean(amounts) if np.mean(amounts) > 0 else 0
        indicators['amount_variation_coefficient'] = cv
        
        # 检查是否有大量相同金额
        amount_counts = amount_data['currency_usdt_amount'].value_counts()
        max_same_amount = amount_counts.max()
        indicators['max_identical_amounts'] = max_same_amount
        indicators['identical_amount_ratio'] = max_same_amount / len(amount_data)
    
    # 3. 成员活动同步性 - 检查成员是否在相似时间活动
    member_dates = group_data.groupby('member_id')['dt'].apply(set)
    if len(member_dates) > 1:
        # 计算成员间共同活动日期的重叠度
        all_dates = set()
        for dates in member_dates:
            all_dates.update(dates)
        
        overlap_scores = []
        member_list = list(member_dates.index)
        for i in range(len(member_list)):
            for j in range(i+1, len(member_list)):
                dates1 = member_dates[member_list[i]]
                dates2 = member_dates[member_list[j]]
                overlap = len(dates1.intersection(dates2)) / len(dates1.union(dates2))
                overlap_scores.append(overlap)
        
        indicators['avg_member_date_overlap'] = np.mean(overlap_scores) if overlap_scores else 0
    
    # 4. 币种集中度
    currency_dist = group_data['currency'].value_counts()
    top_currency_ratio = currency_dist.iloc[0] / len(group_data) if len(currency_dist) > 0 else 0
    indicators['top_currency_concentration'] = top_currency_ratio
    
    # 5. 风险评分 (0-100)
    risk_score = 0
    
    # 高交易密度 (+20)
    if transaction_density > 10:
        risk_score += 20
    elif transaction_density > 5:
        risk_score += 10
    
    # 低金额变异 (+15)
    if 'amount_variation_coefficient' in indicators and indicators['amount_variation_coefficient'] < 0.3:
        risk_score += 15
    
    # 高相同金额比例 (+20)
    if 'identical_amount_ratio' in indicators and indicators['identical_amount_ratio'] > 0.5:
        risk_score += 20
    
    # 高成员活动重叠 (+20)
    if 'avg_member_date_overlap' in indicators and indicators['avg_member_date_overlap'] > 0.7:
        risk_score += 20
    
    # 高币种集中度 (+15)
    if top_currency_ratio > 0.8:
        risk_score += 15
    
    # 大团伙规模 (+10)
    if len(members) > 100:
        risk_score += 10
    
    indicators['risk_score'] = min(risk_score, 100)
    
    return indicators

def analyze_top_groups(df, top_n=20):
    """分析前N个最大的团伙"""
    print(f"=== 分析前{top_n}个最大团伙 ===")
    
    shared_addresses = get_shared_addresses(df, min_users=3)
    shared_addresses = shared_addresses.sort_values('unique_members', ascending=False)
    
    detailed_analyses = []
    
    for i, (_, row) in enumerate(shared_addresses.head(top_n).iterrows()):
        address = row['address']
        member_count = row['unique_members']
        
        print(f"\n分析团伙 {i+1}/{top_n} (成员数: {member_count})")
        
        # 获取该地址的所有成员
        members = df[df['address'] == address]['member_id'].unique()
        
        # 详细分析
        analysis = analyze_group_behavior(df, address, members)
        detailed_analyses.append(analysis)
    
    return detailed_analyses

def generate_detailed_report(analyses):
    """生成详细报告"""
    print("\n=== 生成详细分析报告 ===")
    
    # 按风险评分排序
    analyses.sort(key=lambda x: x['suspicious_indicators']['risk_score'], reverse=True)
    
    report = """# 详细团伙分析报告

## 执行摘要
本报告对发现的可疑提币地址团伙进行了深入分析，重点关注以下几个方面：
1. 团伙规模和成员构成
2. 交易行为模式
3. 金额分布特征
4. 时间活动模式
5. 可疑行为指标

## 高风险团伙排名（按风险评分）

"""
    
    for i, analysis in enumerate(analyses[:10]):
        risk_score = analysis['suspicious_indicators']['risk_score']
        report += f"""
### 第{i+1}名 - 风险评分: {risk_score}/100

**基本信息:**
- 共享地址: `{analysis['address'][:50]}...`
- 成员数量: {analysis['member_count']}
- 总交易次数: {analysis['total_transactions']}
- 活动时间: {analysis['date_range']['start']} 至 {analysis['date_range']['end']} ({analysis['date_range']['span_days']}天)
- 涉及币种: {analysis['unique_currencies']}个 ({', '.join(analysis['currencies'][:5])}{'...' if len(analysis['currencies']) > 5 else ''})

**交易特征:**
- 日均交易量: {analysis['time_patterns']['avg_daily_transactions']:.1f}
- 最高日交易量: {analysis['time_patterns']['max_daily_transactions']}
- 活跃天数: {analysis['time_patterns']['active_days']}
- 交易密度: {analysis['suspicious_indicators']['transaction_density']:.2f} 笔/天

"""
        
        if analysis['amount_stats']:
            report += f"""**金额分析:**
- 总金额(USDT): {analysis['amount_stats']['total_usdt']:,.2f}
- 平均金额(USDT): {analysis['amount_stats']['avg_usdt']:,.2f}
- 中位数金额(USDT): {analysis['amount_stats']['median_usdt']:,.2f}
- 金额变异系数: {analysis['suspicious_indicators'].get('amount_variation_coefficient', 'N/A')}

"""
        
        report += f"""**可疑指标:**
- 币种集中度: {analysis['suspicious_indicators']['top_currency_concentration']:.2%}
- 成员活动重叠度: {analysis['suspicious_indicators'].get('avg_member_date_overlap', 'N/A')}
- 相同金额比例: {analysis['suspicious_indicators'].get('identical_amount_ratio', 'N/A')}

---
"""
    
    # 保存报告
    with open('detailed_group_analysis_report.md', 'w', encoding='utf-8') as f:
        f.write(report)
    
    # 保存JSON数据供进一步分析
    with open('group_analysis_data.json', 'w', encoding='utf-8') as f:
        # 转换datetime对象为字符串
        json_data = []
        for analysis in analyses:
            json_analysis = analysis.copy()
            # 处理member_activity中的datetime对象
            if 'member_activity' in json_analysis:
                for member_id, activity in json_analysis['member_activity'].items():
                    if 'first_transaction' in activity and hasattr(activity['first_transaction'], 'strftime'):
                        activity['first_transaction'] = activity['first_transaction'].strftime('%Y-%m-%d')
                    if 'last_transaction' in activity and hasattr(activity['last_transaction'], 'strftime'):
                        activity['last_transaction'] = activity['last_transaction'].strftime('%Y-%m-%d')
            json_data.append(json_analysis)
        
        json.dump(json_data, f, ensure_ascii=False, indent=2, default=str)
    
    print("详细分析报告已保存:")
    print("- detailed_group_analysis_report.md (Markdown报告)")
    print("- group_analysis_data.json (JSON数据)")

def main():
    """主函数"""
    print("开始详细团伙分析...")
    
    # 加载数据
    df = load_data()
    
    # 分析前20个最大团伙
    analyses = analyze_top_groups(df, top_n=20)
    
    # 生成详细报告
    generate_detailed_report(analyses)
    
    print("\n=== 详细分析完成 ===")

if __name__ == "__main__":
    main()
