#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
地址使用频次统计摘要
快速查看账户使用同一地址的次数分布
"""

import pandas as pd
import numpy as np
from collections import Counter

def analyze_address_usage_frequency():
    """分析地址使用频次"""
    print("=== 地址使用频次分析 ===")
    
    # 加载数据
    df = pd.read_csv('提币地址.csv')
    print(f"总交易记录: {len(df):,}")
    print(f"唯一账户数: {df['member_id'].nunique():,}")
    print(f"唯一地址数: {df['address'].nunique():,}")
    
    # 计算每个账户对每个地址的使用次数
    print("\n正在计算账户-地址使用频次...")
    account_address_usage = df.groupby(['member_id', 'address']).size().reset_index(name='usage_count')
    
    print(f"账户-地址组合总数: {len(account_address_usage):,}")
    
    # 按使用次数分类
    usage_1_time = account_address_usage[account_address_usage['usage_count'] == 1]
    usage_2_3_times = account_address_usage[(account_address_usage['usage_count'] >= 2) & (account_address_usage['usage_count'] <= 3)]
    usage_more_than_3 = account_address_usage[account_address_usage['usage_count'] > 3]
    
    print(f"\n=== 基本统计 ===")
    print(f"仅使用1次: {len(usage_1_time):,} 组合 ({len(usage_1_time)/len(account_address_usage)*100:.1f}%)")
    print(f"使用2-3次: {len(usage_2_3_times):,} 组合 ({len(usage_2_3_times)/len(account_address_usage)*100:.1f}%)")
    print(f"使用3次以上: {len(usage_more_than_3):,} 组合 ({len(usage_more_than_3)/len(account_address_usage)*100:.1f}%)")
    
    # 详细分布
    usage_distribution = account_address_usage['usage_count'].value_counts().sort_index()
    
    print(f"\n=== 详细使用次数分布 ===")
    print("使用次数 | 组合数量 | 占比")
    print("-" * 30)
    
    cumulative = 0
    total = len(account_address_usage)
    
    for usage_count, count in usage_distribution.head(15).items():
        cumulative += count
        print(f"{usage_count:8d} | {count:8,} | {count/total*100:5.1f}% (累计: {cumulative/total*100:5.1f}%)")
    
    # 高频使用分析
    high_freq_usage = account_address_usage[account_address_usage['usage_count'] > 10]
    
    if len(high_freq_usage) > 0:
        print(f"\n=== 高频使用分析（>10次） ===")
        print(f"高频使用组合数: {len(high_freq_usage):,}")
        print(f"涉及账户数: {high_freq_usage['member_id'].nunique():,}")
        print(f"涉及地址数: {high_freq_usage['address'].nunique():,}")
        print(f"最高使用次数: {high_freq_usage['usage_count'].max()}")
        print(f"平均使用次数: {high_freq_usage['usage_count'].mean():.1f}")
        
        # 显示前10个最高频使用
        print(f"\n前10个最高频使用:")
        top_usage = high_freq_usage.nlargest(10, 'usage_count')
        for _, row in top_usage.iterrows():
            print(f"  账户: {row['member_id']} | 地址: {row['address'][:20]}... | 使用次数: {row['usage_count']}")
    else:
        print(f"\n=== 高频使用分析 ===")
        print("未发现使用次数超过10次的账户-地址组合")
    
    # 账户行为模式分析
    print(f"\n=== 账户行为模式分析 ===")
    
    # 每个账户使用的地址数量
    account_address_counts = df.groupby('member_id')['address'].nunique()
    
    single_address_accounts = (account_address_counts == 1).sum()
    multi_address_accounts = (account_address_counts > 1).sum()
    high_diversity_accounts = (account_address_counts > 10).sum()
    
    total_accounts = len(account_address_counts)
    
    print(f"单地址账户: {single_address_accounts:,} ({single_address_accounts/total_accounts*100:.1f}%)")
    print(f"多地址账户: {multi_address_accounts:,} ({multi_address_accounts/total_accounts*100:.1f}%)")
    print(f"高多样性账户(>10地址): {high_diversity_accounts:,} ({high_diversity_accounts/total_accounts*100:.1f}%)")
    
    if high_diversity_accounts > 0:
        print(f"\n高多样性账户详情:")
        high_div_accounts = account_address_counts[account_address_counts > 10].nlargest(10)
        for account_id, address_count in high_div_accounts.items():
            print(f"  账户: {account_id} | 使用地址数: {address_count}")
    
    # 地址被使用的模式
    print(f"\n=== 地址被使用模式分析 ===")
    
    # 每个地址被多少个账户使用
    address_user_counts = df.groupby('address')['member_id'].nunique()
    
    single_user_addresses = (address_user_counts == 1).sum()
    multi_user_addresses = (address_user_counts > 1).sum()
    group_addresses = (address_user_counts >= 3).sum()
    
    total_addresses = len(address_user_counts)
    
    print(f"单用户地址: {single_user_addresses:,} ({single_user_addresses/total_addresses*100:.1f}%)")
    print(f"多用户地址: {multi_user_addresses:,} ({multi_user_addresses/total_addresses*100:.1f}%)")
    print(f"团伙地址(≥3用户): {group_addresses:,} ({group_addresses/total_addresses*100:.1f}%)")
    
    # 交叉分析：高频使用 vs 团伙地址
    if len(high_freq_usage) > 0:
        print(f"\n=== 交叉分析：高频使用 vs 团伙地址 ===")
        
        # 获取地址的用户数信息
        address_user_counts_dict = address_user_counts.to_dict()
        
        # 为高频使用数据添加地址用户数信息
        high_freq_usage['address_user_count'] = high_freq_usage['address'].map(address_user_counts_dict)
        
        # 分析高频使用中有多少是团伙地址
        high_freq_in_groups = high_freq_usage[high_freq_usage['address_user_count'] >= 3]
        high_freq_not_in_groups = high_freq_usage[high_freq_usage['address_user_count'] < 3]
        
        print(f"高频使用且为团伙地址: {len(high_freq_in_groups):,} ({len(high_freq_in_groups)/len(high_freq_usage)*100:.1f}%)")
        print(f"高频使用但非团伙地址: {len(high_freq_not_in_groups):,} ({len(high_freq_not_in_groups)/len(high_freq_usage)*100:.1f}%)")
        
        if len(high_freq_in_groups) > 0:
            print(f"\n高频使用团伙地址示例:")
            sample_group_high_freq = high_freq_in_groups.nlargest(5, 'usage_count')
            for _, row in sample_group_high_freq.iterrows():
                print(f"  账户: {row['member_id']} | 使用次数: {row['usage_count']} | 地址用户数: {row['address_user_count']}")
    
    # 风险评估
    print(f"\n=== 风险评估 ===")
    
    # 计算风险指标
    total_combinations = len(account_address_usage)
    high_risk_combinations = len(usage_more_than_3)
    medium_risk_combinations = len(usage_2_3_times)
    
    print(f"高风险组合(>3次使用): {high_risk_combinations:,} ({high_risk_combinations/total_combinations*100:.1f}%)")
    print(f"中风险组合(2-3次使用): {medium_risk_combinations:,} ({medium_risk_combinations/total_combinations*100:.1f}%)")
    print(f"低风险组合(1次使用): {len(usage_1_time):,} ({len(usage_1_time)/total_combinations*100:.1f}%)")
    
    # 建议
    print(f"\n=== 监控建议 ===")
    if high_risk_combinations > 0:
        print(f"1. 重点监控 {high_risk_combinations:,} 个高频使用组合")
    if len(high_freq_usage) > 0:
        print(f"2. 特别关注 {len(high_freq_usage):,} 个超高频使用组合(>10次)")
    if high_diversity_accounts > 0:
        print(f"3. 审查 {high_diversity_accounts:,} 个高多样性账户(使用>10个地址)")
    
    print(f"4. 建议设置使用次数阈值预警(建议阈值: 5次)")
    print(f"5. 结合团伙分析，重点关注高频使用且为团伙地址的情况")
    
    return account_address_usage, usage_distribution

def main():
    """主函数"""
    print("开始地址使用频次分析...")
    
    account_address_usage, usage_distribution = analyze_address_usage_frequency()
    
    print(f"\n✅ 分析完成")
    print(f"详细结果已包含在 '提币地址全面分析报告.xlsx' 的 '地址使用频次分析' 工作表中")

if __name__ == "__main__":
    main()
