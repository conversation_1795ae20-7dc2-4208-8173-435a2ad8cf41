#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终总结分析脚本
提供清晰的结论和建议
"""

import pandas as pd
import numpy as np
import json
from collections import Counter
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment
from openpyxl.utils.dataframe import dataframe_to_rows

def load_analysis_data():
    """加载分析数据"""
    with open('group_analysis_data.json', 'r', encoding='utf-8') as f:
        analyses = json.load(f)
    return analyses

def generate_executive_summary(analyses):
    """生成执行摘要"""
    print("=== 提币地址团伙分析 - 执行摘要 ===\n")
    
    # 基本统计
    total_groups = len(analyses)
    total_members = sum(analysis['member_count'] for analysis in analyses)
    total_transactions = sum(analysis['total_transactions'] for analysis in analyses)
    
    print(f"📊 **数据概览**")
    print(f"- 分析的大型团伙数量: {total_groups}")
    print(f"- 涉及总用户数: {total_members:,}")
    print(f"- 涉及总交易数: {total_transactions:,}")
    
    # 风险分级
    high_risk = [a for a in analyses if a['suspicious_indicators']['risk_score'] >= 70]
    medium_risk = [a for a in analyses if 50 <= a['suspicious_indicators']['risk_score'] < 70]
    low_risk = [a for a in analyses if a['suspicious_indicators']['risk_score'] < 50]
    
    print(f"\n🚨 **风险分级**")
    print(f"- 高风险团伙 (≥70分): {len(high_risk)} 个")
    print(f"- 中风险团伙 (50-69分): {len(medium_risk)} 个")
    print(f"- 低风险团伙 (<50分): {len(low_risk)} 个")
    
    # 最大团伙
    largest_group = max(analyses, key=lambda x: x['member_count'])
    print(f"\n👥 **最大团伙**")
    print(f"- 成员数量: {largest_group['member_count']}")
    print(f"- 风险评分: {largest_group['suspicious_indicators']['risk_score']}/100")
    print(f"- 交易次数: {largest_group['total_transactions']}")
    
    # 金额统计
    total_amount = 0
    groups_with_amount = 0
    for analysis in analyses:
        if analysis['amount_stats']:
            total_amount += analysis['amount_stats']['total_usdt']
            groups_with_amount += 1
    
    print(f"\n💰 **金额统计**")
    print(f"- 有金额数据的团伙: {groups_with_amount}/{total_groups}")
    print(f"- 涉及总金额: ${total_amount:,.2f} USDT")
    print(f"- 平均每团伙金额: ${total_amount/groups_with_amount:,.2f} USDT" if groups_with_amount > 0 else "- 平均每团伙金额: N/A")

def identify_key_patterns(analyses):
    """识别关键模式"""
    print(f"\n🔍 **关键模式识别**\n")
    
    # 1. 币种偏好分析
    all_currencies = []
    for analysis in analyses:
        all_currencies.extend(analysis['currencies'])
    
    currency_counter = Counter(all_currencies)
    print("1️⃣ **币种偏好**")
    print("   最常见的币种:")
    for currency, count in currency_counter.most_common(5):
        print(f"   - {currency}: {count} 个团伙使用")
    
    # 2. 时间模式分析
    print(f"\n2️⃣ **时间模式**")
    
    # 活动时间跨度
    time_spans = [analysis['date_range']['span_days'] for analysis in analyses]
    avg_span = np.mean(time_spans)
    print(f"   - 平均活动时间跨度: {avg_span:.1f} 天")
    
    short_term = len([s for s in time_spans if s <= 30])
    medium_term = len([s for s in time_spans if 30 < s <= 90])
    long_term = len([s for s in time_spans if s > 90])
    
    print(f"   - 短期活动 (≤30天): {short_term} 个团伙")
    print(f"   - 中期活动 (31-90天): {medium_term} 个团伙")
    print(f"   - 长期活动 (>90天): {long_term} 个团伙")
    
    # 3. 交易密度分析
    print(f"\n3️⃣ **交易密度**")
    densities = [analysis['suspicious_indicators']['transaction_density'] for analysis in analyses]
    high_density = len([d for d in densities if d > 10])
    medium_density = len([d for d in densities if 5 <= d <= 10])
    low_density = len([d for d in densities if d < 5])
    
    print(f"   - 高密度 (>10笔/天): {high_density} 个团伙")
    print(f"   - 中密度 (5-10笔/天): {medium_density} 个团伙")
    print(f"   - 低密度 (<5笔/天): {low_density} 个团伙")
    
    # 4. 金额模式
    print(f"\n4️⃣ **金额模式**")
    amount_variations = []
    identical_ratios = []
    
    for analysis in analyses:
        if 'amount_variation_coefficient' in analysis['suspicious_indicators']:
            amount_variations.append(analysis['suspicious_indicators']['amount_variation_coefficient'])
        if 'identical_amount_ratio' in analysis['suspicious_indicators']:
            identical_ratios.append(analysis['suspicious_indicators']['identical_amount_ratio'])
    
    if amount_variations:
        low_variation = len([v for v in amount_variations if v < 0.3])
        print(f"   - 低金额变异团伙 (<0.3): {low_variation}/{len(amount_variations)}")
    
    if identical_ratios:
        high_identical = len([r for r in identical_ratios if r > 0.5])
        print(f"   - 高相同金额比例团伙 (>50%): {high_identical}/{len(identical_ratios)}")

def provide_recommendations(analyses):
    """提供建议"""
    print(f"\n💡 **风险管控建议**\n")
    
    # 高风险团伙
    high_risk_groups = [a for a in analyses if a['suspicious_indicators']['risk_score'] >= 70]
    
    print("🔴 **立即关注 (高风险团伙)**")
    if high_risk_groups:
        for i, group in enumerate(high_risk_groups[:3]):
            print(f"   {i+1}. 团伙规模: {group['member_count']} 人")
            print(f"      风险评分: {group['suspicious_indicators']['risk_score']}/100")
            print(f"      主要风险: ", end="")
            risks = []
            if group['suspicious_indicators']['top_currency_concentration'] > 0.9:
                risks.append("币种高度集中")
            if group['suspicious_indicators']['transaction_density'] > 10:
                risks.append("交易密度异常")
            if 'identical_amount_ratio' in group['suspicious_indicators'] and group['suspicious_indicators']['identical_amount_ratio'] > 0.5:
                risks.append("金额高度相似")
            print(", ".join(risks) if risks else "多项指标异常")
            print()
    else:
        print("   暂无极高风险团伙")
    
    print("🟡 **重点监控 (中风险团伙)**")
    medium_risk_groups = [a for a in analyses if 50 <= a['suspicious_indicators']['risk_score'] < 70]
    print(f"   - 共 {len(medium_risk_groups)} 个团伙需要重点监控")
    print(f"   - 建议定期审查这些团伙的交易模式")
    
    print(f"\n📋 **具体建议**")
    print("1. **地址黑名单管理**")
    print("   - 将高风险团伙的共享地址加入黑名单")
    print("   - 对这些地址的新交易进行人工审核")
    
    print(f"\n2. **用户账户审查**")
    print("   - 对高风险团伙成员进行KYC复核")
    print("   - 检查这些账户的注册信息和行为模式")
    
    print(f"\n3. **交易监控规则**")
    print("   - 设置共享地址使用超过3人的预警")
    print("   - 监控短时间内大量相似金额的提币")
    print("   - 关注单一币种高度集中的提币模式")
    
    print(f"\n4. **风险评估模型**")
    print("   - 建立基于地址共享的风险评分模型")
    print("   - 定期更新和优化风险指标权重")

def create_excel_report(analyses):
    """创建Excel综合报告"""
    print(f"\n📊 **生成Excel报告**")

    # 创建Excel工作簿
    wb = openpyxl.Workbook()

    # 删除默认工作表
    wb.remove(wb.active)

    # 1. 团伙概览工作表
    ws_overview = wb.create_sheet("团伙概览")
    create_overview_sheet(ws_overview, analyses)

    # 2. 高风险团伙详情
    ws_high_risk = wb.create_sheet("高风险团伙")
    create_high_risk_sheet(ws_high_risk, analyses)

    # 3. 高风险用户列表
    ws_members = wb.create_sheet("高风险用户")
    create_members_sheet(ws_members, analyses)

    # 4. 统计分析
    ws_stats = wb.create_sheet("统计分析")
    create_stats_sheet(ws_stats, analyses)

    # 保存Excel文件
    wb.save('提币地址团伙分析报告.xlsx')
    print(f"- 提币地址团伙分析报告.xlsx: 包含4个工作表的综合报告")

def create_overview_sheet(ws, analyses):
    """创建团伙概览工作表"""
    # 设置标题
    ws['A1'] = '提币地址团伙分析概览'
    ws['A1'].font = Font(size=16, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center')
    ws.merge_cells('A1:H1')

    # 表头
    headers = ['排名', '团伙地址(前20字符)', '成员数量', '风险评分', '总交易数', '主要币种', '活动天数', '总金额(USDT)']
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=3, column=col, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')

    # 按风险评分排序
    sorted_analyses = sorted(analyses, key=lambda x: x['suspicious_indicators']['risk_score'], reverse=True)

    # 填充数据
    for i, analysis in enumerate(sorted_analyses, 1):
        row = i + 3
        ws.cell(row=row, column=1, value=i)
        ws.cell(row=row, column=2, value=analysis['address'][:20] + '...')
        ws.cell(row=row, column=3, value=analysis['member_count'])

        # 风险评分着色
        risk_score = analysis['suspicious_indicators']['risk_score']
        risk_cell = ws.cell(row=row, column=4, value=risk_score)
        if risk_score >= 70:
            risk_cell.fill = PatternFill(start_color='FF6B6B', end_color='FF6B6B', fill_type='solid')
        elif risk_score >= 50:
            risk_cell.fill = PatternFill(start_color='FFE66D', end_color='FFE66D', fill_type='solid')

        ws.cell(row=row, column=5, value=analysis['total_transactions'])
        ws.cell(row=row, column=6, value=', '.join(analysis['currencies'][:2]))
        ws.cell(row=row, column=7, value=analysis['date_range']['span_days'])

        if analysis['amount_stats']:
            ws.cell(row=row, column=8, value=round(analysis['amount_stats']['total_usdt'], 2))
        else:
            ws.cell(row=row, column=8, value='N/A')

    # 调整列宽
    for col in range(1, 9):
        ws.column_dimensions[chr(64 + col)].width = 15

def create_high_risk_sheet(ws, analyses):
    """创建高风险团伙详情工作表"""
    high_risk_groups = [a for a in analyses if a['suspicious_indicators']['risk_score'] >= 50]

    # 设置标题
    ws['A1'] = '高风险团伙详细分析'
    ws['A1'].font = Font(size=16, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center')
    ws.merge_cells('A1:L1')

    # 表头
    headers = ['团伙ID', '共享地址', '成员数量', '风险评分', '总交易数', '币种数量', '主要币种',
               '活动开始', '活动结束', '活动天数', '总金额(USDT)', '平均金额(USDT)']

    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=3, column=col, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')

    # 填充数据
    for i, analysis in enumerate(high_risk_groups, 1):
        row = i + 3
        ws.cell(row=row, column=1, value=f'G{i:03d}')
        ws.cell(row=row, column=2, value=analysis['address'][:30] + '...')
        ws.cell(row=row, column=3, value=analysis['member_count'])
        ws.cell(row=row, column=4, value=analysis['suspicious_indicators']['risk_score'])
        ws.cell(row=row, column=5, value=analysis['total_transactions'])
        ws.cell(row=row, column=6, value=analysis['unique_currencies'])
        ws.cell(row=row, column=7, value=', '.join(analysis['currencies'][:3]))
        ws.cell(row=row, column=8, value=analysis['date_range']['start'])
        ws.cell(row=row, column=9, value=analysis['date_range']['end'])
        ws.cell(row=row, column=10, value=analysis['date_range']['span_days'])

        if analysis['amount_stats']:
            ws.cell(row=row, column=11, value=round(analysis['amount_stats']['total_usdt'], 2))
            ws.cell(row=row, column=12, value=round(analysis['amount_stats']['avg_usdt'], 2))
        else:
            ws.cell(row=row, column=11, value='N/A')
            ws.cell(row=row, column=12, value='N/A')

    # 调整列宽
    for col in range(1, 13):
        ws.column_dimensions[chr(64 + col)].width = 12

def create_members_sheet(ws, analyses):
    """创建高风险用户列表工作表"""
    high_risk_groups = [a for a in analyses if a['suspicious_indicators']['risk_score'] >= 50]

    # 设置标题
    ws['A1'] = '高风险用户列表'
    ws['A1'].font = Font(size=16, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center')
    ws.merge_cells('A1:G1')

    # 表头
    headers = ['用户ID', '团伙ID', '共享地址(前20字符)', '团伙规模', '风险评分', '主要币种', '备注']

    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=3, column=col, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')

    # 填充数据
    row = 4
    for group_idx, analysis in enumerate(high_risk_groups, 1):
        group_id = f'G{group_idx:03d}'
        risk_score = analysis['suspicious_indicators']['risk_score']

        for member_id in analysis['members']:
            ws.cell(row=row, column=1, value=member_id)
            ws.cell(row=row, column=2, value=group_id)
            ws.cell(row=row, column=3, value=analysis['address'][:20] + '...')
            ws.cell(row=row, column=4, value=analysis['member_count'])

            # 风险评分着色
            risk_cell = ws.cell(row=row, column=5, value=risk_score)
            if risk_score >= 60:
                risk_cell.fill = PatternFill(start_color='FFE66D', end_color='FFE66D', fill_type='solid')

            ws.cell(row=row, column=6, value=analysis['currencies'][0] if analysis['currencies'] else 'N/A')

            # 备注
            notes = []
            if analysis['suspicious_indicators']['top_currency_concentration'] > 0.9:
                notes.append('币种集中')
            if analysis['suspicious_indicators']['transaction_density'] > 10:
                notes.append('高频交易')
            ws.cell(row=row, column=7, value=', '.join(notes))

            row += 1

    # 调整列宽
    for col in range(1, 8):
        ws.column_dimensions[chr(64 + col)].width = 15

def create_stats_sheet(ws, analyses):
    """创建统计分析工作表"""
    # 设置标题
    ws['A1'] = '统计分析汇总'
    ws['A1'].font = Font(size=16, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center')
    ws.merge_cells('A1:D1')

    # 基本统计
    ws['A3'] = '基本统计'
    ws['A3'].font = Font(size=14, bold=True)

    stats_data = [
        ['指标', '数值', '说明', ''],
        ['分析团伙总数', len(analyses), '大型团伙(≥3人)', ''],
        ['涉及用户总数', sum(a['member_count'] for a in analyses), '所有团伙成员', ''],
        ['涉及交易总数', sum(a['total_transactions'] for a in analyses), '所有团伙交易', ''],
        ['', '', '', ''],
        ['风险分级', '', '', ''],
        ['高风险团伙(≥70分)', len([a for a in analyses if a['suspicious_indicators']['risk_score'] >= 70]), '需立即处理', ''],
        ['中风险团伙(50-69分)', len([a for a in analyses if 50 <= a['suspicious_indicators']['risk_score'] < 70]), '需重点监控', ''],
        ['低风险团伙(<50分)', len([a for a in analyses if a['suspicious_indicators']['risk_score'] < 50]), '常规监控', ''],
    ]

    for row_idx, row_data in enumerate(stats_data, 4):
        for col_idx, value in enumerate(row_data, 1):
            cell = ws.cell(row=row_idx, column=col_idx, value=value)
            if row_idx == 4 or row_idx == 9:  # 表头行
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')

    # 币种分析
    ws['A15'] = '币种分析'
    ws['A15'].font = Font(size=14, bold=True)

    all_currencies = []
    for analysis in analyses:
        all_currencies.extend(analysis['currencies'])

    currency_counter = Counter(all_currencies)

    ws['A17'] = '币种'
    ws['B17'] = '使用团伙数'
    ws['C17'] = '占比'

    for i, (currency, count) in enumerate(currency_counter.most_common(10), 18):
        ws.cell(row=i, column=1, value=currency)
        ws.cell(row=i, column=2, value=count)
        ws.cell(row=i, column=3, value=f'{count/len(analyses)*100:.1f}%')

    # 调整列宽
    for col in range(1, 5):
        ws.column_dimensions[chr(64 + col)].width = 20

def export_high_risk_details(analyses):
    """导出高风险团伙详细信息"""
    # 创建Excel报告
    create_excel_report(analyses)

    # 同时保留CSV格式
    high_risk_groups = [a for a in analyses if a['suspicious_indicators']['risk_score'] >= 50]

    # 创建CSV格式的高风险用户列表
    high_risk_members = []
    for group in high_risk_groups:
        for member_id in group['members']:
            high_risk_members.append({
                'member_id': member_id,
                'shared_address': group['address'][:50] + '...',
                'group_size': group['member_count'],
                'risk_score': group['suspicious_indicators']['risk_score'],
                'total_transactions': group['total_transactions'],
                'main_currency': group['currencies'][0] if group['currencies'] else 'N/A'
            })

    # 保存为CSV
    df_high_risk = pd.DataFrame(high_risk_members)
    df_high_risk.to_csv('high_risk_members.csv', index=False, encoding='utf-8')

    print(f"\n📄 **导出文件**")
    print(f"- 提币地址团伙分析报告.xlsx: 综合Excel报告")
    print(f"- high_risk_members.csv: {len(high_risk_members)} 个高风险用户")

def main():
    """主函数"""
    try:
        analyses = load_analysis_data()
        
        generate_executive_summary(analyses)
        identify_key_patterns(analyses)
        provide_recommendations(analyses)
        export_high_risk_details(analyses)
        
        print(f"\n✅ **分析完成**")
        print("所有分析结果和建议已生成完毕。")
        
    except FileNotFoundError:
        print("❌ 错误: 请先运行 detailed_group_analysis.py 生成分析数据")

if __name__ == "__main__":
    main()
