#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据分析脚本 - 分析归类后的数据.xlsx
分析维度：
1. 按月度分析
2. 按是否有KYC分析
3. 按member_country分析
4. 按origin分布分析
5. 分析standard_language是否和last_login_country匹配
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_data(file_path):
    """加载Excel数据"""
    try:
        df = pd.read_excel(file_path)
        print(f"数据加载成功，共有 {len(df)} 行数据")
        print(f"列名: {list(df.columns)}")
        return df
    except Exception as e:
        print(f"数据加载失败: {e}")
        return None

def analyze_monthly_distribution(df):
    """按月度分析"""
    print("\n" + "="*50)
    print("1. 月度分析")
    print("="*50)

    # 检查dt列（这应该是日期列）
    if 'dt' in df.columns:
        try:
            # 尝试转换为日期格式
            df['dt_parsed'] = pd.to_datetime(df['dt'], errors='coerce')
            valid_dates = df['dt_parsed'].notna().sum()

            if valid_dates > 0:
                print(f"dt列有效日期数量: {valid_dates}")

                # 提取月份
                df['month'] = df['dt_parsed'].dt.to_period('M')
                monthly_count = df['month'].value_counts().sort_index()

                print(f"\n按月分布:")
                for month, count in monthly_count.items():
                    percentage = (count / len(df)) * 100
                    print(f"  {month}: {count} 条记录 ({percentage:.2f}%)")

                # 创建月度分布图
                plt.figure(figsize=(12, 6))
                monthly_count.plot(kind='bar')
                plt.title('用户数据月度分布')
                plt.xlabel('月份')
                plt.ylabel('记录数量')
                plt.xticks(rotation=45)
                plt.tight_layout()
                plt.savefig('monthly_distribution.png', dpi=300, bbox_inches='tight')
                plt.close()
                print("月度分布图已保存为 monthly_distribution.png")

            else:
                print("dt列中没有有效的日期数据")

        except Exception as e:
            print(f"处理dt列时出错: {e}")
    else:
        print("未找到dt列，无法进行月度分析")

def analyze_kyc_status(df):
    """按是否有KYC分析"""
    print("\n" + "="*50)
    print("2. KYC状态分析")
    print("="*50)

    if 'kyc_country' in df.columns:
        # 判断是否有KYC
        df['has_kyc'] = df['kyc_country'].notna() & (df['kyc_country'] != '') & (df['kyc_country'] != 'None')

        kyc_stats = df['has_kyc'].value_counts()
        print(f"\nKYC状态分布:")
        has_kyc_count = kyc_stats.get(True, 0)
        no_kyc_count = kyc_stats.get(False, 0)
        has_kyc_pct = (has_kyc_count / len(df)) * 100
        no_kyc_pct = (no_kyc_count / len(df)) * 100

        print(f"  有KYC: {has_kyc_count} 条记录 ({has_kyc_pct:.2f}%)")
        print(f"  无KYC: {no_kyc_count} 条记录 ({no_kyc_pct:.2f}%)")

        # 创建KYC状态饼图
        plt.figure(figsize=(8, 6))
        labels = ['有KYC', '无KYC']
        sizes = [has_kyc_count, no_kyc_count]
        colors = ['#ff9999', '#66b3ff']
        plt.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
        plt.title('KYC状态分布')
        plt.axis('equal')
        plt.savefig('kyc_status_distribution.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("KYC状态分布图已保存为 kyc_status_distribution.png")

        # 按KYC状态分析国家分布
        if has_kyc_count > 0:
            print(f"\n有KYC用户的国家分布:")
            kyc_countries = df[df['has_kyc']]['kyc_country'].value_counts()
            for country, count in kyc_countries.head(10).items():
                percentage = (count / has_kyc_count) * 100
                print(f"  {country}: {count} 条记录 ({percentage:.2f}%)")

            # 创建有KYC用户的国家分布图
            plt.figure(figsize=(12, 8))
            top_kyc_countries = kyc_countries.head(15)
            top_kyc_countries.plot(kind='bar')
            plt.title('有KYC用户的国家分布 (前15名)')
            plt.xlabel('国家')
            plt.ylabel('用户数量')
            plt.xticks(rotation=45)
            plt.tight_layout()
            plt.savefig('kyc_countries_distribution.png', dpi=300, bbox_inches='tight')
            plt.close()
            print("有KYC用户国家分布图已保存为 kyc_countries_distribution.png")

    else:
        print("未找到 kyc_country 列")

def analyze_member_country(df):
    """按member_country分析"""
    print("\n" + "="*50)
    print("3. 会员国家分析")
    print("="*50)

    if 'member_country' in df.columns:
        country_stats = df['member_country'].value_counts()
        print(f"\n会员国家分布 (前20名):")
        for country, count in country_stats.head(20).items():
            percentage = (count / len(df)) * 100
            print(f"  {country}: {count} 条记录 ({percentage:.2f}%)")

        print(f"\n总共有 {len(country_stats)} 个不同的国家")

        # 创建会员国家分布图
        plt.figure(figsize=(15, 10))
        top_countries = country_stats.head(20)
        top_countries.plot(kind='bar')
        plt.title('会员国家分布 (前20名)')
        plt.xlabel('国家')
        plt.ylabel('用户数量')
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig('member_countries_distribution.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("会员国家分布图已保存为 member_countries_distribution.png")

        # 分析前5名国家的详细信息
        print(f"\n前5名国家详细分析:")
        top5_countries = country_stats.head(5)
        for country in top5_countries.index:
            country_data = df[df['member_country'] == country]
            kyc_rate = country_data['has_kyc'].sum() / len(country_data) * 100 if 'has_kyc' in df.columns else 0
            print(f"\n{country}:")
            print(f"  总用户数: {len(country_data)}")
            print(f"  KYC完成率: {kyc_rate:.2f}%")

            # 分析该国家的origin分布
            if 'origin' in df.columns:
                origin_dist = country_data['origin'].value_counts()
                print(f"  主要来源: {origin_dist.head(3).to_dict()}")

    else:
        print("未找到 member_country 列")

def analyze_origin_distribution(df):
    """按origin分布分析"""
    print("\n" + "="*50)
    print("4. Origin分布分析")
    print("="*50)

    if 'origin' in df.columns:
        origin_stats = df['origin'].value_counts()
        print(f"\nOrigin分布:")
        for origin, count in origin_stats.items():
            percentage = (count / len(df)) * 100
            print(f"  {origin}: {count} 条记录 ({percentage:.2f}%)")

        # 创建Origin分布饼图
        plt.figure(figsize=(10, 8))
        colors = ['#ff9999', '#66b3ff', '#99ff99', '#ffcc99', '#ff99cc', '#c2c2f0', '#ffb3e6']
        plt.pie(origin_stats.values, labels=origin_stats.index, colors=colors[:len(origin_stats)],
                autopct='%1.1f%%', startangle=90)
        plt.title('Origin分布')
        plt.axis('equal')
        plt.savefig('origin_distribution.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("Origin分布图已保存为 origin_distribution.png")

        # 分析不同Origin的KYC完成率
        if 'has_kyc' in df.columns:
            print(f"\n不同Origin的KYC完成率:")
            for origin in origin_stats.index:
                origin_data = df[df['origin'] == origin]
                kyc_rate = origin_data['has_kyc'].sum() / len(origin_data) * 100
                print(f"  {origin}: {kyc_rate:.2f}%")

    else:
        print("未找到 origin 列")

def analyze_language_country_match(df):
    """分析standard_language是否和last_login_country匹配"""
    print("\n" + "="*50)
    print("5. 语言与登录国家匹配分析")
    print("="*50)
    
    if 'standard_language' in df.columns and 'last_login_country' in df.columns:
        # 创建语言-国家映射字典（简化版本）
        language_country_map = {
            'en': ['US', 'UK', 'GB', 'AU', 'CA', 'NZ', 'IE'],
            'zh': ['CN', 'TW', 'HK', 'SG'],
            'ja': ['JP'],
            'ko': ['KR'],
            'es': ['ES', 'MX', 'AR', 'CO', 'PE', 'VE', 'CL'],
            'fr': ['FR', 'CA', 'BE', 'CH'],
            'de': ['DE', 'AT', 'CH'],
            'it': ['IT'],
            'pt': ['PT', 'BR'],
            'ru': ['RU', 'BY', 'KZ'],
            'ar': ['SA', 'AE', 'EG', 'MA'],
            'hi': ['IN'],
            'th': ['TH'],
            'vi': ['VN'],
            'id': ['ID'],
            'ms': ['MY'],
            'tr': ['TR'],
            'pl': ['PL'],
            'nl': ['NL'],
            'sv': ['SE'],
            'no': ['NO'],
            'da': ['DK'],
            'fi': ['FI']
        }
        
        def check_language_country_match(row):
            """检查语言和国家是否匹配"""
            lang = str(row['standard_language']).lower().strip()
            country = str(row['last_login_country']).upper().strip()
            
            if pd.isna(row['standard_language']) or pd.isna(row['last_login_country']):
                return 'Unknown'
            
            # 检查是否匹配
            for language, countries in language_country_map.items():
                if lang.startswith(language) and country in countries:
                    return 'Match'
            
            return 'No Match'
        
        df['language_country_match'] = df.apply(check_language_country_match, axis=1)
        
        match_stats = df['language_country_match'].value_counts()
        print(f"\n语言与登录国家匹配情况:")
        for status, count in match_stats.items():
            percentage = (count / len(df)) * 100
            print(f"  {status}: {count} 条记录 ({percentage:.2f}%)")
        
        # 详细分析不匹配的情况
        no_match_df = df[df['language_country_match'] == 'No Match']
        if len(no_match_df) > 0:
            print(f"\n不匹配情况详细分析 (前10个组合):")
            lang_country_combo = no_match_df.groupby(['standard_language', 'last_login_country']).size().sort_values(ascending=False)
            for (lang, country), count in lang_country_combo.head(10).items():
                print(f"  语言: {lang}, 国家: {country} - {count} 条记录")
                
    else:
        missing_cols = []
        if 'standard_language' not in df.columns:
            missing_cols.append('standard_language')
        if 'last_login_country' not in df.columns:
            missing_cols.append('last_login_country')
        print(f"缺少必要的列: {missing_cols}")

def generate_summary_report(df):
    """生成汇总报告"""
    print("\n" + "="*50)
    print("数据汇总报告")
    print("="*50)
    
    print(f"总记录数: {len(df)}")
    print(f"总列数: {len(df.columns)}")
    print(f"数据时间范围: 需要进一步分析日期列")
    
    # 数据质量检查
    print(f"\n数据质量检查:")
    missing_data = df.isnull().sum()
    for col, missing_count in missing_data.items():
        if missing_count > 0:
            percentage = (missing_count / len(df)) * 100
            print(f"  {col}: {missing_count} 个缺失值 ({percentage:.2f}%)")

def main():
    """主函数"""
    file_path = "/Users/<USER>/Documents/augment-projects/web/归类后的数据.xlsx"
    
    print("开始分析数据...")
    print(f"文件路径: {file_path}")
    
    # 加载数据
    df = load_data(file_path)
    if df is None:
        return
    
    # 执行各项分析
    analyze_monthly_distribution(df)
    analyze_kyc_status(df)
    analyze_member_country(df)
    analyze_origin_distribution(df)
    analyze_language_country_match(df)
    generate_summary_report(df)
    
    print("\n分析完成！")

if __name__ == "__main__":
    main()
