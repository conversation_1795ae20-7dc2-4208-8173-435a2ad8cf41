#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据分析脚本 - 分析归类后的数据.xlsx
分析维度：
1. 按月度分析
2. 按是否有KYC分析
3. 按member_country分析
4. 按origin分布分析
5. 分析standard_language是否和last_login_country匹配
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_data(file_path):
    """加载CSV或Excel数据"""
    try:
        if file_path.endswith('.csv'):
            df = pd.read_csv(file_path)
        else:
            df = pd.read_excel(file_path)
        print(f"数据加载成功，共有 {len(df)} 行数据")
        print(f"列名: {list(df.columns)}")
        return df
    except Exception as e:
        print(f"数据加载失败: {e}")
        return None

def analyze_monthly_distribution(df):
    """按月度分析"""
    print("\n" + "="*50)
    print("1. 月度分析")
    print("="*50)

    # 检查last_login_time列（这应该是日期列）
    if 'last_login_time' in df.columns:
        try:
            # last_login_time格式是"2025/4/5 23:31"（没有秒数）
            df['last_login_time_parsed'] = pd.to_datetime(df['last_login_time'], format='%Y/%m/%d %H:%M', errors='coerce')
            valid_dates = df['last_login_time_parsed'].notna().sum()

            if valid_dates > 0:
                print(f"last_login_time列有效日期数量: {valid_dates}")

                # 显示日期范围
                min_date = df['last_login_time_parsed'].min()
                max_date = df['last_login_time_parsed'].max()
                print(f"数据时间范围: {min_date.strftime('%Y-%m-%d %H:%M:%S')} 到 {max_date.strftime('%Y-%m-%d %H:%M:%S')}")

                # 提取月份
                df['month'] = df['last_login_time_parsed'].dt.to_period('M')
                monthly_count = df['month'].value_counts().sort_index()

                print(f"\n按月分布:")
                for month, count in monthly_count.items():
                    percentage = (count / len(df)) * 100
                    print(f"  {month}: {count} 条记录 ({percentage:.2f}%)")

                # 创建月度分布图
                plt.figure(figsize=(12, 6))
                monthly_count.plot(kind='bar', color='skyblue')
                plt.title('用户数据月度分布')
                plt.xlabel('月份')
                plt.ylabel('记录数量')
                plt.xticks(rotation=45)
                plt.grid(axis='y', alpha=0.3)
                plt.tight_layout()
                plt.savefig('monthly_distribution.png', dpi=300, bbox_inches='tight')
                plt.close()
                print("月度分布图已保存为 monthly_distribution.png")

                # 按日期分析KYC完成率（需要先创建has_kyc列）
                # 判断是否有KYC
                df['has_kyc'] = df['kyc_country'].notna() & (df['kyc_country'] != '') & (df['kyc_country'] != 'None')

                if 'has_kyc' in df.columns:
                    print(f"\n按月KYC完成率:")
                    monthly_kyc = df.groupby('month')['has_kyc'].agg(['count', 'sum', 'mean']).round(4)
                    monthly_kyc.columns = ['总用户数', 'KYC完成数', 'KYC完成率']
                    monthly_kyc['KYC完成率(%)'] = (monthly_kyc['KYC完成率'] * 100).round(2)
                    for month, row in monthly_kyc.iterrows():
                        print(f"  {month}: 总用户{int(row['总用户数'])}, KYC完成{int(row['KYC完成数'])}, 完成率{row['KYC完成率(%)']}%")

                    # 创建月度KYC完成率图表
                    if len(monthly_kyc) > 1:
                        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))

                        # 用户数量图
                        monthly_kyc['总用户数'].plot(kind='bar', ax=ax1, color='lightblue')
                        ax1.set_title('月度用户数量')
                        ax1.set_ylabel('用户数量')
                        ax1.tick_params(axis='x', rotation=45)

                        # KYC完成率图
                        monthly_kyc['KYC完成率(%)'].plot(kind='bar', ax=ax2, color='orange')
                        ax2.set_title('月度KYC完成率')
                        ax2.set_ylabel('KYC完成率 (%)')
                        ax2.tick_params(axis='x', rotation=45)

                        plt.tight_layout()
                        plt.savefig('monthly_kyc_analysis.png', dpi=300, bbox_inches='tight')
                        plt.close()
                        print("月度KYC分析图已保存为 monthly_kyc_analysis.png")

            else:
                print("last_login_time列中没有有效的日期数据")
                print(f"last_login_time列样本数据: {df['last_login_time'].head().tolist()}")

        except Exception as e:
            print(f"处理last_login_time列时出错: {e}")
            print(f"last_login_time列样本数据: {df['last_login_time'].head().tolist()}")
    else:
        print("未找到last_login_time列，无法进行月度分析")

def analyze_kyc_status(df):
    """按是否有KYC分析"""
    print("\n" + "="*50)
    print("2. KYC状态分析")
    print("="*50)

    if 'kyc_country' in df.columns:
        # 判断是否有KYC
        df['has_kyc'] = df['kyc_country'].notna() & (df['kyc_country'] != '') & (df['kyc_country'] != 'None')

        kyc_stats = df['has_kyc'].value_counts()
        print(f"\nKYC状态分布:")
        has_kyc_count = kyc_stats.get(True, 0)
        no_kyc_count = kyc_stats.get(False, 0)
        has_kyc_pct = (has_kyc_count / len(df)) * 100
        no_kyc_pct = (no_kyc_count / len(df)) * 100

        print(f"  有KYC: {has_kyc_count} 条记录 ({has_kyc_pct:.2f}%)")
        print(f"  无KYC: {no_kyc_count} 条记录 ({no_kyc_pct:.2f}%)")

        # 创建KYC状态饼图
        plt.figure(figsize=(8, 6))
        labels = ['有KYC', '无KYC']
        sizes = [has_kyc_count, no_kyc_count]
        colors = ['#ff9999', '#66b3ff']
        plt.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
        plt.title('KYC状态分布')
        plt.axis('equal')
        plt.savefig('kyc_status_distribution.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("KYC状态分布图已保存为 kyc_status_distribution.png")

        # 按KYC状态分析国家分布
        if has_kyc_count > 0:
            print(f"\n有KYC用户的国家分布:")
            kyc_countries = df[df['has_kyc']]['kyc_country'].value_counts()
            for country, count in kyc_countries.head(10).items():
                percentage = (count / has_kyc_count) * 100
                print(f"  {country}: {count} 条记录 ({percentage:.2f}%)")

            # 创建有KYC用户的国家分布图
            plt.figure(figsize=(12, 8))
            top_kyc_countries = kyc_countries.head(15)
            top_kyc_countries.plot(kind='bar')
            plt.title('有KYC用户的国家分布 (前15名)')
            plt.xlabel('国家')
            plt.ylabel('用户数量')
            plt.xticks(rotation=45)
            plt.tight_layout()
            plt.savefig('kyc_countries_distribution.png', dpi=300, bbox_inches='tight')
            plt.close()
            print("有KYC用户国家分布图已保存为 kyc_countries_distribution.png")

    else:
        print("未找到 kyc_country 列")

def analyze_member_country(df):
    """按member_country分析"""
    print("\n" + "="*50)
    print("3. 会员国家分析")
    print("="*50)

    if 'member_country' in df.columns:
        country_stats = df['member_country'].value_counts()
        print(f"\n会员国家分布 (前20名):")
        for country, count in country_stats.head(20).items():
            percentage = (count / len(df)) * 100
            print(f"  {country}: {count} 条记录 ({percentage:.2f}%)")

        print(f"\n总共有 {len(country_stats)} 个不同的国家")

        # 创建会员国家分布图
        plt.figure(figsize=(15, 10))
        top_countries = country_stats.head(20)
        top_countries.plot(kind='bar')
        plt.title('会员国家分布 (前20名)')
        plt.xlabel('国家')
        plt.ylabel('用户数量')
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig('member_countries_distribution.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("会员国家分布图已保存为 member_countries_distribution.png")

        # 分析前5名国家的详细信息
        print(f"\n前5名国家详细分析:")
        top5_countries = country_stats.head(5)
        for country in top5_countries.index:
            country_data = df[df['member_country'] == country]
            kyc_rate = country_data['has_kyc'].sum() / len(country_data) * 100 if 'has_kyc' in df.columns else 0
            print(f"\n{country}:")
            print(f"  总用户数: {len(country_data)}")
            print(f"  KYC完成率: {kyc_rate:.2f}%")

            # 分析该国家的origin分布
            if 'origin' in df.columns:
                origin_dist = country_data['origin'].value_counts()
                print(f"  主要来源: {origin_dist.head(3).to_dict()}")

    else:
        print("未找到 member_country 列")

def analyze_origin_distribution(df):
    """按origin分布分析"""
    print("\n" + "="*50)
    print("4. Origin分布分析")
    print("="*50)

    if 'origin' in df.columns:
        origin_stats = df['origin'].value_counts()
        print(f"\nOrigin分布:")
        for origin, count in origin_stats.items():
            percentage = (count / len(df)) * 100
            print(f"  {origin}: {count} 条记录 ({percentage:.2f}%)")

        # 创建Origin分布饼图
        plt.figure(figsize=(12, 10))
        colors = ['#ff9999', '#66b3ff', '#99ff99', '#ffcc99', '#ff99cc', '#c2c2f0', '#ffb3e6']

        # 只显示占比大于1%的项目，其他合并为"其他"
        main_origins = origin_stats[origin_stats > len(df) * 0.01]
        other_count = origin_stats[origin_stats <= len(df) * 0.01].sum()

        if other_count > 0:
            display_data = main_origins.copy()
            display_data['其他'] = other_count
        else:
            display_data = main_origins

        # 创建饼图，调整字体大小和标签位置
        wedges, texts, autotexts = plt.pie(display_data.values,
                                          labels=display_data.index,
                                          colors=colors[:len(display_data)],
                                          autopct='%1.1f%%',
                                          startangle=90,
                                          textprops={'fontsize': 12},
                                          pctdistance=0.85)

        # 调整百分比文字的字体大小
        for autotext in autotexts:
            autotext.set_color('white')
            autotext.set_fontweight('bold')
            autotext.set_fontsize(11)

        # 调整标签文字的字体大小
        for text in texts:
            text.set_fontsize(12)

        plt.title('Origin分布', fontsize=16, fontweight='bold', pad=20)
        plt.axis('equal')
        plt.tight_layout()
        plt.savefig('origin_distribution.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("Origin分布图已保存为 origin_distribution.png")

        # 分析不同Origin的KYC完成率
        if 'has_kyc' in df.columns:
            print(f"\n不同Origin的KYC完成率:")
            for origin in origin_stats.index:
                origin_data = df[df['origin'] == origin]
                kyc_rate = origin_data['has_kyc'].sum() / len(origin_data) * 100
                print(f"  {origin}: {kyc_rate:.2f}%")

    else:
        print("未找到 origin 列")

def analyze_language_country_match(df):
    """分析standard_language是否和last_login_country匹配"""
    print("\n" + "="*50)
    print("5. 语言与登录国家匹配分析")
    print("="*50)
    
    if 'standard_language' in df.columns and 'last_login_country' in df.columns:
        # 创建语言-国家映射字典（简化版本）
        language_country_map = {
            'en': ['US', 'UK', 'GB', 'AU', 'CA', 'NZ', 'IE'],
            'zh': ['CN', 'TW', 'HK', 'SG'],
            'ja': ['JP'],
            'ko': ['KR'],
            'es': ['ES', 'MX', 'AR', 'CO', 'PE', 'VE', 'CL'],
            'fr': ['FR', 'CA', 'BE', 'CH'],
            'de': ['DE', 'AT', 'CH'],
            'it': ['IT'],
            'pt': ['PT', 'BR'],
            'ru': ['RU', 'BY', 'KZ'],
            'ar': ['SA', 'AE', 'EG', 'MA'],
            'hi': ['IN'],
            'th': ['TH'],
            'vi': ['VN'],
            'id': ['ID'],
            'ms': ['MY'],
            'tr': ['TR'],
            'pl': ['PL'],
            'nl': ['NL'],
            'sv': ['SE'],
            'no': ['NO'],
            'da': ['DK'],
            'fi': ['FI']
        }
        
        def check_language_country_match(row):
            """检查语言和国家是否匹配"""
            lang = str(row['standard_language']).lower().strip()
            country = str(row['last_login_country']).upper().strip()
            
            if pd.isna(row['standard_language']) or pd.isna(row['last_login_country']):
                return 'Unknown'
            
            # 检查是否匹配
            for language, countries in language_country_map.items():
                if lang.startswith(language) and country in countries:
                    return 'Match'
            
            return 'No Match'
        
        df['language_country_match'] = df.apply(check_language_country_match, axis=1)
        
        match_stats = df['language_country_match'].value_counts()
        print(f"\n语言与登录国家匹配情况:")
        for status, count in match_stats.items():
            percentage = (count / len(df)) * 100
            print(f"  {status}: {count} 条记录 ({percentage:.2f}%)")
        
        # 详细分析不匹配的情况
        no_match_df = df[df['language_country_match'] == 'No Match']
        if len(no_match_df) > 0:
            print(f"\n不匹配情况详细分析 (前10个组合):")
            lang_country_combo = no_match_df.groupby(['standard_language', 'last_login_country']).size().sort_values(ascending=False)
            for (lang, country), count in lang_country_combo.head(10).items():
                print(f"  语言: {lang}, 国家: {country} - {count} 条记录")
                
    else:
        missing_cols = []
        if 'standard_language' not in df.columns:
            missing_cols.append('standard_language')
        if 'last_login_country' not in df.columns:
            missing_cols.append('last_login_country')
        print(f"缺少必要的列: {missing_cols}")

def generate_summary_report(df):
    """生成汇总报告"""
    print("\n" + "="*50)
    print("数据汇总报告")
    print("="*50)

    print(f"总记录数: {len(df)}")
    print(f"总列数: {len(df.columns)}")

    # 显示时间范围
    if 'last_login_time_parsed' in df.columns and df['last_login_time_parsed'].notna().sum() > 0:
        min_date = df['last_login_time_parsed'].min()
        max_date = df['last_login_time_parsed'].max()
        print(f"数据时间范围: {min_date.strftime('%Y-%m-%d %H:%M:%S')} 到 {max_date.strftime('%Y-%m-%d %H:%M:%S')}")

        # 计算数据跨度
        date_span = (max_date - min_date).days
        print(f"数据跨度: {date_span} 天")
    else:
        print(f"数据时间范围: 无法解析")

    # 数据质量检查
    print(f"\n数据质量检查:")
    missing_data = df.isnull().sum()
    for col, missing_count in missing_data.items():
        if missing_count > 0:
            percentage = (missing_count / len(df)) * 100
            print(f"  {col}: {missing_count} 个缺失值 ({percentage:.2f}%)")

    # 显示各国家用户数量统计
    if 'member_country' in df.columns:
        unique_countries = df['member_country'].nunique()
        print(f"\n地理分布:")
        print(f"  覆盖国家数: {unique_countries} 个")

    # 显示平台分布统计
    if 'origin' in df.columns:
        unique_origins = df['origin'].nunique()
        print(f"  平台类型数: {unique_origins} 个")

    # KYC整体统计
    if 'has_kyc' in df.columns:
        kyc_rate = df['has_kyc'].mean() * 100
        print(f"\n业务指标:")
        print(f"  整体KYC完成率: {kyc_rate:.2f}%")

def analyze_bd_distribution(df):
    """分析BD维度数据"""
    print("\n" + "="*50)
    print("6. BD维度分析")
    print("="*50)

    # 检查可能的BD相关列
    bd_columns = [col for col in df.columns if 'bd' in col.lower()]
    print(f"发现BD相关列: {bd_columns}")

    # 首先分析直接代理 vs 间接代理
    if 'recommender_digital_id' in df.columns and 'top_kol_digital_id' in df.columns:
        print(f"\n=== 直接代理 vs 间接代理分析 ===")

        # 创建代理类型字段
        df['agent_type'] = 'Unknown'

        # 有推荐人和KOL数据的用户
        valid_data = df[(df['recommender_digital_id'].notna()) & (df['top_kol_digital_id'].notna())]

        # 判断直接代理和间接代理
        direct_mask = valid_data['recommender_digital_id'] == valid_data['top_kol_digital_id']
        indirect_mask = valid_data['recommender_digital_id'] != valid_data['top_kol_digital_id']

        df.loc[valid_data[direct_mask].index, 'agent_type'] = '直接代理'
        df.loc[valid_data[indirect_mask].index, 'agent_type'] = '间接代理'

        # 统计代理类型分布
        agent_stats = df['agent_type'].value_counts()
        print(f"\n代理类型分布:")
        for agent_type, count in agent_stats.items():
            percentage = (count / len(df)) * 100
            print(f"  {agent_type}: {count} 条记录 ({percentage:.2f}%)")

        # 创建代理类型分布图
        plt.figure(figsize=(10, 6))
        agent_stats_filtered = agent_stats[agent_stats.index != 'Unknown']
        colors = ['#ff9999', '#66b3ff', '#99ff99']
        agent_stats_filtered.plot(kind='bar', color=colors[:len(agent_stats_filtered)])
        plt.title('直接代理 vs 间接代理分布')
        plt.xlabel('代理类型')
        plt.ylabel('用户数量')
        plt.xticks(rotation=0)
        plt.tight_layout()
        plt.savefig('agent_type_distribution.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("代理类型分布图已保存为 agent_type_distribution.png")

        # 分析不同代理类型的KYC完成率
        if 'has_kyc' in df.columns:
            print(f"\n不同代理类型的KYC完成率:")
            agent_kyc_stats = df[df['agent_type'] != 'Unknown'].groupby('agent_type')['has_kyc'].agg(['count', 'sum', 'mean']).round(4)
            agent_kyc_stats.columns = ['总用户数', 'KYC完成数', 'KYC完成率']
            agent_kyc_stats['KYC完成率(%)'] = (agent_kyc_stats['KYC完成率'] * 100).round(2)

            for agent_type, row in agent_kyc_stats.iterrows():
                print(f"  {agent_type}: 总用户{int(row['总用户数'])}, KYC完成{int(row['KYC完成数'])}, 完成率{row['KYC完成率(%)']}%")

        # 分析不同代理类型的国家分布
        if 'member_country' in df.columns:
            print(f"\n不同代理类型的主要国家分布:")
            for agent_type in ['直接代理', '间接代理']:
                agent_data = df[df['agent_type'] == agent_type]
                if len(agent_data) > 0:
                    country_dist = agent_data['member_country'].value_counts().head(5)
                    countries_str = ', '.join([f"{country}({count})" for country, count in country_dist.items()])
                    print(f"  {agent_type}: {countries_str}")

        # 分析不同代理类型的平台分布
        if 'origin' in df.columns:
            print(f"\n不同代理类型的平台分布:")
            for agent_type in ['直接代理', '间接代理']:
                agent_data = df[df['agent_type'] == agent_type]
                if len(agent_data) > 0:
                    origin_dist = agent_data['origin'].value_counts()
                    origins_str = ', '.join([f"{origin}({count})" for origin, count in origin_dist.items()])
                    print(f"  {agent_type}: {origins_str}")

    bd_col = None
    if 'top_kol_bd_name' in df.columns:
        bd_col = 'top_kol_bd_name'
    elif 'bd' in df.columns:
        bd_col = 'bd'

    if bd_col:
        # 过滤掉空值
        bd_data = df[df[bd_col].notna()]
        bd_stats = bd_data[bd_col].value_counts()

        print(f"\n使用字段: {bd_col}")
        print(f"有BD数据的用户: {len(bd_data)} 条记录 ({len(bd_data)/len(df)*100:.2f}%)")
        print(f"无BD数据的用户: {len(df) - len(bd_data)} 条记录 ({(len(df) - len(bd_data))/len(df)*100:.2f}%)")

        print(f"\nBD分布 (前20名):")
        for bd, count in bd_stats.head(20).items():
            percentage = (count / len(bd_data)) * 100
            print(f"  {bd}: {count} 条记录 ({percentage:.2f}%)")

        # 创建BD分布图
        plt.figure(figsize=(12, 8))
        if len(bd_stats) <= 20:  # 如果BD数量不多，显示全部
            bd_stats.plot(kind='bar', color='lightcoral')
        else:  # 如果BD很多，只显示前20名
            bd_stats.head(20).plot(kind='bar', color='lightcoral')
        plt.title('BD分布')
        plt.xlabel('BD')
        plt.ylabel('用户数量')
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig('bd_distribution.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("BD分布图已保存为 bd_distribution.png")

        # 分析不同BD的KYC完成率
        if 'has_kyc' in df.columns:
            print(f"\n不同BD的KYC完成率 (前20名):")
            bd_kyc_stats = bd_data.groupby(bd_col)['has_kyc'].agg(['count', 'sum', 'mean']).round(4)
            bd_kyc_stats.columns = ['总用户数', 'KYC完成数', 'KYC完成率']
            bd_kyc_stats['KYC完成率(%)'] = (bd_kyc_stats['KYC完成率'] * 100).round(2)
            bd_kyc_stats = bd_kyc_stats.sort_values('KYC完成率(%)', ascending=False)

            for bd, row in bd_kyc_stats.head(15).iterrows():
                print(f"  {bd}: 总用户{int(row['总用户数'])}, KYC完成{int(row['KYC完成数'])}, 完成率{row['KYC完成率(%)']}%")

            # 创建BD KYC完成率图表
            if len(bd_kyc_stats) > 0:
                plt.figure(figsize=(15, 10))
                top_bd_kyc = bd_kyc_stats.head(20)

                fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 12))

                # 用户数量图
                top_bd_kyc['总用户数'].plot(kind='bar', ax=ax1, color='skyblue')
                ax1.set_title('各BD用户数量 (前20名)')
                ax1.set_ylabel('用户数量')
                ax1.tick_params(axis='x', rotation=45)

                # KYC完成率图
                top_bd_kyc['KYC完成率(%)'].plot(kind='bar', ax=ax2, color='orange')
                ax2.set_title('各BD的KYC完成率 (前20名)')
                ax2.set_ylabel('KYC完成率 (%)')
                ax2.tick_params(axis='x', rotation=45)

                plt.tight_layout()
                plt.savefig('bd_kyc_analysis.png', dpi=300, bbox_inches='tight')
                plt.close()
                print("BD KYC分析图已保存为 bd_kyc_analysis.png")

        # 分析BD与国家的关系
        if 'member_country' in df.columns:
            print(f"\nBD与国家分布关系 (前10个BD):")
            top_bds = bd_stats.head(10).index
            for bd in top_bds:
                bd_user_data = bd_data[bd_data[bd_col] == bd]
                country_dist = bd_user_data['member_country'].value_counts().head(5)
                countries_str = ', '.join([f"{country}({count})" for country, count in country_dist.items()])
                print(f"  {bd}: {countries_str}")

        # 分析BD与平台的关系
        if 'origin' in df.columns:
            print(f"\nBD与平台分布关系 (前10个BD):")
            for bd in top_bds:
                bd_user_data = bd_data[bd_data[bd_col] == bd]
                origin_dist = bd_user_data['origin'].value_counts()
                origins_str = ', '.join([f"{origin}({count})" for origin, count in origin_dist.items()])
                print(f"  {bd}: {origins_str}")

        # 分析BD的月度表现
        if 'month' in df.columns:
            print(f"\n主要BD的月度用户增长 (前5个BD):")
            top5_bds = bd_stats.head(5).index
            for bd in top5_bds:
                bd_user_data = bd_data[bd_data[bd_col] == bd]
                monthly_dist = bd_user_data['month'].value_counts().sort_index()
                print(f"\n  {bd} 月度分布:")
                for month, count in monthly_dist.tail(6).items():  # 显示最近6个月
                    print(f"    {month}: {count} 条记录")

        # 分析BD与代理类型的关系
        if 'agent_type' in df.columns:
            print(f"\n主要BD的代理类型分布 (前10个BD):")
            top10_bds = bd_stats.head(10).index
            for bd in top10_bds:
                bd_user_data = bd_data[bd_data[bd_col] == bd]
                agent_dist = bd_user_data['agent_type'].value_counts()
                agent_str = ', '.join([f"{agent_type}({count})" for agent_type, count in agent_dist.items() if agent_type != 'Unknown'])
                if agent_str:  # 只显示有代理数据的BD
                    print(f"  {bd}: {agent_str}")

        # 创建BD代理类型交叉分析图表
        if 'agent_type' in df.columns and len(bd_stats) > 0:
            print(f"\n生成BD代理类型交叉分析图表...")
            top10_bds = bd_stats.head(10).index
            bd_agent_matrix = []

            for bd in top10_bds:
                bd_user_data = bd_data[bd_data[bd_col] == bd]
                direct_count = len(bd_user_data[bd_user_data['agent_type'] == '直接代理'])
                indirect_count = len(bd_user_data[bd_user_data['agent_type'] == '间接代理'])
                bd_agent_matrix.append([direct_count, indirect_count])

            if any(sum(row) > 0 for row in bd_agent_matrix):  # 确保有数据
                bd_agent_df = pd.DataFrame(bd_agent_matrix,
                                         index=top10_bds,
                                         columns=['直接代理', '间接代理'])

                plt.figure(figsize=(12, 8))
                bd_agent_df.plot(kind='bar', stacked=True, color=['#ff9999', '#66b3ff'])
                plt.title('主要BD的代理类型分布')
                plt.xlabel('BD')
                plt.ylabel('用户数量')
                plt.xticks(rotation=45)
                plt.legend(title='代理类型')
                plt.tight_layout()
                plt.savefig('bd_agent_type_analysis.png', dpi=300, bbox_inches='tight')
                plt.close()
                print("BD代理类型分析图已保存为 bd_agent_type_analysis.png")

    else:
        print("未找到 BD 相关列")

def analyze_withdrawal_amount(df):
    """分析提现金额维度"""
    print("\n" + "="*50)
    print("7. 提现金额分析")
    print("="*50)

    if 'amount' in df.columns and 'member_id' in df.columns:
        # 按member_id累加提现金额
        member_withdrawal = df.groupby('member_id')['amount'].agg(['sum', 'count', 'mean']).round(2)
        member_withdrawal.columns = ['总提现金额', '提现次数', '平均提现金额']
        member_withdrawal = member_withdrawal.sort_values('总提现金额', ascending=False)

        print(f"\n提现金额统计:")
        print(f"  总用户数: {len(member_withdrawal)}")
        print(f"  总提现金额: ${member_withdrawal['总提现金额'].sum():,.2f}")
        print(f"  平均每用户提现: ${member_withdrawal['总提现金额'].mean():,.2f}")
        print(f"  提现金额中位数: ${member_withdrawal['总提现金额'].median():,.2f}")

        # 提现金额分层分析
        print(f"\n提现金额分层分析:")
        bins = [0, 100, 500, 1000, 5000, 10000, float('inf')]
        labels = ['$0-100', '$100-500', '$500-1K', '$1K-5K', '$5K-10K', '$10K+']
        member_withdrawal['金额分层'] = pd.cut(member_withdrawal['总提现金额'], bins=bins, labels=labels, right=False)

        amount_distribution = member_withdrawal['金额分层'].value_counts().sort_index()
        for tier, count in amount_distribution.items():
            percentage = (count / len(member_withdrawal)) * 100
            tier_amount = member_withdrawal[member_withdrawal['金额分层'] == tier]['总提现金额'].sum()
            print(f"  {tier}: {count} 用户 ({percentage:.2f}%), 总金额: ${tier_amount:,.2f}")

        # 创建提现金额分布图
        plt.figure(figsize=(12, 8))
        amount_distribution.plot(kind='bar', color='lightgreen')
        plt.title('用户提现金额分层分布')
        plt.xlabel('提现金额区间')
        plt.ylabel('用户数量')
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig('withdrawal_amount_distribution.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("提现金额分布图已保存为 withdrawal_amount_distribution.png")

        # 分析提现金额与KYC的关系
        if 'has_kyc' in df.columns:
            # 需要将提现数据与用户数据合并
            available_cols = ['member_id', 'has_kyc']
            if 'member_country' in df.columns:
                available_cols.append('member_country')
            if 'origin_y' in df.columns:
                available_cols.append('origin_y')
            elif 'origin' in df.columns:
                available_cols.append('origin')
            if 'agent_type' in df.columns:
                available_cols.append('agent_type')

            user_data = df.drop_duplicates('member_id')[available_cols].copy()
            withdrawal_kyc = member_withdrawal.merge(user_data, left_index=True, right_on='member_id', how='left')

            print(f"\n提现金额与KYC关系:")
            kyc_withdrawal = withdrawal_kyc.groupby('has_kyc')['总提现金额'].agg(['count', 'sum', 'mean']).round(2)
            kyc_withdrawal.columns = ['用户数', '总金额', '平均金额']

            for kyc_status, row in kyc_withdrawal.iterrows():
                status_name = "有KYC" if kyc_status else "无KYC"
                print(f"  {status_name}: {int(row['用户数'])} 用户, 总金额: ${row['总金额']:,.2f}, 平均: ${row['平均金额']:,.2f}")

            # 分析不同金额分层的KYC完成率
            print(f"\n不同提现金额分层的KYC完成率:")
            tier_kyc = withdrawal_kyc.groupby('金额分层')['has_kyc'].agg(['count', 'sum', 'mean']).round(4)
            tier_kyc.columns = ['总用户数', 'KYC完成数', 'KYC完成率']
            tier_kyc['KYC完成率(%)'] = (tier_kyc['KYC完成率'] * 100).round(2)

            for tier, row in tier_kyc.iterrows():
                print(f"  {tier}: 总用户{int(row['总用户数'])}, KYC完成{int(row['KYC完成数'])}, 完成率{row['KYC完成率(%)']}%")

        # 分析提现金额与国家的关系
        if 'member_country' in df.columns:
            print(f"\n主要国家的提现金额分析 (前10名):")
            user_data = df.drop_duplicates('member_id')[['member_id', 'member_country']].copy()
            withdrawal_country = member_withdrawal.merge(user_data, left_index=True, right_on='member_id', how='left')

            country_withdrawal = withdrawal_country.groupby('member_country')['总提现金额'].agg(['count', 'sum', 'mean']).round(2)
            country_withdrawal.columns = ['用户数', '总金额', '平均金额']
            country_withdrawal = country_withdrawal.sort_values('总金额', ascending=False)

            for country, row in country_withdrawal.head(10).iterrows():
                print(f"  {country}: {int(row['用户数'])} 用户, 总金额: ${row['总金额']:,.2f}, 平均: ${row['平均金额']:,.2f}")

        # 分析提现金额与代理类型的关系
        if 'agent_type' in df.columns:
            print(f"\n不同代理类型的提现金额分析:")
            user_data_agent = df.drop_duplicates('member_id')[['member_id', 'agent_type']].copy()
            withdrawal_agent = member_withdrawal.merge(user_data_agent, left_index=True, right_on='member_id', how='left')

            agent_withdrawal = withdrawal_agent[withdrawal_agent['agent_type'] != 'Unknown'].groupby('agent_type')['总提现金额'].agg(['count', 'sum', 'mean']).round(2)
            agent_withdrawal.columns = ['用户数', '总金额', '平均金额']

            for agent_type, row in agent_withdrawal.iterrows():
                print(f"  {agent_type}: {int(row['用户数'])} 用户, 总金额: ${row['总金额']:,.2f}, 平均: ${row['平均金额']:,.2f}")

        # 分析BD的提现金额情况
        if 'top_kol_bd_name' in df.columns:
            print(f"\n=== BD提现金额分析 ===")

            # 获取有BD数据的用户
            bd_users = df[df['top_kol_bd_name'].notna()].copy()

            # 按BD和member_id聚合提现数据
            bd_member_withdrawal = bd_users.groupby(['top_kol_bd_name', 'member_id'])['amount'].sum().reset_index()
            bd_member_withdrawal.columns = ['BD', 'member_id', '用户总提现金额']

            # 按BD聚合统计
            bd_withdrawal_stats = bd_member_withdrawal.groupby('BD').agg({
                'member_id': 'count',
                '用户总提现金额': ['sum', 'mean', 'median', 'max', 'min']
            }).round(2)

            bd_withdrawal_stats.columns = ['用户数', '总提现金额', '平均提现金额', '中位数提现金额', '最大提现金额', '最小提现金额']
            bd_withdrawal_stats = bd_withdrawal_stats.sort_values('总提现金额', ascending=False)

            print(f"\nBD提现金额排名 (前20名):")
            for i, (bd, row) in enumerate(bd_withdrawal_stats.head(20).iterrows(), 1):
                print(f"  {i}. {bd}:")
                print(f"     用户数: {int(row['用户数'])}")
                print(f"     总提现金额: ${row['总提现金额']:,.2f}")
                print(f"     平均提现金额: ${row['平均提现金额']:,.2f}")
                print(f"     中位数提现金额: ${row['中位数提现金额']:,.2f}")
                print(f"     最大提现金额: ${row['最大提现金额']:,.2f}")
                print("")

            # 创建BD提现金额对比图
            plt.figure(figsize=(15, 10))
            top_bd_withdrawal = bd_withdrawal_stats.head(15)

            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 12))

            # BD总提现金额图
            top_bd_withdrawal['总提现金额'].plot(kind='bar', ax=ax1, color='lightblue')
            ax1.set_title('各BD总提现金额 (前15名)')
            ax1.set_ylabel('总提现金额 ($)')
            ax1.tick_params(axis='x', rotation=45)
            ax1.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x/1e9:.1f}B' if x >= 1e9 else f'${x/1e6:.1f}M'))

            # BD平均提现金额图
            top_bd_withdrawal['平均提现金额'].plot(kind='bar', ax=ax2, color='orange')
            ax2.set_title('各BD平均提现金额 (前15名)')
            ax2.set_ylabel('平均提现金额 ($)')
            ax2.tick_params(axis='x', rotation=45)
            ax2.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x/1e6:.1f}M' if x >= 1e6 else f'${x/1e3:.1f}K'))

            plt.tight_layout()
            plt.savefig('bd_withdrawal_analysis.png', dpi=300, bbox_inches='tight')
            plt.close()
            print("BD提现金额分析图已保存为 bd_withdrawal_analysis.png")

            # 分析BD提现金额与用户数的关系
            print(f"\nBD效率分析 (平均提现金额前10名):")
            bd_efficiency = bd_withdrawal_stats.sort_values('平均提现金额', ascending=False)
            for i, (bd, row) in enumerate(bd_efficiency.head(10).iterrows(), 1):
                efficiency = row['总提现金额'] / row['用户数'] if row['用户数'] > 0 else 0
                print(f"  {i}. {bd}: 平均${efficiency:,.2f}/用户 ({int(row['用户数'])}用户)")

            # 分析BD的提现金额分布
            print(f"\nBD提现金额分层分析:")
            bd_withdrawal_stats['金额分层'] = pd.cut(bd_withdrawal_stats['总提现金额'],
                                                bins=[0, 1e6, 1e7, 1e8, 1e9, float('inf')],
                                                labels=['<$1M', '$1M-10M', '$10M-100M', '$100M-1B', '>$1B'])

            tier_distribution = bd_withdrawal_stats['金额分层'].value_counts().sort_index()
            for tier, count in tier_distribution.items():
                tier_bds = bd_withdrawal_stats[bd_withdrawal_stats['金额分层'] == tier]
                tier_total = tier_bds['总提现金额'].sum()
                print(f"  {tier}: {count} 个BD, 总金额: ${tier_total:,.2f}")

            # 分析各BD的大额用户情况
            print(f"\n各BD的大额用户分析 (>$10K的用户):")
            for bd in bd_withdrawal_stats.head(10).index:
                bd_data = bd_member_withdrawal[bd_member_withdrawal['BD'] == bd]
                big_users = bd_data[bd_data['用户总提现金额'] > 10000]
                if len(big_users) > 0:
                    big_user_amount = big_users['用户总提现金额'].sum()
                    big_user_pct = (big_user_amount / bd_data['用户总提现金额'].sum()) * 100
                    print(f"  {bd}: {len(big_users)}个大额用户, 占该BD总金额的{big_user_pct:.1f}%")

        # 分析大额提现用户
        print(f"\n大额提现用户分析 (前10名):")
        top_withdrawers = member_withdrawal.head(10)
        for i, (member_id, row) in enumerate(top_withdrawers.iterrows(), 1):
            print(f"  {i}. Member {member_id}: ${row['总提现金额']:,.2f} ({int(row['提现次数'])}次)")

    else:
        missing_cols = []
        if 'amount' not in df.columns:
            missing_cols.append('amount')
        if 'member_id' not in df.columns:
            missing_cols.append('member_id')
        print(f"缺少必要的列: {missing_cols}")

def main():
    """主函数"""
    file_path = "/Users/<USER>/Documents/augment-projects/web/基于member_id匹配的完整数据.csv"

    print("开始分析数据...")
    print(f"文件路径: {file_path}")
    
    # 加载数据
    df = load_data(file_path)
    if df is None:
        return
    
    # 执行各项分析
    analyze_monthly_distribution(df)
    analyze_kyc_status(df)
    analyze_member_country(df)
    analyze_origin_distribution(df)
    analyze_language_country_match(df)
    analyze_bd_distribution(df)
    analyze_withdrawal_amount(df)
    generate_summary_report(df)
    
    print("\n分析完成！")

if __name__ == "__main__":
    main()
