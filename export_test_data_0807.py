#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导出8月7号上午11点以后的测试数据
"""

import sqlite3
import json
from datetime import datetime, timedelta
import os

def connect_database():
    """连接数据库"""
    db_path = 'wash_trading_test.db'
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return None
    
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row  # 使结果可以按列名访问
        print(f"✅ 成功连接数据库: {db_path}")
        return conn
    except Exception as e:
        print(f"❌ 连接数据库失败: {e}")
        return None

def get_table_info(conn):
    """获取数据库表信息"""
    print("🔍 查看数据库表结构...")
    
    try:
        cursor = conn.cursor()
        
        # 获取所有表名
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print(f"📊 数据库中的表:")
        for table in tables:
            table_name = table[0]
            print(f"  - {table_name}")
            
            # 获取表结构
            cursor.execute(f"PRAGMA table_info({table_name});")
            columns = cursor.fetchall()
            
            print(f"    列信息:")
            for col in columns:
                print(f"      {col[1]} ({col[2]})")
            
            # 获取记录数
            cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
            count = cursor.fetchone()[0]
            print(f"    记录数: {count}")
            print()
        
        return [table[0] for table in tables]
        
    except Exception as e:
        print(f"❌ 获取表信息失败: {e}")
        return []

def export_test_data_after_time(conn, target_time="2025-08-07 11:00:00"):
    """导出指定时间后的测试数据"""
    print(f"📊 导出 {target_time} 以后的测试数据...")
    
    try:
        cursor = conn.cursor()
        
        # 查找可能的测试数据表
        test_tables = []
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        for table in tables:
            table_name = table[0]
            # 检查表是否包含时间相关字段
            cursor.execute(f"PRAGMA table_info({table_name});")
            columns = cursor.fetchall()
            
            time_columns = []
            for col in columns:
                col_name = col[1].lower()
                if any(keyword in col_name for keyword in ['time', 'date', 'created', 'updated']):
                    time_columns.append(col[1])
            
            if time_columns:
                test_tables.append((table_name, time_columns))
        
        print(f"🔍 找到包含时间字段的表: {len(test_tables)} 个")
        
        all_exported_data = {}
        
        for table_name, time_columns in test_tables:
            print(f"\n📋 处理表: {table_name}")
            print(f"  时间字段: {time_columns}")
            
            # 尝试每个时间字段
            for time_col in time_columns:
                try:
                    # 构建查询SQL
                    sql = f"""
                    SELECT * FROM {table_name} 
                    WHERE {time_col} >= ? 
                    ORDER BY {time_col} DESC
                    """
                    
                    cursor.execute(sql, (target_time,))
                    rows = cursor.fetchall()
                    
                    if rows:
                        print(f"  ✅ 通过 {time_col} 字段找到 {len(rows)} 条记录")
                        
                        # 转换为字典列表
                        records = []
                        for row in rows:
                            record = dict(row)
                            records.append(record)
                        
                        table_key = f"{table_name}_by_{time_col}"
                        all_exported_data[table_key] = {
                            'table_name': table_name,
                            'time_column': time_col,
                            'filter_time': target_time,
                            'record_count': len(records),
                            'records': records
                        }
                        
                        # 显示前几条记录的时间信息
                        print(f"  📅 时间范围:")
                        if len(records) > 0:
                            first_time = records[-1].get(time_col, 'N/A')  # 最早的
                            last_time = records[0].get(time_col, 'N/A')   # 最新的
                            print(f"    最早: {first_time}")
                            print(f"    最新: {last_time}")
                        
                        break  # 找到数据就跳出时间字段循环
                        
                except Exception as e:
                    print(f"  ⚠️ 查询 {time_col} 字段失败: {e}")
                    continue
        
        return all_exported_data
        
    except Exception as e:
        print(f"❌ 导出数据失败: {e}")
        return {}

def save_exported_data(exported_data, filename="test_data_0807_after_11am.json"):
    """保存导出的数据"""
    if not exported_data:
        print("❌ 没有数据需要保存")
        return
    
    try:
        # 创建完整的导出数据结构
        export_package = {
            'export_info': {
                'export_time': datetime.now().isoformat(),
                'filter_condition': '2025-08-07 11:00:00 以后',
                'total_tables': len(exported_data),
                'total_records': sum(data['record_count'] for data in exported_data.values())
            },
            'tables': exported_data
        }
        
        # 保存为JSON文件
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(export_package, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"✅ 数据已保存到: {filename}")
        
        # 打印导出摘要
        print(f"\n📊 导出摘要:")
        print(f"  导出时间: {export_package['export_info']['export_time']}")
        print(f"  过滤条件: {export_package['export_info']['filter_condition']}")
        print(f"  表数量: {export_package['export_info']['total_tables']}")
        print(f"  总记录数: {export_package['export_info']['total_records']}")
        
        print(f"\n📋 各表详情:")
        for table_key, data in exported_data.items():
            print(f"  {data['table_name']} (通过{data['time_column']}): {data['record_count']} 条")
        
        return filename
        
    except Exception as e:
        print(f"❌ 保存数据失败: {e}")
        return None

def analyze_recent_test_data(exported_data):
    """分析最近的测试数据"""
    print(f"\n🔍 分析最近的测试数据...")
    
    if not exported_data:
        print("❌ 没有数据可分析")
        return
    
    for table_key, data in exported_data.items():
        table_name = data['table_name']
        records = data['records']
        
        print(f"\n📊 表 {table_name} 分析:")
        print(f"  记录数: {len(records)}")
        
        if not records:
            continue
        
        # 分析记录结构
        sample_record = records[0]
        print(f"  字段数: {len(sample_record)}")
        print(f"  主要字段: {list(sample_record.keys())[:10]}")
        
        # 查找可能的关键字段
        key_fields = []
        for field in sample_record.keys():
            field_lower = field.lower()
            if any(keyword in field_lower for keyword in ['id', 'amount', 'price', 'symbol', 'coin', 'type', 'status']):
                key_fields.append(field)
        
        if key_fields:
            print(f"  关键字段: {key_fields}")
        
        # 显示最新的几条记录
        print(f"  最新记录示例:")
        for i, record in enumerate(records[:3]):
            print(f"    记录 {i+1}:")
            for field in key_fields[:5]:  # 只显示前5个关键字段
                value = record.get(field, 'N/A')
                print(f"      {field}: {value}")

def main():
    """主函数"""
    print("🚀 开始导出8月7号上午11点以后的测试数据...")
    print("="*60)
    
    # 1. 连接数据库
    conn = connect_database()
    if not conn:
        return
    
    try:
        # 2. 获取表信息
        tables = get_table_info(conn)
        
        # 3. 导出指定时间后的数据
        target_time = "2025-08-07 11:00:00"
        exported_data = export_test_data_after_time(conn, target_time)
        
        # 4. 保存导出的数据
        if exported_data:
            filename = save_exported_data(exported_data)
            
            # 5. 分析导出的数据
            analyze_recent_test_data(exported_data)
            
            print(f"\n✅ 导出完成！文件: {filename}")
        else:
            print("❌ 没有找到符合条件的数据")
    
    except Exception as e:
        print(f"❌ 导出过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 6. 关闭数据库连接
        if conn:
            conn.close()
            print("🔒 数据库连接已关闭")

if __name__ == "__main__":
    main()
