#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成SQL查询语句，用于查询大量member_id
"""

import pandas as pd
import math

def generate_sql_queries(excel_file_path, batch_size=1000):
    """
    从Excel文件读取member_id并生成分批SQL查询语句
    
    Args:
        excel_file_path: Excel文件路径
        batch_size: 每个SQL语句包含的member_id数量
    """
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_file_path)
        
        # 获取第一列作为member_id列（根据之前的分析，第一列包含member_id）
        member_ids = df.iloc[:, 0].dropna().unique().tolist()
        
        print(f"总共找到 {len(member_ids)} 个唯一的member_id")
        
        # 计算需要分成多少批
        total_batches = math.ceil(len(member_ids) / batch_size)
        print(f"将分成 {total_batches} 个批次，每批最多 {batch_size} 个ID")
        
        # 生成SQL语句
        sql_queries = []
        
        for i in range(total_batches):
            start_idx = i * batch_size
            end_idx = min((i + 1) * batch_size, len(member_ids))
            batch_member_ids = member_ids[start_idx:end_idx]
            
            # 构建IN子句
            in_clause = ",\n    ".join([f"'{member_id}'" for member_id in batch_member_ids])
            
            # 构建完整的SQL语句
            sql = f"""-- 批次 {i + 1}/{total_batches} (包含 {len(batch_member_ids)} 个member_id)
SELECT DISTINCT 
    member_id,
    digital_id,
    last_login_country,
    first_operation_country,
    kyc_country,
    member_country,
    standard_language,
    origin
FROM dw_dim.dim_hierarchy_t_ourbit_member_a_daily
WHERE dt BETWEEN '20250708' AND '20250807'
  AND member_id IN (
    {in_clause}
  );"""
            
            sql_queries.append(sql)
        
        return sql_queries
        
    except Exception as e:
        print(f"处理文件时出错: {e}")
        return []

def save_sql_to_file(sql_queries, output_file='member_queries.sql'):
    """
    将SQL查询保存到文件
    """
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("-- 自动生成的member_id查询SQL语句\n")
            f.write(f"-- 总共 {len(sql_queries)} 个查询批次\n")
            f.write("-- 每个查询最多包含1000个member_id\n\n")
            
            for i, sql in enumerate(sql_queries):
                f.write(sql)
                if i < len(sql_queries) - 1:
                    f.write("\n\n")
        
        print(f"SQL查询已保存到文件: {output_file}")
        
    except Exception as e:
        print(f"保存文件时出错: {e}")

if __name__ == "__main__":
    # 配置参数
    excel_file = "地址数据.xlsx"
    batch_size = 1000  # 每批1000个member_id，可以根据数据库性能调整
    
    # 生成SQL查询
    queries = generate_sql_queries(excel_file, batch_size)
    
    if queries:
        # 保存到文件
        save_sql_to_file(queries)
        
        # 显示第一个查询作为示例
        print("\n" + "="*50)
        print("第一个查询示例:")
        print("="*50)
        print(queries[0][:1000] + "..." if len(queries[0]) > 1000 else queries[0])
        
        print(f"\n所有 {len(queries)} 个查询已生成并保存到 member_queries.sql 文件中")
    else:
        print("未能生成SQL查询")
