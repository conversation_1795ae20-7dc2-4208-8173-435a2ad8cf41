#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提币地址分析脚本
主要分析member_id和address的关系，识别可能的团伙行为
"""

import pandas as pd
import numpy as np
from collections import defaultdict, Counter
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_and_explore_data():
    """加载数据并进行基本探索"""
    print("=== 数据加载和基本信息 ===")
    df = pd.read_csv('提币地址.csv')
    
    print(f"数据总行数: {len(df):,}")
    print(f"数据列数: {len(df.columns)}")
    print(f"\n列名: {list(df.columns)}")
    
    # 基本统计信息
    print(f"\n=== 基本统计 ===")
    print(f"唯一member_id数量: {df['member_id'].nunique():,}")
    print(f"唯一address数量: {df['address'].nunique():,}")
    print(f"唯一currency数量: {df['currency'].nunique()}")
    print(f"时间范围: {df['dt'].min()} 到 {df['dt'].max()}")
    
    # 检查缺失值
    print(f"\n=== 缺失值情况 ===")
    missing_info = df.isnull().sum()
    for col, missing_count in missing_info.items():
        if missing_count > 0:
            print(f"{col}: {missing_count:,} ({missing_count/len(df)*100:.2f}%)")
    
    return df

def analyze_member_address_relationship(df):
    """分析member_id和address的关系"""
    print("\n=== Member ID 和 Address 关系分析 ===")
    
    # 每个member_id使用的地址数量
    member_address_count = df.groupby('member_id')['address'].nunique().reset_index()
    member_address_count.columns = ['member_id', 'unique_addresses']
    
    print(f"使用多个地址的用户数量: {(member_address_count['unique_addresses'] > 1).sum():,}")
    print(f"平均每个用户使用地址数: {member_address_count['unique_addresses'].mean():.2f}")
    print(f"最多使用地址数: {member_address_count['unique_addresses'].max()}")
    
    # 每个address被多少个member_id使用
    address_member_count = df.groupby('address')['member_id'].nunique().reset_index()
    address_member_count.columns = ['address', 'unique_members']
    
    shared_addresses = address_member_count[address_member_count['unique_members'] > 1]
    print(f"\n被多个用户共享的地址数量: {len(shared_addresses):,}")
    print(f"最多被多少个用户共享: {address_member_count['unique_members'].max()}")
    
    return member_address_count, address_member_count, shared_addresses

def identify_potential_groups(df, shared_addresses):
    """识别潜在的团伙行为"""
    print("\n=== 潜在团伙识别 ===")
    
    # 分析共享地址的用户群体
    suspicious_groups = []
    
    for _, row in shared_addresses.iterrows():
        address = row['address']
        member_count = row['unique_members']
        
        if member_count >= 3:  # 至少3个用户共享同一地址
            members = df[df['address'] == address]['member_id'].unique()
            
            # 获取这些用户的详细信息
            group_data = df[df['member_id'].isin(members)]
            
            # 计算一些特征
            total_transactions = len(group_data)
            currencies = group_data['currency'].unique()
            time_span = group_data['dt'].nunique()
            total_amount = group_data['currency_usdt_amount'].sum() if 'currency_usdt_amount' in group_data.columns else 0
            
            suspicious_groups.append({
                'shared_address': address,
                'member_count': member_count,
                'members': list(members),
                'total_transactions': total_transactions,
                'currencies': list(currencies),
                'time_span_days': time_span,
                'total_usdt_amount': total_amount
            })
    
    # 按用户数量排序
    suspicious_groups.sort(key=lambda x: x['member_count'], reverse=True)
    
    print(f"发现 {len(suspicious_groups)} 个可疑团伙（3人以上共享地址）")
    
    # 显示前10个最大的团伙
    print("\n=== 前10个最大团伙 ===")
    for i, group in enumerate(suspicious_groups[:10]):
        print(f"\n团伙 {i+1}:")
        print(f"  共享地址: {group['shared_address']}")
        print(f"  成员数量: {group['member_count']}")
        print(f"  总交易次数: {group['total_transactions']}")
        print(f"  涉及币种: {', '.join(group['currencies'])}")
        print(f"  时间跨度: {group['time_span_days']} 天")
        print(f"  总金额(USDT): {group['total_usdt_amount']:.2f}")
    
    return suspicious_groups

def analyze_transaction_patterns(df):
    """分析交易模式"""
    print("\n=== 交易模式分析 ===")
    
    # 币种分布
    currency_dist = df['currency'].value_counts()
    print(f"\n=== 币种分布 (前10) ===")
    print(currency_dist.head(10))
    
    # 金额分析
    if 'currency_usdt_amount' in df.columns:
        df_with_amount = df.dropna(subset=['currency_usdt_amount'])
        print(f"\n=== 金额分析 ===")
        print(f"有金额数据的交易: {len(df_with_amount):,}")
        print(f"平均金额(USDT): {df_with_amount['currency_usdt_amount'].mean():.2f}")
        print(f"中位数金额(USDT): {df_with_amount['currency_usdt_amount'].median():.2f}")
        print(f"最大金额(USDT): {df_with_amount['currency_usdt_amount'].max():.2f}")
        print(f"总金额(USDT): {df_with_amount['currency_usdt_amount'].sum():.2f}")
    
    # 时间分析
    df['dt'] = pd.to_datetime(df['dt'], format='%Y%m%d')
    daily_transactions = df.groupby('dt').size()
    print(f"\n=== 时间分析 ===")
    print(f"日均交易量: {daily_transactions.mean():.0f}")
    print(f"最高日交易量: {daily_transactions.max()}")
    print(f"最低日交易量: {daily_transactions.min()}")
    
    return currency_dist, daily_transactions

def create_visualizations(df, member_address_count, address_member_count, suspicious_groups):
    """创建可视化图表"""
    print("\n=== 生成可视化图表 ===")
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # 1. 用户使用地址数量分布
    ax1 = axes[0, 0]
    address_count_dist = member_address_count['unique_addresses'].value_counts().head(20)
    ax1.bar(range(len(address_count_dist)), address_count_dist.values)
    ax1.set_title('用户使用地址数量分布')
    ax1.set_xlabel('地址数量')
    ax1.set_ylabel('用户数量')
    ax1.set_xticks(range(len(address_count_dist)))
    ax1.set_xticklabels(address_count_dist.index, rotation=45)
    
    # 2. 地址被用户共享情况
    ax2 = axes[0, 1]
    member_count_dist = address_member_count['unique_members'].value_counts().head(10)
    ax2.bar(range(len(member_count_dist)), member_count_dist.values)
    ax2.set_title('地址被用户共享情况')
    ax2.set_xlabel('共享用户数')
    ax2.set_ylabel('地址数量')
    ax2.set_xticks(range(len(member_count_dist)))
    ax2.set_xticklabels(member_count_dist.index)
    
    # 3. 币种分布
    ax3 = axes[1, 0]
    currency_dist = df['currency'].value_counts().head(10)
    ax3.pie(currency_dist.values, labels=currency_dist.index, autopct='%1.1f%%')
    ax3.set_title('主要币种分布')
    
    # 4. 团伙规模分布
    ax4 = axes[1, 1]
    if suspicious_groups:
        group_sizes = [group['member_count'] for group in suspicious_groups]
        ax4.hist(group_sizes, bins=20, edgecolor='black')
        ax4.set_title('可疑团伙规模分布')
        ax4.set_xlabel('团伙成员数')
        ax4.set_ylabel('团伙数量')
    
    plt.tight_layout()
    plt.savefig('withdrawal_analysis.png', dpi=300, bbox_inches='tight')
    print("图表已保存为 withdrawal_analysis.png")

def generate_report(df, suspicious_groups):
    """生成分析报告"""
    print("\n=== 生成分析报告 ===")
    
    report = f"""
# 提币地址分析报告

## 数据概览
- 总交易记录: {len(df):,}
- 唯一用户数: {df['member_id'].nunique():,}
- 唯一地址数: {df['address'].nunique():,}
- 时间范围: {df['dt'].min().strftime('%Y-%m-%d')} 到 {df['dt'].max().strftime('%Y-%m-%d')}

## 主要发现

### 1. 地址共享情况
- 发现 {len(suspicious_groups)} 个可疑团伙（3人以上共享地址）
- 最大团伙有 {max([g['member_count'] for g in suspicious_groups]) if suspicious_groups else 0} 个成员

### 2. 风险等级分类
"""
    
    if suspicious_groups:
        high_risk = [g for g in suspicious_groups if g['member_count'] >= 10]
        medium_risk = [g for g in suspicious_groups if 5 <= g['member_count'] < 10]
        low_risk = [g for g in suspicious_groups if 3 <= g['member_count'] < 5]
        
        report += f"""
- 高风险团伙 (≥10人): {len(high_risk)} 个
- 中风险团伙 (5-9人): {len(medium_risk)} 个  
- 低风险团伙 (3-4人): {len(low_risk)} 个
"""
        
        report += "\n### 3. 高风险团伙详情\n"
        for i, group in enumerate(high_risk[:5]):
            report += f"""
#### 团伙 {i+1}
- 共享地址: {group['shared_address']}
- 成员数量: {group['member_count']}
- 总交易次数: {group['total_transactions']}
- 涉及币种: {', '.join(group['currencies'])}
- 总金额(USDT): {group['total_usdt_amount']:.2f}
"""
    
    with open('withdrawal_analysis_report.md', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print("分析报告已保存为 withdrawal_analysis_report.md")

def main():
    """主函数"""
    print("开始分析提币地址数据...")
    
    # 1. 加载和探索数据
    df = load_and_explore_data()
    
    # 2. 分析member_id和address关系
    member_address_count, address_member_count, shared_addresses = analyze_member_address_relationship(df)
    
    # 3. 识别潜在团伙
    suspicious_groups = identify_potential_groups(df, shared_addresses)
    
    # 4. 分析交易模式
    currency_dist, daily_transactions = analyze_transaction_patterns(df)
    
    # 5. 创建可视化
    create_visualizations(df, member_address_count, address_member_count, suspicious_groups)
    
    # 6. 生成报告
    generate_report(df, suspicious_groups)
    
    print("\n=== 分析完成 ===")
    print("生成的文件:")
    print("- withdrawal_analysis.png (可视化图表)")
    print("- withdrawal_analysis_report.md (分析报告)")

if __name__ == "__main__":
    main()
