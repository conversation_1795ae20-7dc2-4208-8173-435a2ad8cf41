#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证团伙分类逻辑
确认没有重复计算
"""

import pandas as pd

def verify_group_classification():
    """验证团伙分类逻辑"""
    print("=== 验证团伙分类逻辑 ===")
    
    # 加载数据
    df = pd.read_csv('提币地址.csv')
    
    # 计算每个地址被多少个账户使用
    address_user_counts = df.groupby('address')['member_id'].nunique().reset_index()
    address_user_counts.columns = ['address', 'user_count']
    
    print(f"总地址数: {len(address_user_counts):,}")
    
    # 分类地址
    normal_addresses = address_user_counts[address_user_counts['user_count'] == 1]
    group_addresses = address_user_counts[address_user_counts['user_count'] >= 2]
    
    print(f"正常地址数: {len(normal_addresses):,}")
    print(f"团伙地址数: {len(group_addresses):,}")
    print(f"总计: {len(normal_addresses) + len(group_addresses):,}")
    
    # 验证是否有重叠
    normal_address_set = set(normal_addresses['address'])
    group_address_set = set(group_addresses['address'])
    
    overlap = normal_address_set & group_address_set
    print(f"\n正常地址和团伙地址重叠数: {len(overlap)}")
    
    if len(overlap) > 0:
        print("❌ 错误：存在重叠地址！")
        print("重叠地址示例:", list(overlap)[:5])
    else:
        print("✅ 正确：没有重叠地址")
    
    # 验证团伙地址分类
    print(f"\n=== 验证团伙地址分类 ===")
    
    group_2_users = group_addresses[group_addresses['user_count'] == 2]
    group_3_users = group_addresses[group_addresses['user_count'] == 3]
    group_4_5_users = group_addresses[(group_addresses['user_count'] >= 4) & (group_addresses['user_count'] <= 5)]
    group_6_10_users = group_addresses[(group_addresses['user_count'] >= 6) & (group_addresses['user_count'] <= 10)]
    group_11_plus_users = group_addresses[group_addresses['user_count'] >= 11]
    
    print(f"2人团伙地址: {len(group_2_users):,}")
    print(f"3人团伙地址: {len(group_3_users):,}")
    print(f"4-5人团伙地址: {len(group_4_5_users):,}")
    print(f"6-10人团伙地址: {len(group_6_10_users):,}")
    print(f"11人以上团伙地址: {len(group_11_plus_users):,}")
    
    total_classified = len(group_2_users) + len(group_3_users) + len(group_4_5_users) + len(group_6_10_users) + len(group_11_plus_users)
    print(f"分类后总计: {total_classified:,}")
    print(f"原始团伙地址数: {len(group_addresses):,}")
    
    if total_classified == len(group_addresses):
        print("✅ 正确：团伙地址分类无重复")
    else:
        print("❌ 错误：团伙地址分类有问题")
    
    # 验证每个分类是否有重叠
    print(f"\n=== 验证分类重叠 ===")
    
    set_2 = set(group_2_users['address'])
    set_3 = set(group_3_users['address'])
    set_4_5 = set(group_4_5_users['address'])
    set_6_10 = set(group_6_10_users['address'])
    set_11_plus = set(group_11_plus_users['address'])
    
    all_sets = [set_2, set_3, set_4_5, set_6_10, set_11_plus]
    set_names = ['2人团伙', '3人团伙', '4-5人团伙', '6-10人团伙', '11人以上团伙']
    
    has_overlap = False
    for i in range(len(all_sets)):
        for j in range(i+1, len(all_sets)):
            overlap = all_sets[i] & all_sets[j]
            if len(overlap) > 0:
                print(f"❌ {set_names[i]} 和 {set_names[j]} 有重叠: {len(overlap)} 个地址")
                has_overlap = True
    
    if not has_overlap:
        print("✅ 正确：各分类之间无重叠")
    
    # 详细验证：检查具体的用户数分布
    print(f"\n=== 详细用户数分布 ===")
    user_count_dist = group_addresses['user_count'].value_counts().sort_index()
    
    print("用户数 | 地址数量")
    print("-" * 20)
    for user_count, count in user_count_dist.head(20).items():
        print(f"{user_count:6d} | {count:8,}")
    
    # 验证范围分类
    print(f"\n=== 验证范围分类 ===")
    
    # 手动计算各范围
    manual_2 = len(group_addresses[group_addresses['user_count'] == 2])
    manual_3 = len(group_addresses[group_addresses['user_count'] == 3])
    manual_4_5 = len(group_addresses[(group_addresses['user_count'] >= 4) & (group_addresses['user_count'] <= 5)])
    manual_6_10 = len(group_addresses[(group_addresses['user_count'] >= 6) & (group_addresses['user_count'] <= 10)])
    manual_11_plus = len(group_addresses[group_addresses['user_count'] >= 11])
    
    print(f"手动计算:")
    print(f"  2人团伙: {manual_2:,}")
    print(f"  3人团伙: {manual_3:,}")
    print(f"  4-5人团伙: {manual_4_5:,}")
    print(f"  6-10人团伙: {manual_6_10:,}")
    print(f"  11人以上团伙: {manual_11_plus:,}")
    print(f"  总计: {manual_2 + manual_3 + manual_4_5 + manual_6_10 + manual_11_plus:,}")
    
    # 使用value_counts验证
    print(f"\n使用value_counts验证:")
    range_2 = user_count_dist[user_count_dist.index == 2].sum()
    range_3 = user_count_dist[user_count_dist.index == 3].sum()
    range_4_5 = user_count_dist[(user_count_dist.index >= 4) & (user_count_dist.index <= 5)].sum()
    range_6_10 = user_count_dist[(user_count_dist.index >= 6) & (user_count_dist.index <= 10)].sum()
    range_11_plus = user_count_dist[user_count_dist.index >= 11].sum()
    
    print(f"  2人团伙: {range_2:,}")
    print(f"  3人团伙: {range_3:,}")
    print(f"  4-5人团伙: {range_4_5:,}")
    print(f"  6-10人团伙: {range_6_10:,}")
    print(f"  11人以上团伙: {range_11_plus:,}")
    print(f"  总计: {range_2 + range_3 + range_4_5 + range_6_10 + range_11_plus:,}")
    
    # 最终验证
    print(f"\n=== 最终验证 ===")
    if (manual_2 == range_2 and manual_3 == range_3 and manual_4_5 == range_4_5 and 
        manual_6_10 == range_6_10 and manual_11_plus == range_11_plus):
        print("✅ 所有计算结果一致，分类逻辑正确")
    else:
        print("❌ 计算结果不一致，存在问题")
    
    return address_user_counts, group_addresses

def main():
    """主函数"""
    address_user_counts, group_addresses = verify_group_classification()
    
    print(f"\n=== 总结 ===")
    print(f"代码逻辑验证完成")
    print(f"团伙地址分类是基于每个地址的唯一用户数进行的")
    print(f"不存在重复计算的问题")

if __name__ == "__main__":
    main()
