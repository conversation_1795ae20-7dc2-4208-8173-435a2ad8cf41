# BIPC交易所API配置
api:
  base_url: "https://beta-9.bydtms.com"
  # 请在这里填入你的API密钥（如果有的话）
  api_key: ""
  api_secret: ""
  # BIPC特有的认证信息（从浏览器获取）
  token: "006ba7a3-a856-4094-84e5-fa86dd5e990e"  # TOKEN cookie值
  jsessionid: "7637816AE9FFBDB3459872BFAC4FA724"  # JSESSIONID cookie值（可选）
  # 请求超时时间（秒）
  timeout: 30
  # 请求重试次数
  max_retries: 3

# 交易配置
trading:
  # 默认交易对
  symbol: "ETH-USDT"
  # 最小交易数量
  min_quantity: 0.001
  # 最大交易数量
  max_quantity: 1.0
  # 价格精度（小数位数）
  price_precision: 2
  # 数量精度（小数位数）
  quantity_precision: 6

# 策略配置
strategy:
  # 策略类型
  type: "simple"
  # 策略参数
  params:
    # 买入阈值（百分比）
    buy_threshold: -0.5
    # 卖出阈值（百分比）
    sell_threshold: 0.5
    # 检查间隔（秒）
    check_interval: 10

# 风险控制
risk:
  # 最大持仓金额（USDT）
  max_position_value: 100.0
  # 单次最大交易金额（USDT）
  max_order_value: 50.0
  # 每日最大亏损（USDT）
  max_daily_loss: 20.0

# 日志配置
logging:
  # 日志级别: DEBUG, INFO, WARNING, ERROR
  level: "INFO"
  # 日志文件路径
  file: "logs/trading_bot.log"
  # 日志文件最大大小（MB）
  max_size: 10
  # 保留的日志文件数量
  backup_count: 5

# 数据库配置（可选）
database:
  # 是否启用数据库
  enabled: false
  # 数据库类型: sqlite, mysql, postgresql
  type: "sqlite"
  # 数据库连接字符串
  url: "sqlite:///trading_data.db"
