# SQL查询文件说明

## 概述
根据您提供的Excel文件（地址数据.xlsx），我已经生成了用于查询28,000多个member_id的SQL语句。

## 数据统计
- **Excel文件总行数**: 28,214行
- **唯一member_id数量**: 22,162个
- **查询时间范围**: 2025年7月8日 至 2025年8月7日 (`dt BETWEEN '20250708' AND '20250807'`)

## 生成的文件

### 1. member_queries.sql
**文件大小**: 约22,510行
**内容**: 23个独立的SQL查询，每个查询包含最多1000个member_id

**特点**:
- 每个查询都是完整的、可独立执行的SQL语句
- 适合分批执行，避免单次查询过大
- 每个查询都有注释说明批次信息

**使用方法**:
```sql
-- 可以逐个执行每个查询，例如：
-- 批次 1/23 (包含 1000 个member_id)
SELECT DISTINCT 
    member_id,
    digital_id,
    last_login_country,
    first_operation_country,
    kyc_country,
    member_country,
    standard_language,
    origin
FROM dw_dim.dim_hierarchy_t_ourbit_member_a_daily
WHERE dt BETWEEN '20250708' AND '20250807'
  AND member_id IN (
    'member_id_1',
    'member_id_2',
    ...
  );
```

### 2. member_union_query.sql
**内容**: 一个完整的UNION ALL查询，包含所有member_id

**特点**:
- 单个文件包含所有查询
- 使用UNION ALL连接所有批次
- 可以一次性获取所有结果

**使用方法**:
```sql
-- 执行整个文件即可获取所有结果
-- 注意：这个查询可能比较大，执行时间较长
```

## 查询字段说明
每个查询都包含以下字段：
- `member_id`: 会员ID
- `digital_id`: 数字ID
- `last_login_country`: 最后登录国家
- `first_operation_country`: 首次操作国家
- `kyc_country`: KYC国家
- `member_country`: 会员国家
- `standard_language`: 标准语言
- `origin`: 来源

## 建议使用方式

### 方案1：分批执行（推荐）
使用 `member_queries.sql` 文件，逐个执行23个查询：
- 优点：每个查询相对较小，执行速度快，便于监控进度
- 缺点：需要执行23次查询

### 方案2：一次性执行
使用 `member_union_query.sql` 文件：
- 优点：一次性获取所有结果
- 缺点：查询较大，可能执行时间较长

## 性能优化建议
1. 确保 `member_id` 和 `dt` 字段有适当的索引
2. 如果数据库性能允许，可以增加每批的member_id数量（修改batch_size参数）
3. 可以并行执行多个批次查询以提高效率

## 文件位置
所有生成的文件都保存在当前目录下：
- `member_queries.sql` - 分批查询文件
- `member_union_query.sql` - UNION ALL查询文件
- `generate_sql.py` - 生成分批查询的Python脚本
- `generate_union_sql.py` - 生成UNION查询的Python脚本
