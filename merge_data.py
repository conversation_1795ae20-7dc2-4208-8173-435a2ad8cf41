#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据合并脚本：根据member_id将CSV和Excel文件进行一对一匹配
"""

import pandas as pd
import sys
import os

def merge_data_files():
    """
    读取CSV和Excel文件，根据member_id进行合并
    """
    try:
        # 读取CSV文件
        print("正在读取CSV文件...")
        csv_file = "整理后的数据.csv"
        df_csv = pd.read_csv(csv_file, encoding='utf-8')
        print(f"CSV文件读取成功，共 {len(df_csv)} 行数据")
        print(f"CSV文件列名: {list(df_csv.columns)}")
        
        # 读取Excel文件
        print("\n正在读取Excel文件...")
        excel_file = "bd.xlsx"
        df_excel = pd.read_excel(excel_file)
        print(f"Excel文件读取成功，共 {len(df_excel)} 行数据")
        print(f"Excel文件列名: {list(df_excel.columns)}")
        
        # 检查member_id列是否存在
        if 'member_id' not in df_csv.columns:
            print("错误：CSV文件中没有找到 'member_id' 列")
            return False
            
        if 'member_id' not in df_excel.columns:
            print("错误：Excel文件中没有找到 'member_id' 列")
            return False
        
        # 显示数据预览
        print("\nCSV文件前5行数据预览:")
        print(df_csv.head())
        
        print("\nExcel文件前5行数据预览:")
        print(df_excel.head())
        
        # 检查member_id的重复情况
        csv_duplicates = df_csv['member_id'].duplicated().sum()
        excel_duplicates = df_excel['member_id'].duplicated().sum()
        
        print(f"\nCSV文件中member_id重复数量: {csv_duplicates}")
        print(f"Excel文件中member_id重复数量: {excel_duplicates}")
        
        # 进行内连接合并（一对一匹配）
        print("\n正在进行数据合并...")
        merged_df = pd.merge(df_csv, df_excel, on='member_id', how='inner', suffixes=('_csv', '_excel'))
        
        print(f"合并完成，匹配到 {len(merged_df)} 条记录")
        
        # 显示合并后的数据信息
        print(f"\n合并后的数据列名: {list(merged_df.columns)}")
        print(f"合并后数据形状: {merged_df.shape}")
        
        # 保存合并后的数据到Excel文件
        output_file = "合并后的完整数据.xlsx"
        print(f"\n正在保存合并后的数据到 {output_file}...")
        
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            merged_df.to_excel(writer, sheet_name='合并数据', index=False)
            
            # 添加统计信息工作表
            stats_data = {
                '统计项目': [
                    'CSV原始数据行数',
                    'Excel原始数据行数', 
                    '成功匹配行数',
                    'CSV中未匹配行数',
                    'Excel中未匹配行数',
                    '匹配率(基于CSV)',
                    '匹配率(基于Excel)'
                ],
                '数值': [
                    len(df_csv),
                    len(df_excel),
                    len(merged_df),
                    len(df_csv) - len(merged_df),
                    len(df_excel) - len(merged_df),
                    f"{len(merged_df)/len(df_csv)*100:.2f}%",
                    f"{len(merged_df)/len(df_excel)*100:.2f}%"
                ]
            }
            stats_df = pd.DataFrame(stats_data)
            stats_df.to_excel(writer, sheet_name='统计信息', index=False)
        
        print(f"数据已成功保存到 {output_file}")
        
        # 显示匹配统计
        print("\n=== 匹配统计 ===")
        print(f"CSV原始数据: {len(df_csv)} 行")
        print(f"Excel原始数据: {len(df_excel)} 行")
        print(f"成功匹配: {len(merged_df)} 行")
        print(f"CSV中未匹配: {len(df_csv) - len(merged_df)} 行")
        print(f"Excel中未匹配: {len(df_excel) - len(merged_df)} 行")
        print(f"匹配率(基于CSV): {len(merged_df)/len(df_csv)*100:.2f}%")
        print(f"匹配率(基于Excel): {len(merged_df)/len(df_excel)*100:.2f}%")
        
        return True
        
    except FileNotFoundError as e:
        print(f"文件未找到错误: {e}")
        return False
    except Exception as e:
        print(f"处理过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    print("开始数据合并处理...")
    success = merge_data_files()
    
    if success:
        print("\n数据合并完成！")
    else:
        print("\n数据合并失败！")
        sys.exit(1)
