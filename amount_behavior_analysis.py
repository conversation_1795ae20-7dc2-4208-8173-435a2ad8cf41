#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
金额行为分析脚本
专门分析200U以下和200U以上的行为模式
"""

import pandas as pd
import numpy as np
from collections import Counter
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment
import warnings
warnings.filterwarnings('ignore')

def analyze_amount_behaviors():
    """分析金额行为模式"""
    print("=== 金额行为分析 ===")
    
    # 加载数据
    df = pd.read_csv('提币地址.csv')
    df['dt'] = pd.to_datetime(df['dt'], format='%Y%m%d')
    
    # 过滤有金额数据的记录
    amount_data = df.dropna(subset=['currency_usdt_amount']).copy()
    
    # 分类
    below_200 = amount_data[amount_data['currency_usdt_amount'] < 200].copy()
    above_200 = amount_data[amount_data['currency_usdt_amount'] >= 200].copy()
    
    print(f"有金额数据的交易: {len(amount_data):,}")
    print(f"200U以下交易: {len(below_200):,} ({len(below_200)/len(amount_data)*100:.1f}%)")
    print(f"200U以上交易: {len(above_200):,} ({len(above_200)/len(amount_data)*100:.1f}%)")
    
    # 识别团伙
    address_users = df.groupby('address')['member_id'].nunique().reset_index()
    group_addresses = address_users[address_users['member_id'] >= 3]['address'].tolist()
    
    # 标记团伙交易
    amount_data['is_group'] = amount_data['address'].isin(group_addresses)
    below_200['is_group'] = below_200['address'].isin(group_addresses)
    above_200['is_group'] = above_200['address'].isin(group_addresses)
    
    return amount_data, below_200, above_200, group_addresses

def create_behavior_analysis_excel(amount_data, below_200, above_200, group_addresses):
    """创建行为分析Excel报告"""
    print("=== 生成行为分析Excel报告 ===")
    
    wb = openpyxl.Workbook()
    wb.remove(wb.active)
    
    # 1. 行为对比分析
    create_behavior_comparison_sheet(wb, amount_data, below_200, above_200)
    
    # 2. 用户行为分类
    create_user_behavior_sheet(wb, amount_data, below_200, above_200)
    
    # 3. 团伙行为分析
    create_group_behavior_sheet(wb, amount_data, group_addresses)
    
    # 4. 币种偏好分析
    create_currency_preference_sheet(wb, below_200, above_200)
    
    # 5. 时间模式分析
    create_time_pattern_sheet(wb, below_200, above_200)
    
    # 6. 详细分类统计
    create_detailed_stats_sheet(wb, amount_data, below_200, above_200)
    
    filename = '金额行为分析报告.xlsx'
    wb.save(filename)
    print(f"行为分析报告已保存: {filename}")
    
    return filename

def create_behavior_comparison_sheet(wb, amount_data, below_200, above_200):
    """创建行为对比分析工作表"""
    ws = wb.create_sheet("行为对比分析")
    
    # 标题
    ws['A1'] = '200U以下 vs 200U以上 行为对比分析'
    ws['A1'].font = Font(size=16, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center')
    ws.merge_cells('A1:D1')
    
    # 基本对比
    ws['A3'] = '基本统计对比'
    ws['A3'].font = Font(size=14, bold=True)
    
    comparison_data = [
        ['指标', '200U以下', '200U以上', '差异'],
        ['交易数量', f"{len(below_200):,}", f"{len(above_200):,}", f"{len(below_200) - len(above_200):,}"],
        ['占比', f"{len(below_200)/len(amount_data)*100:.1f}%", f"{len(above_200)/len(amount_data)*100:.1f}%", ""],
        ['唯一用户数', f"{below_200['member_id'].nunique():,}", f"{above_200['member_id'].nunique():,}", f"{below_200['member_id'].nunique() - above_200['member_id'].nunique():,}"],
        ['唯一地址数', f"{below_200['address'].nunique():,}", f"{above_200['address'].nunique():,}", f"{below_200['address'].nunique() - above_200['address'].nunique():,}"],
        ['平均金额(USDT)', f"{below_200['currency_usdt_amount'].mean():.2f}", f"{above_200['currency_usdt_amount'].mean():.2f}", f"{above_200['currency_usdt_amount'].mean() - below_200['currency_usdt_amount'].mean():.2f}"],
        ['中位数金额(USDT)', f"{below_200['currency_usdt_amount'].median():.2f}", f"{above_200['currency_usdt_amount'].median():.2f}", f"{above_200['currency_usdt_amount'].median() - below_200['currency_usdt_amount'].median():.2f}"],
        ['总金额(USDT)', f"{below_200['currency_usdt_amount'].sum():,.2f}", f"{above_200['currency_usdt_amount'].sum():,.2f}", f"{above_200['currency_usdt_amount'].sum() - below_200['currency_usdt_amount'].sum():,.2f}"],
        ['币种数量', f"{below_200['currency'].nunique()}", f"{above_200['currency'].nunique()}", f"{above_200['currency'].nunique() - below_200['currency'].nunique()}"],
        ['团伙交易数', f"{len(below_200[below_200['is_group']]):,}", f"{len(above_200[above_200['is_group']]):,}", f"{len(above_200[above_200['is_group']]) - len(below_200[below_200['is_group']]):,}"],
        ['团伙交易占比', f"{len(below_200[below_200['is_group']])/len(below_200)*100:.1f}%", f"{len(above_200[above_200['is_group']])/len(above_200)*100:.1f}%", ""]
    ]
    
    for i, row_data in enumerate(comparison_data, 4):
        for j, value in enumerate(row_data, 1):
            cell = ws.cell(row=i, column=j, value=value)
            if i == 4:  # 表头
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')
    
    # 用户行为模式对比
    ws['A17'] = '用户行为模式对比'
    ws['A17'].font = Font(size=14, bold=True)
    
    # 计算用户在两个金额区间的分布
    below_200_users = set(below_200['member_id'].unique())
    above_200_users = set(above_200['member_id'].unique())
    
    only_below_200 = below_200_users - above_200_users
    only_above_200 = above_200_users - below_200_users
    both_categories = below_200_users & above_200_users
    
    behavior_data = [
        ['用户类型', '用户数量', '占比', '说明'],
        ['仅小额用户', f"{len(only_below_200):,}", f"{len(only_below_200)/(len(below_200_users | above_200_users))*100:.1f}%", '只进行200U以下交易'],
        ['仅大额用户', f"{len(only_above_200):,}", f"{len(only_above_200)/(len(below_200_users | above_200_users))*100:.1f}%", '只进行200U以上交易'],
        ['混合用户', f"{len(both_categories):,}", f"{len(both_categories)/(len(below_200_users | above_200_users))*100:.1f}%", '同时进行两种金额交易'],
        ['总用户数', f"{len(below_200_users | above_200_users):,}", '100.0%', '有金额数据的用户总数']
    ]
    
    for i, row_data in enumerate(behavior_data, 19):
        for j, value in enumerate(row_data, 1):
            cell = ws.cell(row=i, column=j, value=value)
            if i == 19 or i == 23:  # 表头和总计行
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')
    
    # 调整列宽
    for col in range(1, 5):
        ws.column_dimensions[chr(64 + col)].width = 20

def create_user_behavior_sheet(wb, amount_data, below_200, above_200):
    """创建用户行为分类工作表"""
    ws = wb.create_sheet("用户行为分类")
    
    # 标题
    ws['A1'] = '用户行为详细分类'
    ws['A1'].font = Font(size=16, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center')
    ws.merge_cells('A1:J1')
    
    # 分析每个用户的行为模式
    user_behavior = []
    
    all_users = amount_data['member_id'].unique()
    
    for user_id in all_users:
        user_data = amount_data[amount_data['member_id'] == user_id]
        user_below_200 = user_data[user_data['currency_usdt_amount'] < 200]
        user_above_200 = user_data[user_data['currency_usdt_amount'] >= 200]
        
        # 分类用户行为
        if len(user_above_200) == 0:
            behavior_type = '纯小额用户'
        elif len(user_below_200) == 0:
            behavior_type = '纯大额用户'
        elif len(user_below_200) > len(user_above_200) * 2:
            behavior_type = '小额主导用户'
        elif len(user_above_200) > len(user_below_200) * 2:
            behavior_type = '大额主导用户'
        else:
            behavior_type = '均衡用户'
        
        # 计算其他特征
        total_amount = user_data['currency_usdt_amount'].sum()
        avg_amount = user_data['currency_usdt_amount'].mean()
        transaction_count = len(user_data)
        currency_count = user_data['currency'].nunique()
        is_in_group = user_data['is_group'].any()
        
        user_behavior.append({
            'user_id': user_id,
            'behavior_type': behavior_type,
            'total_transactions': transaction_count,
            'below_200_count': len(user_below_200),
            'above_200_count': len(user_above_200),
            'total_amount': total_amount,
            'avg_amount': avg_amount,
            'currency_count': currency_count,
            'is_in_group': is_in_group,
            'first_transaction': user_data['dt'].min(),
            'last_transaction': user_data['dt'].max()
        })
    
    # 按行为类型统计
    behavior_counter = Counter([u['behavior_type'] for u in user_behavior])
    
    ws['A3'] = '用户行为类型统计'
    ws['A3'].font = Font(size=14, bold=True)
    
    type_headers = ['行为类型', '用户数量', '占比', '平均交易数', '平均金额(USDT)', '团伙用户数', '团伙占比']
    for col, header in enumerate(type_headers, 1):
        cell = ws.cell(row=5, column=col, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')
    
    row = 6
    for behavior_type, count in behavior_counter.most_common():
        type_users = [u for u in user_behavior if u['behavior_type'] == behavior_type]
        avg_transactions = np.mean([u['total_transactions'] for u in type_users])
        avg_amount = np.mean([u['avg_amount'] for u in type_users])
        group_users = len([u for u in type_users if u['is_in_group']])
        
        ws.cell(row=row, column=1, value=behavior_type)
        ws.cell(row=row, column=2, value=count)
        ws.cell(row=row, column=3, value=f"{count/len(user_behavior)*100:.1f}%")
        ws.cell(row=row, column=4, value=f"{avg_transactions:.1f}")
        ws.cell(row=row, column=5, value=f"{avg_amount:.2f}")
        ws.cell(row=row, column=6, value=group_users)
        ws.cell(row=row, column=7, value=f"{group_users/count*100:.1f}%")
        
        row += 1
    
    # 详细用户列表（前500个）
    ws['A15'] = '用户详细列表（前500个）'
    ws['A15'].font = Font(size=14, bold=True)
    
    detail_headers = ['用户ID', '行为类型', '总交易数', '小额交易数', '大额交易数', '总金额(USDT)', '平均金额(USDT)', '币种数', '是否团伙', '首次交易']
    for col, header in enumerate(detail_headers, 1):
        cell = ws.cell(row=17, column=col, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')
    
    # 按总金额排序，显示前500个用户
    user_behavior.sort(key=lambda x: x['total_amount'], reverse=True)
    
    for i, user in enumerate(user_behavior[:500], 18):
        ws.cell(row=i, column=1, value=user['user_id'])
        ws.cell(row=i, column=2, value=user['behavior_type'])
        ws.cell(row=i, column=3, value=user['total_transactions'])
        ws.cell(row=i, column=4, value=user['below_200_count'])
        ws.cell(row=i, column=5, value=user['above_200_count'])
        ws.cell(row=i, column=6, value=f"{user['total_amount']:.2f}")
        ws.cell(row=i, column=7, value=f"{user['avg_amount']:.2f}")
        ws.cell(row=i, column=8, value=user['currency_count'])
        ws.cell(row=i, column=9, value='是' if user['is_in_group'] else '否')
        ws.cell(row=i, column=10, value=user['first_transaction'].strftime('%Y-%m-%d'))
        
        # 根据行为类型着色
        if user['behavior_type'] == '纯大额用户':
            for col in range(1, 11):
                ws.cell(row=i, column=col).fill = PatternFill(start_color='FFE6E6', end_color='FFE6E6', fill_type='solid')
        elif user['behavior_type'] == '大额主导用户':
            for col in range(1, 11):
                ws.cell(row=i, column=col).fill = PatternFill(start_color='FFF2E6', end_color='FFF2E6', fill_type='solid')
    
    # 调整列宽
    for col in range(1, 11):
        ws.column_dimensions[chr(64 + col)].width = 12

def create_group_behavior_sheet(wb, amount_data, group_addresses):
    """创建团伙行为分析工作表"""
    ws = wb.create_sheet("团伙行为分析")

    # 标题
    ws['A1'] = '团伙金额行为分析'
    ws['A1'].font = Font(size=16, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center')
    ws.merge_cells('A1:I1')

    # 分析每个团伙的金额行为
    group_behaviors = []

    for address in group_addresses:
        group_data = amount_data[amount_data['address'] == address]
        group_below_200 = group_data[group_data['currency_usdt_amount'] < 200]
        group_above_200 = group_data[group_data['currency_usdt_amount'] >= 200]

        if len(group_data) == 0:
            continue

        # 分类团伙行为
        below_ratio = len(group_below_200) / len(group_data)
        if below_ratio >= 0.8:
            group_type = '小额主导团伙'
        elif below_ratio <= 0.2:
            group_type = '大额主导团伙'
        else:
            group_type = '混合型团伙'

        group_behaviors.append({
            'address': address,
            'group_type': group_type,
            'member_count': group_data['member_id'].nunique(),
            'total_transactions': len(group_data),
            'below_200_count': len(group_below_200),
            'above_200_count': len(group_above_200),
            'below_200_ratio': below_ratio,
            'total_amount': group_data['currency_usdt_amount'].sum(),
            'avg_amount': group_data['currency_usdt_amount'].mean(),
            'currencies': list(group_data['currency'].unique())
        })

    # 团伙类型统计
    ws['A3'] = '团伙类型统计'
    ws['A3'].font = Font(size=14, bold=True)

    group_type_counter = Counter([g['group_type'] for g in group_behaviors])

    type_stats = [
        ['团伙类型', '团伙数量', '占比', '平均成员数', '平均交易数', '平均金额(USDT)'],
    ]

    for group_type, count in group_type_counter.items():
        type_groups = [g for g in group_behaviors if g['group_type'] == group_type]
        avg_members = np.mean([g['member_count'] for g in type_groups])
        avg_transactions = np.mean([g['total_transactions'] for g in type_groups])
        avg_amount = np.mean([g['avg_amount'] for g in type_groups])

        type_stats.append([
            group_type,
            count,
            f"{count/len(group_behaviors)*100:.1f}%",
            f"{avg_members:.1f}",
            f"{avg_transactions:.1f}",
            f"{avg_amount:.2f}"
        ])

    for i, row_data in enumerate(type_stats, 5):
        for j, value in enumerate(row_data, 1):
            cell = ws.cell(row=i, column=j, value=value)
            if i == 5:  # 表头
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')

    # 详细团伙列表
    ws['A12'] = '团伙详细列表'
    ws['A12'].font = Font(size=14, bold=True)

    detail_headers = ['团伙地址', '团伙类型', '成员数', '总交易数', '小额交易数', '大额交易数', '小额占比', '总金额(USDT)', '平均金额(USDT)']
    for col, header in enumerate(detail_headers, 1):
        cell = ws.cell(row=14, column=col, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')

    # 按成员数排序
    group_behaviors.sort(key=lambda x: x['member_count'], reverse=True)

    for i, group in enumerate(group_behaviors[:200], 15):  # 显示前200个团伙
        ws.cell(row=i, column=1, value=group['address'][:20] + '...')
        ws.cell(row=i, column=2, value=group['group_type'])
        ws.cell(row=i, column=3, value=group['member_count'])
        ws.cell(row=i, column=4, value=group['total_transactions'])
        ws.cell(row=i, column=5, value=group['below_200_count'])
        ws.cell(row=i, column=6, value=group['above_200_count'])
        ws.cell(row=i, column=7, value=f"{group['below_200_ratio']*100:.1f}%")
        ws.cell(row=i, column=8, value=f"{group['total_amount']:.2f}")
        ws.cell(row=i, column=9, value=f"{group['avg_amount']:.2f}")

        # 根据团伙类型着色
        if group['group_type'] == '大额主导团伙':
            for col in range(1, 10):
                ws.cell(row=i, column=col).fill = PatternFill(start_color='FFE6E6', end_color='FFE6E6', fill_type='solid')
        elif group['group_type'] == '小额主导团伙':
            for col in range(1, 10):
                ws.cell(row=i, column=col).fill = PatternFill(start_color='E6F3FF', end_color='E6F3FF', fill_type='solid')

    # 调整列宽
    for col in range(1, 10):
        ws.column_dimensions[chr(64 + col)].width = 12

def create_currency_preference_sheet(wb, below_200, above_200):
    """创建币种偏好分析工作表"""
    ws = wb.create_sheet("币种偏好分析")

    # 标题
    ws['A1'] = '不同金额区间的币种偏好分析'
    ws['A1'].font = Font(size=16, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center')
    ws.merge_cells('A1:G1')

    # 币种使用对比
    below_200_currencies = below_200['currency'].value_counts()
    above_200_currencies = above_200['currency'].value_counts()

    # 合并所有币种
    all_currencies = set(below_200_currencies.index) | set(above_200_currencies.index)

    ws['A3'] = '币种使用对比（前20种）'
    ws['A3'].font = Font(size=14, bold=True)

    currency_headers = ['币种', '200U以下交易数', '200U以下占比', '200U以上交易数', '200U以上占比', '偏好倾向', '差异指数']
    for col, header in enumerate(currency_headers, 1):
        cell = ws.cell(row=5, column=col, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')

    # 计算币种偏好
    currency_analysis = []
    for currency in all_currencies:
        below_count = below_200_currencies.get(currency, 0)
        above_count = above_200_currencies.get(currency, 0)
        total_count = below_count + above_count

        if total_count > 0:
            below_ratio = below_count / total_count
            above_ratio = above_count / total_count

            # 偏好倾向
            if below_ratio > 0.7:
                preference = '小额偏好'
            elif above_ratio > 0.7:
                preference = '大额偏好'
            else:
                preference = '均衡'

            # 差异指数（绝对差值）
            diff_index = abs(below_ratio - above_ratio)

            currency_analysis.append({
                'currency': currency,
                'below_count': below_count,
                'above_count': above_count,
                'total_count': total_count,
                'below_ratio': below_ratio,
                'above_ratio': above_ratio,
                'preference': preference,
                'diff_index': diff_index
            })

    # 按总交易数排序
    currency_analysis.sort(key=lambda x: x['total_count'], reverse=True)

    for i, curr in enumerate(currency_analysis[:20], 6):
        ws.cell(row=i, column=1, value=curr['currency'])
        ws.cell(row=i, column=2, value=curr['below_count'])
        ws.cell(row=i, column=3, value=f"{curr['below_ratio']*100:.1f}%")
        ws.cell(row=i, column=4, value=curr['above_count'])
        ws.cell(row=i, column=5, value=f"{curr['above_ratio']*100:.1f}%")
        ws.cell(row=i, column=6, value=curr['preference'])
        ws.cell(row=i, column=7, value=f"{curr['diff_index']:.3f}")

        # 根据偏好着色
        if curr['preference'] == '大额偏好':
            for col in range(1, 8):
                ws.cell(row=i, column=col).fill = PatternFill(start_color='FFE6E6', end_color='FFE6E6', fill_type='solid')
        elif curr['preference'] == '小额偏好':
            for col in range(1, 8):
                ws.cell(row=i, column=col).fill = PatternFill(start_color='E6F3FF', end_color='E6F3FF', fill_type='solid')

    # 调整列宽
    for col in range(1, 8):
        ws.column_dimensions[chr(64 + col)].width = 15

def create_time_pattern_sheet(wb, below_200, above_200):
    """创建时间模式分析工作表"""
    ws = wb.create_sheet("时间模式分析")

    # 标题
    ws['A1'] = '不同金额区间的时间模式分析'
    ws['A1'].font = Font(size=16, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center')
    ws.merge_cells('A1:F1')

    # 日期分析
    below_200_daily = below_200.groupby('dt').size()
    above_200_daily = above_200.groupby('dt').size()

    # 合并日期范围
    all_dates = sorted(set(below_200_daily.index) | set(above_200_daily.index))

    ws['A3'] = '每日交易量对比'
    ws['A3'].font = Font(size=14, bold=True)

    daily_headers = ['日期', '200U以下交易数', '200U以上交易数', '总交易数', '小额占比', '趋势']
    for col, header in enumerate(daily_headers, 1):
        cell = ws.cell(row=5, column=col, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')

    # 显示最近30天的数据
    recent_dates = all_dates[-30:] if len(all_dates) >= 30 else all_dates

    for i, date in enumerate(recent_dates, 6):
        below_count = below_200_daily.get(date, 0)
        above_count = above_200_daily.get(date, 0)
        total_count = below_count + above_count
        below_ratio = below_count / total_count if total_count > 0 else 0

        # 趋势分析
        if below_ratio > 0.8:
            trend = '小额主导'
        elif below_ratio < 0.5:
            trend = '大额增多'
        else:
            trend = '均衡'

        ws.cell(row=i, column=1, value=date.strftime('%Y-%m-%d'))
        ws.cell(row=i, column=2, value=below_count)
        ws.cell(row=i, column=3, value=above_count)
        ws.cell(row=i, column=4, value=total_count)
        ws.cell(row=i, column=5, value=f"{below_ratio*100:.1f}%")
        ws.cell(row=i, column=6, value=trend)

    # 调整列宽
    for col in range(1, 7):
        ws.column_dimensions[chr(64 + col)].width = 15

def create_detailed_stats_sheet(wb, amount_data, below_200, above_200):
    """创建详细分类统计工作表"""
    ws = wb.create_sheet("详细分类统计")

    # 标题
    ws['A1'] = '详细分类统计汇总'
    ws['A1'].font = Font(size=16, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center')
    ws.merge_cells('A1:D1')

    # 金额区间细分统计
    ws['A3'] = '金额区间细分统计'
    ws['A3'].font = Font(size=14, bold=True)

    amount_ranges = [
        ('0-10U', 0, 10),
        ('10-50U', 10, 50),
        ('50-100U', 50, 100),
        ('100-200U', 100, 200),
        ('200-500U', 200, 500),
        ('500-1000U', 500, 1000),
        ('1000-5000U', 1000, 5000),
        ('5000-10000U', 5000, 10000),
        ('10000U以上', 10000, float('inf'))
    ]

    range_headers = ['金额区间', '交易数量', '占比', '用户数', '地址数', '总金额(USDT)', '平均金额(USDT)']
    for col, header in enumerate(range_headers, 1):
        cell = ws.cell(row=5, column=col, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')

    for i, (range_name, min_val, max_val) in enumerate(amount_ranges, 6):
        if max_val == float('inf'):
            range_data = amount_data[amount_data['currency_usdt_amount'] >= min_val]
        else:
            range_data = amount_data[(amount_data['currency_usdt_amount'] >= min_val) & (amount_data['currency_usdt_amount'] < max_val)]

        ws.cell(row=i, column=1, value=range_name)
        ws.cell(row=i, column=2, value=len(range_data))
        ws.cell(row=i, column=3, value=f"{len(range_data)/len(amount_data)*100:.1f}%")
        ws.cell(row=i, column=4, value=range_data['member_id'].nunique())
        ws.cell(row=i, column=5, value=range_data['address'].nunique())
        ws.cell(row=i, column=6, value=f"{range_data['currency_usdt_amount'].sum():,.2f}")
        ws.cell(row=i, column=7, value=f"{range_data['currency_usdt_amount'].mean():.2f}" if len(range_data) > 0 else "0")

        # 200U分界线着色
        if min_val < 200:
            for col in range(1, 8):
                ws.cell(row=i, column=col).fill = PatternFill(start_color='E6F3FF', end_color='E6F3FF', fill_type='solid')
        else:
            for col in range(1, 8):
                ws.cell(row=i, column=col).fill = PatternFill(start_color='FFE6E6', end_color='FFE6E6', fill_type='solid')

    # 调整列宽
    for col in range(1, 8):
        ws.column_dimensions[chr(64 + col)].width = 15

def main():
    """主函数"""
    print("开始金额行为分析...")

    # 1. 分析金额行为
    amount_data, below_200, above_200, group_addresses = analyze_amount_behaviors()

    # 2. 创建Excel报告
    filename = create_behavior_analysis_excel(amount_data, below_200, above_200, group_addresses)

    # 3. 输出摘要
    print(f"\n=== 分析摘要 ===")
    print(f"200U以下交易: {len(below_200):,} 笔")
    print(f"200U以上交易: {len(above_200):,} 笔")
    print(f"团伙地址数: {len(group_addresses):,} 个")
    print(f"团伙中200U以下交易: {len(below_200[below_200['is_group']]):,} 笔")
    print(f"团伙中200U以上交易: {len(above_200[above_200['is_group']]):,} 笔")

    print(f"\n✅ 金额行为分析完成: {filename}")
    print("包含6个工作表:")
    print("1. 行为对比分析 - 200U以下vs以上的对比")
    print("2. 用户行为分类 - 用户行为模式分类")
    print("3. 团伙行为分析 - 团伙的金额行为模式")
    print("4. 币种偏好分析 - 不同金额区间的币种偏好")
    print("5. 时间模式分析 - 时间维度的行为模式")
    print("6. 详细分类统计 - 细分金额区间统计")

if __name__ == "__main__":
    main()
