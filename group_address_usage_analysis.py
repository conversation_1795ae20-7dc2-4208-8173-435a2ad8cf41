#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
团伙地址使用频率分析
专门分析账户对团伙地址（3人以上共享地址）的使用频次
"""

import pandas as pd
import numpy as np
from collections import Counter
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment
import warnings
warnings.filterwarnings('ignore')

def analyze_group_address_usage():
    """分析团伙地址使用频率"""
    print("=== 团伙地址使用频率分析 ===")
    
    # 加载数据
    df = pd.read_csv('提币地址.csv')
    print(f"总交易记录: {len(df):,}")
    print(f"唯一账户数: {df['member_id'].nunique():,}")
    print(f"唯一地址数: {df['address'].nunique():,}")
    
    # 识别团伙地址（3人以上共享）
    print("\n正在识别团伙地址...")
    address_user_counts = df.groupby('address')['member_id'].nunique()
    group_addresses = address_user_counts[address_user_counts >= 3].index.tolist()
    
    print(f"团伙地址数量: {len(group_addresses):,}")
    print(f"团伙地址占比: {len(group_addresses)/df['address'].nunique()*100:.1f}%")
    
    # 筛选出涉及团伙地址的交易
    group_transactions = df[df['address'].isin(group_addresses)].copy()
    print(f"涉及团伙地址的交易: {len(group_transactions):,}")
    print(f"涉及团伙地址的账户: {group_transactions['member_id'].nunique():,}")
    
    # 计算每个账户对每个团伙地址的使用次数
    print("\n正在计算账户对团伙地址的使用频次...")
    group_usage = group_transactions.groupby(['member_id', 'address']).size().reset_index(name='usage_count')
    
    print(f"账户-团伙地址组合总数: {len(group_usage):,}")
    
    # 按使用次数分类
    usage_1_time = group_usage[group_usage['usage_count'] == 1]
    usage_2_3_times = group_usage[(group_usage['usage_count'] >= 2) & (group_usage['usage_count'] <= 3)]
    usage_more_than_3 = group_usage[group_usage['usage_count'] > 3]
    
    print(f"\n=== 团伙地址使用频次统计 ===")
    print(f"仅使用1次: {len(usage_1_time):,} 组合 ({len(usage_1_time)/len(group_usage)*100:.1f}%)")
    print(f"使用2-3次: {len(usage_2_3_times):,} 组合 ({len(usage_2_3_times)/len(group_usage)*100:.1f}%)")
    print(f"使用3次以上: {len(usage_more_than_3):,} 组合 ({len(usage_more_than_3)/len(group_usage)*100:.1f}%)")
    
    # 详细分布
    usage_distribution = group_usage['usage_count'].value_counts().sort_index()
    
    print(f"\n=== 详细使用次数分布 ===")
    print("使用次数 | 组合数量 | 占比")
    print("-" * 30)
    
    cumulative = 0
    total = len(group_usage)
    
    for usage_count, count in usage_distribution.head(20).items():
        cumulative += count
        print(f"{usage_count:8d} | {count:8,} | {count/total*100:5.1f}% (累计: {cumulative/total*100:5.1f}%)")
    
    # 高频使用分析
    high_freq_usage = group_usage[group_usage['usage_count'] > 10]
    
    if len(high_freq_usage) > 0:
        print(f"\n=== 团伙地址高频使用分析（>10次） ===")
        print(f"高频使用组合数: {len(high_freq_usage):,}")
        print(f"涉及账户数: {high_freq_usage['member_id'].nunique():,}")
        print(f"涉及团伙地址数: {high_freq_usage['address'].nunique():,}")
        print(f"最高使用次数: {high_freq_usage['usage_count'].max()}")
        print(f"平均使用次数: {high_freq_usage['usage_count'].mean():.1f}")
        
        # 显示前10个最高频使用
        print(f"\n前10个最高频使用:")
        top_usage = high_freq_usage.nlargest(10, 'usage_count')
        for _, row in top_usage.iterrows():
            address_users = address_user_counts[row['address']]
            print(f"  账户: {row['member_id']} | 地址用户数: {address_users} | 使用次数: {row['usage_count']}")
    else:
        print(f"\n=== 团伙地址高频使用分析 ===")
        print("未发现对团伙地址使用次数超过10次的情况")
    
    # 团伙地址规模与使用频次的关系
    print(f"\n=== 团伙规模与使用频次关系分析 ===")
    
    # 为使用数据添加团伙规模信息
    group_usage['group_size'] = group_usage['address'].map(address_user_counts)
    
    # 按团伙规模分类分析
    size_ranges = [
        ('小型团伙(3-10人)', 3, 10),
        ('中型团伙(11-50人)', 11, 50),
        ('大型团伙(51-100人)', 51, 100),
        ('超大型团伙(100人以上)', 101, float('inf'))
    ]
    
    for size_name, min_size, max_size in size_ranges:
        if max_size == float('inf'):
            size_usage = group_usage[group_usage['group_size'] >= min_size]
        else:
            size_usage = group_usage[(group_usage['group_size'] >= min_size) & (group_usage['group_size'] <= max_size)]
        
        if len(size_usage) > 0:
            avg_usage = size_usage['usage_count'].mean()
            max_usage = size_usage['usage_count'].max()
            high_freq_count = len(size_usage[size_usage['usage_count'] > 5])
            
            print(f"{size_name}:")
            print(f"  组合数: {len(size_usage):,}")
            print(f"  平均使用次数: {avg_usage:.1f}")
            print(f"  最高使用次数: {max_usage}")
            print(f"  高频使用组合(>5次): {high_freq_count} ({high_freq_count/len(size_usage)*100:.1f}%)")
    
    return group_usage, group_addresses, address_user_counts

def create_group_address_usage_excel(group_usage, group_addresses, address_user_counts, df):
    """创建团伙地址使用频率Excel报告"""
    print("\n=== 生成团伙地址使用频率Excel报告 ===")
    
    wb = openpyxl.Workbook()
    wb.remove(wb.active)
    
    # 1. 概览统计
    create_overview_sheet(wb, group_usage, group_addresses, address_user_counts, df)
    
    # 2. 使用频次详细分析
    create_frequency_detail_sheet(wb, group_usage)
    
    # 3. 高频使用账户列表
    create_high_frequency_users_sheet(wb, group_usage, address_user_counts)
    
    # 4. 团伙规模与使用频次关系
    create_size_frequency_relation_sheet(wb, group_usage)
    
    # 5. 团伙地址详情
    create_group_address_detail_sheet(wb, group_addresses, address_user_counts, df)
    
    filename = '团伙地址使用频率分析报告.xlsx'
    wb.save(filename)
    print(f"Excel报告已保存: {filename}")
    
    return filename

def create_overview_sheet(wb, group_usage, group_addresses, address_user_counts, df):
    """创建概览统计工作表"""
    ws = wb.create_sheet("概览统计")
    
    # 标题
    ws['A1'] = '团伙地址使用频率分析概览'
    ws['A1'].font = Font(size=16, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center')
    ws.merge_cells('A1:D1')
    
    # 基本统计
    ws['A3'] = '基本统计'
    ws['A3'].font = Font(size=14, bold=True)
    
    group_transactions = df[df['address'].isin(group_addresses)]
    
    basic_stats = [
        ['指标', '数值', '说明'],
        ['团伙地址总数', f"{len(group_addresses):,}", '3人以上共享的地址'],
        ['团伙地址占比', f"{len(group_addresses)/df['address'].nunique()*100:.1f}%", '占所有地址的比例'],
        ['涉及交易数', f"{len(group_transactions):,}", '使用团伙地址的交易'],
        ['涉及账户数', f"{group_transactions['member_id'].nunique():,}", '使用过团伙地址的账户'],
        ['账户-团伙地址组合数', f"{len(group_usage):,}", '账户使用团伙地址的组合总数'],
        ['', '', ''],
        ['使用频次分布', '', ''],
        ['仅使用1次', f"{len(group_usage[group_usage['usage_count'] == 1]):,}", f"{len(group_usage[group_usage['usage_count'] == 1])/len(group_usage)*100:.1f}%"],
        ['使用2-3次', f"{len(group_usage[(group_usage['usage_count'] >= 2) & (group_usage['usage_count'] <= 3)]):,}", f"{len(group_usage[(group_usage['usage_count'] >= 2) & (group_usage['usage_count'] <= 3)])/len(group_usage)*100:.1f}%"],
        ['使用3次以上', f"{len(group_usage[group_usage['usage_count'] > 3]):,}", f"{len(group_usage[group_usage['usage_count'] > 3])/len(group_usage)*100:.1f}%"],
        ['使用10次以上', f"{len(group_usage[group_usage['usage_count'] > 10]):,}", f"{len(group_usage[group_usage['usage_count'] > 10])/len(group_usage)*100:.1f}%"],
    ]
    
    for i, row_data in enumerate(basic_stats, 4):
        for j, value in enumerate(row_data, 1):
            cell = ws.cell(row=i, column=j, value=value)
            if i == 4 or i == 11:  # 表头行
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')
            elif '使用10次以上' in str(value):  # 高风险行
                cell.fill = PatternFill(start_color='FFE6E6', end_color='FFE6E6', fill_type='solid')
    
    # 团伙规模分布
    ws['A18'] = '团伙规模分布'
    ws['A18'].font = Font(size=14, bold=True)
    
    size_stats = [
        ['团伙规模', '地址数量', '占比', '平均使用次数'],
        ['3-10人', 0, '', ''],
        ['11-50人', 0, '', ''],
        ['51-100人', 0, '', ''],
        ['100人以上', 0, '', '']
    ]
    
    # 计算各规模团伙的统计
    size_ranges = [(3, 10), (11, 50), (51, 100), (101, float('inf'))]
    for i, (min_size, max_size) in enumerate(size_ranges, 1):
        if max_size == float('inf'):
            size_addresses = [addr for addr in group_addresses if address_user_counts[addr] >= min_size]
        else:
            size_addresses = [addr for addr in group_addresses if min_size <= address_user_counts[addr] <= max_size]
        
        size_usage = group_usage[group_usage['address'].isin(size_addresses)]
        avg_usage = size_usage['usage_count'].mean() if len(size_usage) > 0 else 0
        
        size_stats[i][1] = len(size_addresses)
        size_stats[i][2] = f"{len(size_addresses)/len(group_addresses)*100:.1f}%"
        size_stats[i][3] = f"{avg_usage:.1f}"
    
    for i, row_data in enumerate(size_stats, 20):
        for j, value in enumerate(row_data, 1):
            cell = ws.cell(row=i, column=j, value=value)
            if i == 20:  # 表头
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')
    
    # 调整列宽
    for col in range(1, 5):
        ws.column_dimensions[chr(64 + col)].width = 20

def create_frequency_detail_sheet(wb, group_usage):
    """创建使用频次详细分析工作表"""
    ws = wb.create_sheet("使用频次详细分析")
    
    # 标题
    ws['A1'] = '团伙地址使用频次详细分析'
    ws['A1'].font = Font(size=16, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center')
    ws.merge_cells('A1:E1')
    
    # 详细分布表
    usage_distribution = group_usage['usage_count'].value_counts().sort_index()
    
    ws['A3'] = '使用频次分布表'
    ws['A3'].font = Font(size=14, bold=True)
    
    headers = ['使用次数', '组合数量', '占比', '累计数量', '累计占比']
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=5, column=col, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')
    
    cumulative_count = 0
    total_combinations = len(group_usage)
    
    for i, (usage_count, count) in enumerate(usage_distribution.items(), 6):
        cumulative_count += count
        ws.cell(row=i, column=1, value=usage_count)
        ws.cell(row=i, column=2, value=count)
        ws.cell(row=i, column=3, value=f"{count/total_combinations*100:.1f}%")
        ws.cell(row=i, column=4, value=cumulative_count)
        ws.cell(row=i, column=5, value=f"{cumulative_count/total_combinations*100:.1f}%")
        
        # 高频使用着色
        if usage_count > 20:
            for col in range(1, 6):
                ws.cell(row=i, column=col).fill = PatternFill(start_color='FF9999', end_color='FF9999', fill_type='solid')
        elif usage_count > 10:
            for col in range(1, 6):
                ws.cell(row=i, column=col).fill = PatternFill(start_color='FFE6E6', end_color='FFE6E6', fill_type='solid')
        elif usage_count > 5:
            for col in range(1, 6):
                ws.cell(row=i, column=col).fill = PatternFill(start_color='FFF2E6', end_color='FFF2E6', fill_type='solid')
    
    # 调整列宽
    for col in range(1, 6):
        ws.column_dimensions[chr(64 + col)].width = 15

def create_high_frequency_users_sheet(wb, group_usage, address_user_counts):
    """创建高频使用账户列表工作表"""
    ws = wb.create_sheet("高频使用账户")

    # 标题
    ws['A1'] = '高频使用团伙地址的账户列表'
    ws['A1'].font = Font(size=16, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center')
    ws.merge_cells('A1:G1')

    # 筛选高频使用（>3次）
    high_freq_usage = group_usage[group_usage['usage_count'] > 3].copy()
    high_freq_usage['group_size'] = high_freq_usage['address'].map(address_user_counts)
    high_freq_usage = high_freq_usage.sort_values('usage_count', ascending=False)

    ws['A3'] = f'高频使用统计（使用次数>3次，共{len(high_freq_usage)}个组合）'
    ws['A3'].font = Font(size=14, bold=True)

    headers = ['账户ID', '团伙地址', '使用次数', '团伙规模', '风险等级', '地址类型', '备注']
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=5, column=col, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')

    # 填充数据
    for i, (_, row) in enumerate(high_freq_usage.iterrows(), 6):
        usage_count = row['usage_count']
        group_size = row['group_size']

        # 风险等级评估
        if usage_count > 20:
            risk_level = '极高风险'
            risk_color = 'FF9999'
        elif usage_count > 10:
            risk_level = '高风险'
            risk_color = 'FFE6E6'
        elif usage_count > 5:
            risk_level = '中风险'
            risk_color = 'FFF2E6'
        else:
            risk_level = '低风险'
            risk_color = None

        # 地址类型
        if group_size > 100:
            address_type = '超大型团伙'
        elif group_size > 50:
            address_type = '大型团伙'
        elif group_size > 10:
            address_type = '中型团伙'
        else:
            address_type = '小型团伙'

        # 备注
        notes = []
        if usage_count > 50:
            notes.append('极端高频')
        if group_size > 100:
            notes.append('超大团伙')

        ws.cell(row=i, column=1, value=row['member_id'])
        ws.cell(row=i, column=2, value=row['address'][:30] + '...')
        ws.cell(row=i, column=3, value=usage_count)
        ws.cell(row=i, column=4, value=group_size)
        ws.cell(row=i, column=5, value=risk_level)
        ws.cell(row=i, column=6, value=address_type)
        ws.cell(row=i, column=7, value=', '.join(notes) if notes else '正常')

        # 根据风险等级着色
        if risk_color:
            for col in range(1, 8):
                ws.cell(row=i, column=col).fill = PatternFill(start_color=risk_color, end_color=risk_color, fill_type='solid')

    # 调整列宽
    for col in range(1, 8):
        ws.column_dimensions[chr(64 + col)].width = 15

def create_size_frequency_relation_sheet(wb, group_usage):
    """创建团伙规模与使用频次关系工作表"""
    ws = wb.create_sheet("规模频次关系")

    # 标题
    ws['A1'] = '团伙规模与使用频次关系分析'
    ws['A1'].font = Font(size=16, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center')
    ws.merge_cells('A1:H1')

    # 按团伙规模分组分析
    size_ranges = [
        ('小型团伙(3-10人)', 3, 10),
        ('中型团伙(11-50人)', 11, 50),
        ('大型团伙(51-100人)', 51, 100),
        ('超大型团伙(100人以上)', 101, float('inf'))
    ]

    ws['A3'] = '团伙规模与使用频次统计'
    ws['A3'].font = Font(size=14, bold=True)

    headers = ['团伙规模', '组合数量', '平均使用次数', '最高使用次数', '高频组合数(>5次)', '高频占比', '超高频组合数(>10次)', '超高频占比']
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=5, column=col, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')

    for i, (size_name, min_size, max_size) in enumerate(size_ranges, 6):
        if max_size == float('inf'):
            size_usage = group_usage[group_usage['group_size'] >= min_size]
        else:
            size_usage = group_usage[(group_usage['group_size'] >= min_size) & (group_usage['group_size'] <= max_size)]

        if len(size_usage) > 0:
            avg_usage = size_usage['usage_count'].mean()
            max_usage = size_usage['usage_count'].max()
            high_freq_count = len(size_usage[size_usage['usage_count'] > 5])
            super_high_freq_count = len(size_usage[size_usage['usage_count'] > 10])

            ws.cell(row=i, column=1, value=size_name)
            ws.cell(row=i, column=2, value=len(size_usage))
            ws.cell(row=i, column=3, value=f"{avg_usage:.1f}")
            ws.cell(row=i, column=4, value=max_usage)
            ws.cell(row=i, column=5, value=high_freq_count)
            ws.cell(row=i, column=6, value=f"{high_freq_count/len(size_usage)*100:.1f}%")
            ws.cell(row=i, column=7, value=super_high_freq_count)
            ws.cell(row=i, column=8, value=f"{super_high_freq_count/len(size_usage)*100:.1f}%")

            # 超大型团伙着色
            if '超大型' in size_name:
                for col in range(1, 9):
                    ws.cell(row=i, column=col).fill = PatternFill(start_color='FFE6E6', end_color='FFE6E6', fill_type='solid')
        else:
            ws.cell(row=i, column=1, value=size_name)
            for col in range(2, 9):
                ws.cell(row=i, column=col, value=0)

    # 调整列宽
    for col in range(1, 9):
        ws.column_dimensions[chr(64 + col)].width = 12

def create_group_address_detail_sheet(wb, group_addresses, address_user_counts, df):
    """创建团伙地址详情工作表"""
    ws = wb.create_sheet("团伙地址详情")

    # 标题
    ws['A1'] = '团伙地址详细信息'
    ws['A1'].font = Font(size=16, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center')
    ws.merge_cells('A1:I1')

    # 获取每个团伙地址的详细信息
    group_details = []
    for address in group_addresses:
        address_data = df[df['address'] == address]
        user_count = address_user_counts[address]

        # 计算使用频次统计
        usage_stats = address_data.groupby('member_id').size()

        detail = {
            'address': address,
            'user_count': user_count,
            'total_transactions': len(address_data),
            'avg_transactions_per_user': len(address_data) / user_count,
            'max_usage_by_single_user': usage_stats.max(),
            'min_usage_by_single_user': usage_stats.min(),
            'users_with_high_usage': len(usage_stats[usage_stats > 5]),
            'currencies': list(address_data['currency'].unique()),
            'date_range': f"{address_data['dt'].min().strftime('%Y-%m-%d')} 至 {address_data['dt'].max().strftime('%Y-%m-%d')}"
        }
        group_details.append(detail)

    # 按用户数量排序
    group_details.sort(key=lambda x: x['user_count'], reverse=True)

    headers = ['团伙地址', '用户数', '总交易数', '人均交易数', '单人最高使用', '单人最低使用', '高频用户数(>5次)', '币种数', '活动时间']
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=3, column=col, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')

    # 填充数据（显示前200个）
    for i, detail in enumerate(group_details[:200], 4):
        ws.cell(row=i, column=1, value=detail['address'][:30] + '...')
        ws.cell(row=i, column=2, value=detail['user_count'])
        ws.cell(row=i, column=3, value=detail['total_transactions'])
        ws.cell(row=i, column=4, value=f"{detail['avg_transactions_per_user']:.1f}")
        ws.cell(row=i, column=5, value=detail['max_usage_by_single_user'])
        ws.cell(row=i, column=6, value=detail['min_usage_by_single_user'])
        ws.cell(row=i, column=7, value=detail['users_with_high_usage'])
        ws.cell(row=i, column=8, value=len(detail['currencies']))
        ws.cell(row=i, column=9, value=detail['date_range'])

        # 超大型团伙着色
        if detail['user_count'] > 100:
            for col in range(1, 10):
                ws.cell(row=i, column=col).fill = PatternFill(start_color='FFE6E6', end_color='FFE6E6', fill_type='solid')
        elif detail['max_usage_by_single_user'] > 20:
            for col in range(1, 10):
                ws.cell(row=i, column=col).fill = PatternFill(start_color='FFF2E6', end_color='FFF2E6', fill_type='solid')

    # 调整列宽
    for col in range(1, 10):
        ws.column_dimensions[chr(64 + col)].width = 12

def main():
    """主函数"""
    print("开始团伙地址使用频率分析...")

    # 1. 分析团伙地址使用频率
    group_usage, group_addresses, address_user_counts = analyze_group_address_usage()

    # 2. 创建Excel报告
    df = pd.read_csv('提币地址.csv')
    df['dt'] = pd.to_datetime(df['dt'], format='%Y%m%d')

    filename = create_group_address_usage_excel(group_usage, group_addresses, address_user_counts, df)

    # 3. 输出关键发现
    print(f"\n=== 关键发现 ===")
    high_freq_usage = group_usage[group_usage['usage_count'] > 10]
    super_high_freq = group_usage[group_usage['usage_count'] > 20]

    print(f"团伙地址总数: {len(group_addresses):,}")
    print(f"账户-团伙地址组合: {len(group_usage):,}")
    print(f"高频使用组合(>10次): {len(high_freq_usage):,}")
    print(f"超高频使用组合(>20次): {len(super_high_freq):,}")

    if len(high_freq_usage) > 0:
        print(f"最高使用次数: {group_usage['usage_count'].max()}")
        print(f"涉及高频使用的账户: {high_freq_usage['member_id'].nunique():,}")
        print(f"涉及高频使用的团伙地址: {high_freq_usage['address'].nunique():,}")

    print(f"\n✅ 团伙地址使用频率分析完成: {filename}")
    print("包含5个工作表:")
    print("1. 概览统计 - 整体统计信息")
    print("2. 使用频次详细分析 - 详细的频次分布")
    print("3. 高频使用账户 - 高频使用的账户列表")
    print("4. 规模频次关系 - 团伙规模与使用频次的关系")
    print("5. 团伙地址详情 - 每个团伙地址的详细信息")

if __name__ == "__main__":
    main()
