# SQL查询文件说明

## 文件概述

本目录包含3个SQL查询文件，用于查询member_id相关的数据：

- `member_query_part_1.sql` - 包含7,388个member_id
- `member_query_part_2.sql` - 包含7,388个member_id  
- `member_query_part_3.sql` - 包含7,387个member_id

**总计：22,163个唯一的member_id**

## 数据来源

这些SQL文件是从Excel文件 `地址数据 2.xlsx` 中提取的member_id数据生成的。原始Excel文件包含28,215行数据，去重后得到22,163个唯一的member_id。

## SQL查询说明

每个SQL文件都包含相同的查询结构：

```sql
SELECT DISTINCT 
    member_id,
    digital_id,
    last_login_country,
    first_operation_country,
    kyc_country,
    member_country,
    standard_language,
    origin
FROM dw_dim.dim_hierarchy_t_ourbit_member_a_daily
WHERE dt BETWEEN '20250208' AND '20250807'
  AND member_id IN (
    -- 这里是member_id列表
  );
```

## 使用方法

1. 将SQL文件复制到你的数据库查询工具中
2. 根据需要调整日期范围（当前设置为 '20250208' 到 '20250807'）
3. 执行查询获取结果
4. 可以分别执行3个文件，或者根据需要合并结果

## 注意事项

- 每个文件包含大约7,000多个member_id，请确保你的数据库能够处理这样的IN子句长度
- 如果遇到性能问题，可以考虑进一步拆分文件
- 查询结果可能会很大，请确保有足够的存储空间和处理能力

## 生成脚本

这些文件是通过 `generate_sql_files.py` 脚本自动生成的。如果需要重新生成或修改，可以编辑该脚本。
