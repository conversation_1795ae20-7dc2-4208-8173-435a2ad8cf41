#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导出8月7号上午11点以后的测试数据
"""

import sqlite3
import json
from datetime import datetime

def export_data_after_time():
    """导出8月7号上午11点以后的数据"""
    print("🚀 导出8月7号上午11点以后的测试数据...")
    
    try:
        # 连接数据库
        conn = sqlite3.connect('wash_trading_test.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 设置目标时间
        target_time = "2025-08-07 11:00:00"
        print(f"📅 目标时间: {target_time}")
        
        # 查询8月7号上午11点以后的数据
        sql = """
        SELECT * FROM trade_records 
        WHERE created_at >= ? 
        ORDER BY created_at DESC
        """
        
        cursor.execute(sql, (target_time,))
        rows = cursor.fetchall()
        
        print(f"✅ 找到 {len(rows)} 条记录")
        
        if len(rows) == 0:
            print("❌ 没有找到符合条件的数据")
            
            # 查看最新的几条记录的时间
            cursor.execute("SELECT created_at FROM trade_records ORDER BY created_at DESC LIMIT 10")
            latest_records = cursor.fetchall()
            
            print("📊 最新的10条记录时间:")
            for i, record in enumerate(latest_records):
                print(f"  {i+1}. {record[0]}")
            
            conn.close()
            return
        
        # 转换为字典列表
        records = []
        for row in rows:
            record = dict(row)
            records.append(record)
        
        # 分析数据
        print(f"\n📊 数据分析:")
        
        # 时间范围
        earliest_time = records[-1]['created_at']  # 最早的
        latest_time = records[0]['created_at']     # 最新的
        print(f"  时间范围: {earliest_time} 到 {latest_time}")
        
        # 币种分布
        coin_stats = {}
        for record in records:
            coin = record['coin']
            coin_stats[coin] = coin_stats.get(coin, 0) + 1
        
        print(f"  币种分布:")
        for coin, count in sorted(coin_stats.items()):
            print(f"    {coin}: {count} 条")
        
        # 测试类型分布
        test_type_stats = {}
        for record in records:
            test_type = record['test_type']
            test_type_stats[test_type] = test_type_stats.get(test_type, 0) + 1
        
        print(f"  测试类型分布:")
        for test_type, count in sorted(test_type_stats.items()):
            print(f"    {test_type}: {count} 条")
        
        # 成功率统计
        success_count = sum(1 for record in records if record['trade_success'] == 1)
        success_rate = success_count / len(records) * 100
        print(f"  交易成功率: {success_rate:.1f}% ({success_count}/{len(records)})")
        
        # 创建导出数据包
        export_data = {
            'export_info': {
                'export_time': datetime.now().isoformat(),
                'filter_condition': f'created_at >= {target_time}',
                'total_records': len(records),
                'time_range': {
                    'earliest': earliest_time,
                    'latest': latest_time
                },
                'statistics': {
                    'coin_distribution': coin_stats,
                    'test_type_distribution': test_type_stats,
                    'success_rate': success_rate,
                    'success_count': success_count
                }
            },
            'records': records
        }
        
        # 保存数据
        filename = f"test_data_0807_after_11am_{len(records)}records.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"\n✅ 数据已导出到: {filename}")
        
        # 显示一些示例记录
        print(f"\n📋 示例记录 (最新的3条):")
        for i, record in enumerate(records[:3]):
            print(f"\n  记录 {i+1}:")
            print(f"    test_case_id: {record['test_case_id']}")
            print(f"    coin: {record['coin']}")
            print(f"    usdt_amount: {record['usdt_amount']}")
            print(f"    profit_loss: {record['profit_loss']}")
            print(f"    created_at: {record['created_at']}")
            print(f"    test_type: {record['test_type']}")
        
        conn.close()
        return filename
        
    except Exception as e:
        print(f"❌ 导出失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """主函数"""
    filename = export_data_after_time()
    
    if filename:
        print(f"\n🎉 导出完成！")
        print(f"📁 文件: {filename}")
        print(f"💡 可以用这个文件进行进一步的分析")
    else:
        print(f"\n❌ 导出失败")

if __name__ == "__main__":
    main()
