# 8月7号上午11点后测试数据导出摘要

## 📊 导出概况

- **导出时间**: 2025-08-10 01:39:43
- **过滤条件**: created_at >= 2025-08-07 11:00:00
- **导出记录数**: 3,146 条
- **数据文件**: `test_data_0807_after_11am_3146records.json`

## 📅 时间范围

- **最早记录**: 2025-08-07 11:52:01
- **最新记录**: 2025-08-08 16:16:17
- **时间跨度**: 约28.5小时

## 📈 数据分布

### 币种分布
| 币种 | 记录数 | 占比 |
|------|--------|------|
| BTC | 1,052 | 33.4% |
| ETH | 1,049 | 33.3% |
| DOG | 1,045 | 33.2% |
| **总计** | **3,146** | **100%** |

### 测试类型分布
| 测试类型 | 记录数 | 占比 |
|----------|--------|------|
| normal | 2,011 | 63.9% |
| wash_trading_suspect | 1,135 | 36.1% |
| **总计** | **3,146** | **100%** |

## ✅ 数据质量

- **交易成功率**: 100% (3,146/3,146)
- **数据完整性**: 所有记录都包含完整的字段信息
- **时间连续性**: 数据时间连续，无明显断层

## 📋 记录结构

每条记录包含以下主要字段：
- `record_id`: 记录唯一标识
- `test_case_id`: 测试案例ID
- `coin`: 币种 (BTC/ETH/DOG)
- `usdt_amount`: USDT交易金额
- `profit_loss`: 盈亏金额
- `buy_time`: 开仓时间
- `sell_time`: 平仓时间
- `created_at`: 记录创建时间
- `test_type`: 测试类型

## 🎯 用途说明

这批数据可用于：

1. **最新算法测试**: 验证最新的对敲检测算法效果
2. **时间序列分析**: 分析交易时间模式
3. **盈亏分析**: 研究不同币种和测试类型的盈亏特征
4. **算法优化**: 作为新的测试集进行算法调优

## 📁 文件信息

- **文件名**: `test_data_0807_after_11am_3146records.json`
- **文件大小**: 约94,406行
- **格式**: JSON格式，包含元数据和记录数组
- **编码**: UTF-8

## 💡 建议

1. **立即分析**: 可以用这批数据测试之前分析的算法优化方案
2. **对比验证**: 与之前的测试数据对比，验证算法改进效果
3. **持续监控**: 建议定期导出最新数据进行持续分析

---

**导出完成时间**: 2025-08-10 01:39:43  
**数据来源**: wash_trading_test.db  
**导出工具**: export_0807_data.py
