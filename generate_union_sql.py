#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成UNION ALL形式的SQL查询语句
"""

import pandas as pd
import math

def generate_union_sql(excel_file_path, batch_size=1000):
    """
    生成UNION ALL形式的SQL查询
    """
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_file_path)
        member_ids = df.iloc[:, 0].dropna().unique().tolist()
        
        print(f"总共找到 {len(member_ids)} 个唯一的member_id")
        
        # 计算需要分成多少批
        total_batches = math.ceil(len(member_ids) / batch_size)
        
        # 生成UNION ALL查询
        union_queries = []
        
        for i in range(total_batches):
            start_idx = i * batch_size
            end_idx = min((i + 1) * batch_size, len(member_ids))
            batch_member_ids = member_ids[start_idx:end_idx]
            
            # 构建IN子句
            in_clause = ",\n        ".join([f"'{member_id}'" for member_id in batch_member_ids])
            
            # 构建查询部分
            query_part = f"""    SELECT DISTINCT 
        member_id,
        digital_id,
        last_login_country,
        first_operation_country,
        kyc_country,
        member_country,
        standard_language,
        origin
    FROM dw_dim.dim_hierarchy_t_ourbit_member_a_daily
    WHERE dt BETWEEN '20250708' AND '20250807'
      AND member_id IN (
        {in_clause}
      )"""
            
            union_queries.append(query_part)
        
        # 组合成完整的UNION ALL查询
        full_query = "-- 完整的UNION ALL查询，包含所有member_id\n"
        full_query += "\n\nUNION ALL\n\n".join(union_queries)
        full_query += ";"
        
        return full_query
        
    except Exception as e:
        print(f"处理文件时出错: {e}")
        return ""

def save_union_sql_to_file(sql_query, output_file='member_union_query.sql'):
    """
    将UNION ALL查询保存到文件
    """
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(sql_query)
        
        print(f"UNION ALL查询已保存到文件: {output_file}")
        
    except Exception as e:
        print(f"保存文件时出错: {e}")

if __name__ == "__main__":
    excel_file = "地址数据.xlsx"
    batch_size = 1000
    
    # 生成UNION ALL查询
    union_query = generate_union_sql(excel_file, batch_size)
    
    if union_query:
        # 保存到文件
        save_union_sql_to_file(union_query)
        
        # 显示查询的开头部分
        print("\n" + "="*50)
        print("UNION ALL查询开头部分:")
        print("="*50)
        print(union_query[:2000] + "..." if len(union_query) > 2000 else union_query)
        
        print(f"\n完整的UNION ALL查询已保存到 member_union_query.sql 文件中")
    else:
        print("未能生成UNION ALL查询")
