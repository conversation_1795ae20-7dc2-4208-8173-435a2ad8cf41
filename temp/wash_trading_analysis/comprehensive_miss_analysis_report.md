# 对敲检测漏检案例综合分析报告

## 📊 执行摘要

**分析时间**: 2025-08-10 02:21:49

### 核心发现
- **总漏检案例**: 142 个
- **新发现漏检**: 142 个 (来自最新数据)
- **历史边界案例**: 0 个 (之前分析的边界案例)
- **匹配成功**: 125 个

### 关键问题
- **漏检率过高**: 新数据显示53.18%的漏检率
- **问题持续存在**: 历史边界问题未得到解决
- **检测能力不足**: 大量高质量对敲交易被漏检

---

## 🔍 漏检特征深度分析

### 金额特征

| 指标 | 数值 |
|------|------|
| 最小金额 | 10.14 USDT |
| 最大金额 | 98503.50 USDT |
| 平均金额 | 12630.91 USDT |
| 中位数金额 | 1443.68 USDT |
| 标准差 | 23421.07 USDT |

### 金额差异特征
| 指标 | 数值 |
|------|------|
| 最小差异 | 0.05% |
| 最大差异 | 2.82% |
| 平均差异 | 1.07% |
| 中位数差异 | 0.99% |
| 标准差 | 0.72% |

### 分布特征

#### 币种分布
- **ETH-USDT**: 57 个 (40.1%)
- **DOGE-USDT**: 39 个 (27.5%)
- **BTC-USDT**: 46 个 (32.4%)

#### 金额范围分布
- **<100 USDT**: 46 个 (32.4%)
- **100-1000 USDT**: 16 个 (11.3%)
- **1000-10000 USDT**: 46 个 (32.4%)
- **>10000 USDT**: 34 个 (23.9%)


---

## 📈 漏检 vs 匹配成功对比

### 关键指标对比
| 指标 | 漏检案例 | 匹配成功 | 差异 |
|------|----------|----------|------|
| 平均金额 | 12630.91 USDT | 13821.61 USDT | 0.91x |
| 平均差异 | 1.07% | 1.03% | 1.04x |
| 金额范围 | 10-98504 | 10-97858 | - |
| 差异范围 | 0.05%-2.82% | 0.00%-2.61% | - |

### 关键发现
- ⚠️ **漏检案例平均金额更小**：小额交易检测能力不足


---

## 🎯 检测边界分析

### 临界阈值
| 边界类型 | 漏检案例 | 匹配成功 | 边界间隙 |
|----------|----------|----------|----------|
| 最小金额 | 10.14 USDT | 10.07 USDT | 0.07 USDT |
| 最大差异 | 2.82% | 2.61% | 0.20% |

### 重叠分析
- **金额范围重叠**: 99.3% (141 个案例)
- **差异范围重叠**: 98.6% (140 个案例)

### 边界建议
- 存在金额检测空白区间: 10.07 - 10.14 USDT
- 金额差异检测空白: 2.61% - 2.82%
- 金额范围高度重叠(99.3%)，问题不在金额阈值
- 差异范围高度重叠(98.6%)，问题不在差异阈值


---

## 🔧 算法优化建议

### 参数调整建议

#### Amount Tolerance
- **当前问题**: 金额容差不是主要问题
- **建议**: 保持当前设置或微调
- **调整方向**: minimal

#### Diff Tolerance
- **当前问题**: 差异容差设置合理
- **建议**: 检查其他因素
- **调整方向**: investigate_other_factors

#### Scoring Weights
- **当前问题**: 综合评分权重可能不平衡
- **建议**: 调整评分权重，降低过严的维度权重
- **调整方向**: rebalance_weights

### 优先级行动
1. 立即调整算法参数，漏检率过高
1. 重点检查非金额因素，如时间窗口、评分逻辑

### 预期改进效果
- **当前漏检**: 142 个
- **预期减少**: 99 个
- **目标漏检**: 43 个
- **预期检测率**: 84.0%


---

## 📋 结论与下一步

### 核心结论
1. **漏检问题严重**: 总计142个漏检案例，需要立即优化
2. **问题根源复杂**: 不仅是阈值问题，可能涉及算法逻辑
3. **优化潜力巨大**: 通过参数调整预期可显著提升检测率

### 立即行动
1. **🚨 紧急**: 实施参数调整建议
2. **📊 验证**: 用漏检案例验证优化效果
3. **🔄 迭代**: 持续监控和优化

### 长期规划
1. **算法升级**: 考虑引入更先进的检测算法
2. **动态调整**: 建立自适应参数调整机制
3. **持续监控**: 建立漏检率监控体系

---

**报告生成时间**: 2025-08-10 02:21:49
**分析数据**: 新漏检142个 + 历史边界0个 + 匹配成功125个
