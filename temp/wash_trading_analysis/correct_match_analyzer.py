#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确的对敲匹配分析器：
86条测试数据 vs 155条平台数据
重点分析测试数据中的漏检情况
"""

import json
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Tuple

class CorrectWashTradingAnalyzer:
    def __init__(self):
        self.test_pairs = []  # 86个测试配对
        self.platform_records = []  # 155条平台记录
        
    def load_data(self):
        """加载数据"""
        print("📊 加载数据...")
        
        # 加载测试数据（平台格式）
        with open('test_data_platform_format.json', 'r', encoding='utf-8') as f:
            test_data = json.load(f)
        
        # 加载平台数据（去重后）
        with open('platform_data_deduplicated.json', 'r', encoding='utf-8') as f:
            self.platform_records = json.load(f)
        
        # 将测试数据转换为配对格式
        self.test_pairs = self.convert_test_data_to_pairs(test_data)
        
        print(f"✅ 测试配对: {len(self.test_pairs)} 个")
        print(f"✅ 平台记录: {len(self.platform_records)} 条")
    
    def convert_test_data_to_pairs(self, test_data: List[Dict]) -> List[Dict]:
        """将测试数据转换为配对格式"""
        pairs = {}
        
        for record in test_data:
            test_case_id = record.get('test_case_id')
            if not test_case_id:
                continue
                
            if test_case_id not in pairs:
                pairs[test_case_id] = {'long': None, 'short': None}
            
            # 根据order_id判断多空方向
            order_id = record.get('order_id', '')
            if 'long' in order_id:
                pairs[test_case_id]['long'] = record
            elif 'short' in order_id:
                pairs[test_case_id]['short'] = record
        
        # 转换为配对列表
        test_pairs = []
        for case_id, pair in pairs.items():
            if pair['long'] and pair['short']:
                test_pairs.append({
                    'test_case_id': case_id,
                    'long_record': pair['long'],
                    'short_record': pair['short'],
                    'symbol': pair['long']['symbol'],
                    'long_amount': pair['long']['open_amount'],
                    'short_amount': pair['short']['open_amount'],
                    'create_time': pair['long']['create_time']  # 使用计算的创建时间
                })
        
        return test_pairs
    
    def calculate_match_score(self, test_pair: Dict, platform_record: Dict) -> Tuple[float, Dict]:
        """计算匹配分数"""
        score_details = {}
        
        # 1. 币种匹配 (必须匹配)
        if test_pair['symbol'] != platform_record.get('symbol'):
            return 0.0, {'reason': 'symbol_mismatch'}
        score_details['symbol_match'] = True
        
        # 2. 金额匹配 - 检查测试数据的多空金额是否与平台记录匹配
        test_amounts = [test_pair['long_amount'], test_pair['short_amount']]
        platform_amount = platform_record.get('open_amount', 0)
        platform_match_amount = platform_record.get('match_open_amount', 0)
        
        # 检查测试金额是否与平台金额匹配
        amount_matches = []
        for test_amount in test_amounts:
            # 与主记录金额匹配
            diff1 = abs(test_amount - platform_amount) / max(test_amount, platform_amount) if max(test_amount, platform_amount) > 0 else 0
            # 与匹配记录金额匹配
            diff2 = abs(test_amount - platform_match_amount) / max(test_amount, platform_match_amount) if max(test_amount, platform_match_amount) > 0 else 0
            
            min_diff = min(diff1, diff2)
            amount_matches.append(min_diff)
        
        best_amount_match = min(amount_matches)
        amount_score = max(0, 1 - best_amount_match * 10)  # 10%差异得0分
        score_details['amount_diff'] = best_amount_match
        score_details['amount_score'] = amount_score
        
        # 3. 时间匹配 - 放宽时间窗口到10分钟
        try:
            test_create_time = datetime.fromisoformat(test_pair['create_time'].replace(' ', 'T'))
            platform_create_time = datetime.fromisoformat(platform_record.get('create_time', '').replace(' ', 'T'))
            
            time_diff = abs((platform_create_time - test_create_time).total_seconds())
            time_score = max(0, 1 - time_diff / 600)  # 10分钟内满分，超过10分钟0分
            
            score_details['time_diff_seconds'] = time_diff
            score_details['time_score'] = time_score
        except:
            time_score = 0
            score_details['time_diff_seconds'] = float('inf')
            score_details['time_score'] = 0
        
        # 综合分数 - 降低阈值
        if amount_score < 0.3:  # 金额差异太大
            total_score = 0
        else:
            total_score = amount_score * 0.8 + time_score * 0.2  # 更重视金额匹配
        
        score_details['total_score'] = total_score
        return total_score, score_details
    
    def find_matches(self) -> Dict:
        """寻找匹配"""
        print("🔍 寻找匹配...")
        
        matched_test_pairs = []
        unmatched_test_pairs = []
        matched_platform_ids = set()
        
        for test_pair in self.test_pairs:
            best_match = None
            best_score = 0
            best_details = {}
            
            for platform_record in self.platform_records:
                platform_id = platform_record.get('id')
                if platform_id in matched_platform_ids:
                    continue
                
                score, details = self.calculate_match_score(test_pair, platform_record)
                
                if score > best_score and score >= 0.4:  # 降低匹配阈值
                    best_score = score
                    best_match = platform_record
                    best_details = details
            
            if best_match:
                matched_test_pairs.append({
                    'test_pair': test_pair,
                    'platform_record': best_match,
                    'match_score': best_score,
                    'match_details': best_details
                })
                matched_platform_ids.add(best_match.get('id'))
            else:
                unmatched_test_pairs.append(test_pair)
        
        # 排除平台数据中不被测试数据包含的记录
        relevant_platform_records = [
            record for record in self.platform_records 
            if record.get('id') in matched_platform_ids
        ]
        
        print(f"✅ 测试数据匹配成功: {len(matched_test_pairs)} 个")
        print(f"⚠️ 测试数据未匹配: {len(unmatched_test_pairs)} 个")
        print(f"ℹ️ 相关平台记录: {len(relevant_platform_records)} 条")
        print(f"🗑️ 排除的平台记录: {len(self.platform_records) - len(relevant_platform_records)} 条")
        
        return {
            'matched_test_pairs': matched_test_pairs,
            'unmatched_test_pairs': unmatched_test_pairs,
            'relevant_platform_records': relevant_platform_records,
            'excluded_platform_records': [
                record for record in self.platform_records 
                if record.get('id') not in matched_platform_ids
            ]
        }
    
    def analyze_unmatched_test_pairs(self, unmatched_pairs: List[Dict]) -> Dict:
        """分析未匹配的测试配对"""
        print("🔍 分析未匹配的测试配对...")
        
        analysis = {
            'total_count': len(unmatched_pairs),
            'by_symbol': {},
            'by_amount_range': {},
            'by_time_range': {},
            'potential_reasons': {},
            'details': []
        }
        
        for test_pair in unmatched_pairs:
            symbol = test_pair['symbol']
            amounts = [test_pair['long_amount'], test_pair['short_amount']]
            avg_amount = sum(amounts) / len(amounts)
            amount_diff_pct = abs(amounts[0] - amounts[1]) / max(amounts) * 100
            create_time = test_pair['create_time']
            
            # 按币种统计
            analysis['by_symbol'][symbol] = analysis['by_symbol'].get(symbol, 0) + 1
            
            # 按金额范围统计
            if avg_amount < 100:
                range_key = '<100'
            elif avg_amount < 1000:
                range_key = '100-1000'
            elif avg_amount < 10000:
                range_key = '1000-10000'
            else:
                range_key = '>10000'
            analysis['by_amount_range'][range_key] = analysis['by_amount_range'].get(range_key, 0) + 1
            
            # 分析可能的原因
            reasons = []
            if amount_diff_pct > 5:
                reasons.append('金额差异过大')
            if avg_amount < 50:
                reasons.append('金额过小')
            if avg_amount > 100000:
                reasons.append('金额过大')
            
            reason_key = ', '.join(reasons) if reasons else '未知原因'
            analysis['potential_reasons'][reason_key] = analysis['potential_reasons'].get(reason_key, 0) + 1
            
            # 详细信息
            analysis['details'].append({
                'test_case_id': test_pair['test_case_id'],
                'symbol': symbol,
                'long_amount': test_pair['long_amount'],
                'short_amount': test_pair['short_amount'],
                'amount_diff_pct': amount_diff_pct,
                'avg_amount': avg_amount,
                'create_time': create_time,
                'potential_reasons': reasons
            })
        
        return analysis
    
    def compare_matched_vs_unmatched(self, matched_pairs: List[Dict], unmatched_pairs: List[Dict]) -> Dict:
        """比较匹配和未匹配的数据特征"""
        print("📊 比较匹配与未匹配数据特征...")
        
        def extract_features(pairs, is_matched=True):
            features = {
                'amounts': [],
                'amount_diffs': [],
                'symbols': {},
                'avg_amount': 0,
                'avg_amount_diff': 0
            }
            
            for pair in pairs:
                if is_matched:
                    test_pair = pair['test_pair']
                else:
                    test_pair = pair
                
                amounts = [test_pair['long_amount'], test_pair['short_amount']]
                avg_amount = sum(amounts) / len(amounts)
                amount_diff_pct = abs(amounts[0] - amounts[1]) / max(amounts) * 100
                symbol = test_pair['symbol']
                
                features['amounts'].append(avg_amount)
                features['amount_diffs'].append(amount_diff_pct)
                features['symbols'][symbol] = features['symbols'].get(symbol, 0) + 1
            
            if features['amounts']:
                features['avg_amount'] = sum(features['amounts']) / len(features['amounts'])
                features['avg_amount_diff'] = sum(features['amount_diffs']) / len(features['amount_diffs'])
            
            return features
        
        matched_features = extract_features(matched_pairs, True)
        unmatched_features = extract_features(unmatched_pairs, False)
        
        return {
            'matched_features': matched_features,
            'unmatched_features': unmatched_features,
            'comparison': {
                'avg_amount_ratio': unmatched_features['avg_amount'] / matched_features['avg_amount'] if matched_features['avg_amount'] > 0 else 0,
                'avg_amount_diff_ratio': unmatched_features['avg_amount_diff'] / matched_features['avg_amount_diff'] if matched_features['avg_amount_diff'] > 0 else 0
            }
        }
    
    def generate_report(self, results: Dict) -> Dict:
        """生成分析报告"""
        print("📊 生成分析报告...")
        
        matched_pairs = results['matched_test_pairs']
        unmatched_pairs = results['unmatched_test_pairs']
        
        # 基础统计
        total_test_pairs = len(matched_pairs) + len(unmatched_pairs)
        match_rate = len(matched_pairs) / total_test_pairs * 100 if total_test_pairs > 0 else 0
        
        # 分析未匹配数据
        unmatched_analysis = self.analyze_unmatched_test_pairs(unmatched_pairs)
        
        # 比较匹配与未匹配数据
        comparison = self.compare_matched_vs_unmatched(matched_pairs, unmatched_pairs)
        
        report = {
            'summary': {
                'total_test_pairs': total_test_pairs,
                'matched_pairs': len(matched_pairs),
                'unmatched_pairs': len(unmatched_pairs),
                'match_rate_percent': round(match_rate, 2),
                'relevant_platform_records': len(results['relevant_platform_records']),
                'excluded_platform_records': len(results['excluded_platform_records'])
            },
            'matched_details': matched_pairs,
            'unmatched_analysis': unmatched_analysis,
            'feature_comparison': comparison,
            'excluded_platform_records': results['excluded_platform_records'],
            'generation_time': datetime.now().isoformat()
        }
        
        return report
    
    def save_results(self, report: Dict):
        """保存分析结果"""
        print("💾 保存分析结果...")
        
        # 保存完整报告
        with open('correct_wash_trading_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=str)
        print("✅ 完整报告: correct_wash_trading_analysis.json")
        
        # 保存未匹配测试数据（重点关注）
        with open('unmatched_test_pairs.json', 'w', encoding='utf-8') as f:
            json.dump(report['unmatched_analysis'], f, ensure_ascii=False, indent=2, default=str)
        print("✅ 未匹配测试数据: unmatched_test_pairs.json")
        
        # 保存排除的平台记录
        with open('excluded_platform_records.json', 'w', encoding='utf-8') as f:
            json.dump(report['excluded_platform_records'], f, ensure_ascii=False, indent=2, default=str)
        print("✅ 排除的平台记录: excluded_platform_records.json")
        
        # 打印摘要
        self.print_summary(report)
    
    def print_summary(self, report: Dict):
        """打印分析摘要"""
        summary = report['summary']
        unmatched = report['unmatched_analysis']
        comparison = report['feature_comparison']
        
        print("\n" + "="*60)
        print("📋 正确的对敲匹配分析摘要")
        print("="*60)
        
        print(f"📊 总体统计:")
        print(f"  测试配对总数: {summary['total_test_pairs']} 个")
        print(f"  匹配成功: {summary['matched_pairs']} 个")
        print(f"  匹配失败: {summary['unmatched_pairs']} 个")
        print(f"  匹配率: {summary['match_rate_percent']}%")
        
        print(f"\n🔍 重点关注 - 测试数据未匹配（可能漏检）:")
        print(f"  总数: {unmatched['total_count']} 个")
        if unmatched['by_symbol']:
            print(f"  按币种分布:")
            for symbol, count in unmatched['by_symbol'].items():
                print(f"    {symbol}: {count} 个")
        
        if unmatched['potential_reasons']:
            print(f"  可能原因:")
            for reason, count in unmatched['potential_reasons'].items():
                print(f"    {reason}: {count} 个")
        
        print(f"\n📊 特征对比:")
        matched_avg = comparison['matched_features']['avg_amount']
        unmatched_avg = comparison['unmatched_features']['avg_amount']
        print(f"  匹配数据平均金额: {matched_avg:.2f} USDT")
        print(f"  未匹配数据平均金额: {unmatched_avg:.2f} USDT")
        
        print(f"\n🗑️ 排除的平台记录: {summary['excluded_platform_records']} 条")
        print(f"   (这些是平台检测到的其他数据源，不在测试范围内)")


def main():
    """主函数"""
    print("🚀 开始正确的对敲匹配分析...")
    print("="*60)
    
    analyzer = CorrectWashTradingAnalyzer()
    
    try:
        # 1. 加载数据
        analyzer.load_data()
        
        # 2. 寻找匹配
        results = analyzer.find_matches()
        
        # 3. 生成报告
        report = analyzer.generate_report(results)
        
        # 4. 保存结果
        analyzer.save_results(report)
        
        print("\n✅ 正确的对敲匹配分析完成！")
        
    except Exception as e:
        print(f"❌ 分析过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
