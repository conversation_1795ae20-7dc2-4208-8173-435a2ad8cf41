{"analysis_time": "2025-08-10T02:21:49.699977", "miss_analysis": {"total_miss_count": 142, "new_miss_count": 142, "old_miss_count": 0, "characteristics": {"amount": {"min": 10.14, "max": 98503.5, "avg": 12630.9125, "median": 1443.6750000000002, "std": 23421.07067204787}, "amount_diff": {"min": 0.05244077612347005, "max": 2.8178762111924067, "avg": 1.0715853219489742, "median": 0.9917473782414212, "std": 0.7240715993646593}, "symbol_distribution": {"ETH-USDT": 57, "DOGE-USDT": 39, "BTC-USDT": 46}, "amount_range_distribution": {"<100": 46, "100-1000": 16, "1000-10000": 46, ">10000": 34}}}, "comparison": {"miss_stats": {"count": 142, "avg_amount": 12630.9125, "avg_diff_pct": 1.0715853219489742, "min_amount": 10.14, "max_amount": 98503.5, "min_diff_pct": 0.05244077612347005, "max_diff_pct": 2.8178762111924067}, "matched_stats": {"count": 125, "avg_amount": 13821.608639999993, "avg_diff_pct": 1.0277988388978234, "min_amount": 10.07, "max_amount": 97857.5, "min_diff_pct": 0.0, "max_diff_pct": 2.614845189279135}, "differences": {"amount_ratio": 0.9138525644146734, "diff_pct_ratio": 1.042602191590434, "amount_gap": -1190.6961399999927, "diff_pct_gap": 0.04378648305115074}}, "boundaries": {"critical_thresholds": {"miss_min_amount": 10.14, "miss_max_diff_pct": 2.8178762111924067, "matched_min_amount": 10.07, "matched_max_diff_pct": 2.614845189279135, "amount_gap": 0.07000000000000028, "diff_pct_gap": 0.20303102191327183}, "overlap_analysis": {"amount_overlap_count": 141, "amount_overlap_pct": 99.29577464788733, "diff_overlap_count": 140, "diff_overlap_pct": 98.59154929577466}, "recommendations": ["存在金额检测空白区间: 10.07 - 10.14 USDT", "金额差异检测空白: 2.61% - 2.82%", "金额范围高度重叠(99.3%)，问题不在金额阈值", "差异范围高度重叠(98.6%)，问题不在差异阈值"]}, "recommendations": {"parameter_adjustments": {"amount_tolerance": {"current_issue": "金额容差不是主要问题", "recommendation": "保持当前设置或微调", "adjustment": "minimal"}, "diff_tolerance": {"current_issue": "差异容差设置合理", "recommendation": "检查其他因素", "adjustment": "investigate_other_factors"}, "scoring_weights": {"current_issue": "综合评分权重可能不平衡", "recommendation": "调整评分权重，降低过严的维度权重", "adjustment": "rebalance_weights"}}, "priority_actions": ["立即调整算法参数，漏检率过高", "重点检查非金额因素，如时间窗口、评分逻辑"], "expected_improvements": {"current_miss_count": 142, "expected_miss_reduction": 99.39999999999999, "target_miss_count": 42.60000000000001, "expected_detection_rate": "84.0%"}}}