#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
算法边界分析器：分析匹配成功和未匹配数据的各种差异指标
找出平台检测算法的边界条件
"""

import json
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Tuple
import statistics

class BoundaryAnalyzer:
    def __init__(self):
        self.matched_data = []
        self.unmatched_data = []
        
    def load_data(self):
        """加载匹配和未匹配的数据"""
        print("📊 加载数据...")
        
        # 加载71%匹配成功的数据
        with open('71%_matched_test_data.json', 'r', encoding='utf-8') as f:
            matched_file = json.load(f)
            self.matched_data = matched_file['records']
        
        # 加载29%未匹配的数据
        with open('29%_unmatched_test_data.json', 'r', encoding='utf-8') as f:
            unmatched_file = json.load(f)
            self.unmatched_data = unmatched_file['records']
        
        print(f"✅ 匹配成功数据: {len(self.matched_data)} 条")
        print(f"✅ 未匹配数据: {len(self.unmatched_data)} 条")
    
    def pair_records(self, records: List[Dict]) -> List[Dict]:
        """将记录按test_case_id配对"""
        pairs = {}
        
        for record in records:
            test_case_id = record.get('test_case_id', '')
            
            # 提取基础case_id
            if test_case_id.endswith('_long'):
                base_case_id = test_case_id[:-5]
                direction = 'long'
            elif test_case_id.endswith('_short'):
                base_case_id = test_case_id[:-6]
                direction = 'short'
            else:
                continue
            
            if base_case_id not in pairs:
                pairs[base_case_id] = {'long': None, 'short': None}
            
            pairs[base_case_id][direction] = record
        
        # 转换为配对列表
        paired_data = []
        for case_id, pair in pairs.items():
            if pair['long'] and pair['short']:
                paired_data.append({
                    'case_id': case_id,
                    'long': pair['long'],
                    'short': pair['short']
                })
        
        return paired_data
    
    def calculate_pair_metrics(self, pair: Dict) -> Dict:
        """计算配对的各种指标"""
        long_record = pair['long']
        short_record = pair['short']
        
        # 时间解析
        try:
            long_buy_time = datetime.fromisoformat(long_record['buy_time'])
            long_sell_time = datetime.fromisoformat(long_record['sell_time'])
            short_buy_time = datetime.fromisoformat(short_record['buy_time'])
            short_sell_time = datetime.fromisoformat(short_record['sell_time'])
        except:
            return None
        
        # 计算各种差异指标
        metrics = {
            'case_id': pair['case_id'],
            'coin': long_record['coin'],
            'test_type': long_record.get('test_type', 'unknown'),
            
            # 金额相关
            'long_amount': long_record['usdt_amount'],
            'short_amount': short_record['usdt_amount'],
            'amount_diff_abs': abs(long_record['usdt_amount'] - short_record['usdt_amount']),
            'amount_diff_pct': abs(long_record['usdt_amount'] - short_record['usdt_amount']) / max(long_record['usdt_amount'], short_record['usdt_amount']) * 100,
            'avg_amount': (long_record['usdt_amount'] + short_record['usdt_amount']) / 2,
            'max_amount': max(long_record['usdt_amount'], short_record['usdt_amount']),
            'min_amount': min(long_record['usdt_amount'], short_record['usdt_amount']),
            
            # 盈亏相关
            'long_pnl': long_record.get('profit_loss', 0),
            'short_pnl': short_record.get('profit_loss', 0),
            'total_pnl': long_record.get('profit_loss', 0) + short_record.get('profit_loss', 0),
            'pnl_diff_abs': abs(long_record.get('profit_loss', 0) - short_record.get('profit_loss', 0)),
            'net_pnl_abs': abs(long_record.get('profit_loss', 0) + short_record.get('profit_loss', 0)),
            
            # 时间相关
            'long_buy_time': long_buy_time,
            'short_buy_time': short_buy_time,
            'long_sell_time': long_sell_time,
            'short_sell_time': short_sell_time,
            
            # 开仓时间间隔
            'buy_time_gap_seconds': abs((long_buy_time - short_buy_time).total_seconds()),
            'sell_time_gap_seconds': abs((long_sell_time - short_sell_time).total_seconds()),
            
            # 持仓时间
            'long_holding_seconds': (long_sell_time - long_buy_time).total_seconds(),
            'short_holding_seconds': (short_sell_time - short_buy_time).total_seconds(),
            'holding_time_diff_seconds': abs((long_sell_time - long_buy_time).total_seconds() - (short_sell_time - short_buy_time).total_seconds()),
            
            # 整体时间跨度
            'earliest_time': min(long_buy_time, short_buy_time),
            'latest_time': max(long_sell_time, short_sell_time),
            'total_duration_seconds': (max(long_sell_time, short_sell_time) - min(long_buy_time, short_buy_time)).total_seconds(),
            
            # 交易成功率
            'long_success': long_record.get('trade_success', 0),
            'short_success': short_record.get('trade_success', 0),
            'both_success': long_record.get('trade_success', 0) and short_record.get('trade_success', 0)
        }
        
        return metrics
    
    def analyze_metrics(self, data: List[Dict], data_type: str) -> Dict:
        """分析指标统计"""
        print(f"🔍 分析{data_type}数据指标...")
        
        # 配对数据
        paired_data = self.pair_records(data)
        print(f"  配对数量: {len(paired_data)}")
        
        # 计算所有配对的指标
        all_metrics = []
        for pair in paired_data:
            metrics = self.calculate_pair_metrics(pair)
            if metrics:
                all_metrics.append(metrics)
        
        if not all_metrics:
            return {}
        
        # 统计分析
        def get_stats(values):
            if not values:
                return {'min': 0, 'max': 0, 'avg': 0, 'median': 0, 'std': 0}
            return {
                'min': min(values),
                'max': max(values),
                'avg': sum(values) / len(values),
                'median': statistics.median(values),
                'std': statistics.stdev(values) if len(values) > 1 else 0
            }
        
        analysis = {
            'total_pairs': len(all_metrics),
            'coin_distribution': {},
            'test_type_distribution': {},
            
            # 金额统计
            'amount_stats': {
                'avg_amount': get_stats([m['avg_amount'] for m in all_metrics]),
                'max_amount': get_stats([m['max_amount'] for m in all_metrics]),
                'min_amount': get_stats([m['min_amount'] for m in all_metrics]),
                'amount_diff_abs': get_stats([m['amount_diff_abs'] for m in all_metrics]),
                'amount_diff_pct': get_stats([m['amount_diff_pct'] for m in all_metrics])
            },
            
            # 盈亏统计
            'pnl_stats': {
                'total_pnl': get_stats([m['total_pnl'] for m in all_metrics]),
                'net_pnl_abs': get_stats([m['net_pnl_abs'] for m in all_metrics]),
                'pnl_diff_abs': get_stats([m['pnl_diff_abs'] for m in all_metrics])
            },
            
            # 时间统计
            'time_stats': {
                'buy_time_gap_seconds': get_stats([m['buy_time_gap_seconds'] for m in all_metrics]),
                'sell_time_gap_seconds': get_stats([m['sell_time_gap_seconds'] for m in all_metrics]),
                'holding_time_diff_seconds': get_stats([m['holding_time_diff_seconds'] for m in all_metrics]),
                'total_duration_seconds': get_stats([m['total_duration_seconds'] for m in all_metrics]),
                'long_holding_seconds': get_stats([m['long_holding_seconds'] for m in all_metrics]),
                'short_holding_seconds': get_stats([m['short_holding_seconds'] for m in all_metrics])
            },
            
            # 原始数据
            'raw_metrics': all_metrics
        }
        
        # 币种分布
        for metric in all_metrics:
            coin = metric['coin']
            analysis['coin_distribution'][coin] = analysis['coin_distribution'].get(coin, 0) + 1
        
        # 测试类型分布
        for metric in all_metrics:
            test_type = metric['test_type']
            analysis['test_type_distribution'][test_type] = analysis['test_type_distribution'].get(test_type, 0) + 1
        
        return analysis
    
    def find_boundary_conditions(self, matched_analysis: Dict, unmatched_analysis: Dict) -> Dict:
        """找出边界条件"""
        print("🎯 分析边界条件...")
        
        boundary = {
            'detection_thresholds': {},
            'critical_ranges': {},
            'overlap_analysis': {},
            'recommendations': []
        }
        
        # 金额边界分析
        matched_min_avg = matched_analysis['amount_stats']['avg_amount']['min']
        matched_max_avg = matched_analysis['amount_stats']['avg_amount']['max']
        unmatched_min_avg = unmatched_analysis['amount_stats']['avg_amount']['min']
        unmatched_max_avg = unmatched_analysis['amount_stats']['avg_amount']['max']
        
        boundary['detection_thresholds']['amount'] = {
            'matched_range': [matched_min_avg, matched_max_avg],
            'unmatched_range': [unmatched_min_avg, unmatched_max_avg],
            'overlap': [max(matched_min_avg, unmatched_min_avg), min(matched_max_avg, unmatched_max_avg)],
            'gap': matched_min_avg - unmatched_max_avg if matched_min_avg > unmatched_max_avg else 0
        }
        
        # 金额差异百分比边界
        matched_max_diff_pct = matched_analysis['amount_stats']['amount_diff_pct']['max']
        unmatched_min_diff_pct = unmatched_analysis['amount_stats']['amount_diff_pct']['min']
        
        boundary['detection_thresholds']['amount_diff_pct'] = {
            'matched_max': matched_max_diff_pct,
            'unmatched_min': unmatched_min_diff_pct,
            'boundary_gap': unmatched_min_diff_pct - matched_max_diff_pct
        }
        
        # 时间间隔边界
        matched_max_buy_gap = matched_analysis['time_stats']['buy_time_gap_seconds']['max']
        unmatched_min_buy_gap = unmatched_analysis['time_stats']['buy_time_gap_seconds']['min']
        
        boundary['detection_thresholds']['buy_time_gap'] = {
            'matched_max_seconds': matched_max_buy_gap,
            'unmatched_min_seconds': unmatched_min_buy_gap,
            'boundary_gap_seconds': unmatched_min_buy_gap - matched_max_buy_gap
        }
        
        # 盈亏边界
        matched_max_net_pnl = matched_analysis['pnl_stats']['net_pnl_abs']['max']
        unmatched_min_net_pnl = unmatched_analysis['pnl_stats']['net_pnl_abs']['min']
        
        boundary['detection_thresholds']['net_pnl'] = {
            'matched_max': matched_max_net_pnl,
            'unmatched_min': unmatched_min_net_pnl,
            'boundary_gap': unmatched_min_net_pnl - matched_max_net_pnl
        }
        
        # 生成建议
        if boundary['detection_thresholds']['amount']['gap'] > 0:
            boundary['recommendations'].append(f"存在金额检测空白区间: {unmatched_max_avg:.2f} - {matched_min_avg:.2f} USDT")
        
        if boundary['detection_thresholds']['amount_diff_pct']['boundary_gap'] > 0:
            boundary['recommendations'].append(f"金额差异百分比存在检测空白: {matched_max_diff_pct:.2f}% - {unmatched_min_diff_pct:.2f}%")
        
        if boundary['detection_thresholds']['buy_time_gap']['boundary_gap_seconds'] > 0:
            boundary['recommendations'].append(f"开仓时间间隔存在检测空白: {matched_max_buy_gap:.1f}s - {unmatched_min_buy_gap:.1f}s")
        
        return boundary
    
    def generate_boundary_report(self) -> Dict:
        """生成边界分析报告"""
        print("📊 生成边界分析报告...")
        
        # 分析匹配和未匹配数据
        matched_analysis = self.analyze_metrics(self.matched_data, "匹配成功")
        unmatched_analysis = self.analyze_metrics(self.unmatched_data, "未匹配")
        
        # 找出边界条件
        boundary_conditions = self.find_boundary_conditions(matched_analysis, unmatched_analysis)
        
        report = {
            'generation_time': datetime.now().isoformat(),
            'summary': {
                'matched_pairs': matched_analysis.get('total_pairs', 0),
                'unmatched_pairs': unmatched_analysis.get('total_pairs', 0)
            },
            'matched_analysis': matched_analysis,
            'unmatched_analysis': unmatched_analysis,
            'boundary_conditions': boundary_conditions,
            'detailed_comparison': self.create_detailed_comparison(matched_analysis, unmatched_analysis)
        }
        
        return report
    
    def create_detailed_comparison(self, matched: Dict, unmatched: Dict) -> Dict:
        """创建详细对比"""
        comparison = {}
        
        # 对比各项指标
        metrics_to_compare = [
            ('avg_amount', '平均金额'),
            ('amount_diff_pct', '金额差异百分比'),
            ('buy_time_gap_seconds', '开仓时间间隔'),
            ('total_duration_seconds', '总持续时间'),
            ('net_pnl_abs', '净盈亏绝对值')
        ]
        
        for metric_key, metric_name in metrics_to_compare:
            if metric_key in ['avg_amount', 'amount_diff_pct']:
                matched_stats = matched['amount_stats'][metric_key]
                unmatched_stats = unmatched['amount_stats'][metric_key]
            elif metric_key in ['net_pnl_abs']:
                matched_stats = matched['pnl_stats'][metric_key]
                unmatched_stats = unmatched['pnl_stats'][metric_key]
            else:
                matched_stats = matched['time_stats'][metric_key]
                unmatched_stats = unmatched['time_stats'][metric_key]
            
            comparison[metric_key] = {
                'name': metric_name,
                'matched': matched_stats,
                'unmatched': unmatched_stats,
                'difference': {
                    'min_diff': unmatched_stats['min'] - matched_stats['min'],
                    'max_diff': unmatched_stats['max'] - matched_stats['max'],
                    'avg_diff': unmatched_stats['avg'] - matched_stats['avg']
                }
            }
        
        return comparison

    def save_results(self, report: Dict):
        """保存分析结果"""
        print("💾 保存边界分析结果...")

        # 保存完整报告
        with open('algorithm_boundary_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=str)
        print("✅ 完整报告: algorithm_boundary_analysis.json")

        # 保存边界条件摘要
        boundary_summary = {
            'generation_time': report['generation_time'],
            'boundary_conditions': report['boundary_conditions'],
            'key_findings': self.extract_key_findings(report)
        }

        with open('boundary_conditions_summary.json', 'w', encoding='utf-8') as f:
            json.dump(boundary_summary, f, ensure_ascii=False, indent=2, default=str)
        print("✅ 边界条件摘要: boundary_conditions_summary.json")

    def extract_key_findings(self, report: Dict) -> Dict:
        """提取关键发现"""
        matched = report['matched_analysis']
        unmatched = report['unmatched_analysis']

        return {
            'critical_thresholds': {
                'amount_threshold': {
                    'matched_min': matched['amount_stats']['avg_amount']['min'],
                    'unmatched_max': unmatched['amount_stats']['avg_amount']['max'],
                    'gap': matched['amount_stats']['avg_amount']['min'] - unmatched['amount_stats']['avg_amount']['max']
                },
                'amount_diff_threshold': {
                    'matched_max_pct': matched['amount_stats']['amount_diff_pct']['max'],
                    'unmatched_min_pct': unmatched['amount_stats']['amount_diff_pct']['min']
                },
                'time_gap_threshold': {
                    'matched_max_seconds': matched['time_stats']['buy_time_gap_seconds']['max'],
                    'unmatched_min_seconds': unmatched['time_stats']['buy_time_gap_seconds']['min']
                }
            },
            'detection_gaps': report['boundary_conditions']['recommendations']
        }

    def print_boundary_summary(self, report: Dict):
        """打印边界分析摘要"""
        matched = report['matched_analysis']
        unmatched = report['unmatched_analysis']
        boundary = report['boundary_conditions']

        print("\n" + "="*80)
        print("🎯 算法边界分析摘要")
        print("="*80)

        print(f"📊 数据概览:")
        print(f"  匹配成功配对: {matched['total_pairs']} 个")
        print(f"  未匹配配对: {unmatched['total_pairs']} 个")

        print(f"\n💰 金额边界分析:")
        amount_threshold = boundary['detection_thresholds']['amount']
        print(f"  匹配成功金额范围: {amount_threshold['matched_range'][0]:.2f} - {amount_threshold['matched_range'][1]:.2f} USDT")
        print(f"  未匹配金额范围: {amount_threshold['unmatched_range'][0]:.2f} - {amount_threshold['unmatched_range'][1]:.2f} USDT")
        if amount_threshold['gap'] > 0:
            print(f"  ⚠️ 检测空白区间: {amount_threshold['unmatched_range'][1]:.2f} - {amount_threshold['matched_range'][0]:.2f} USDT")
        else:
            print(f"  ✅ 金额范围有重叠，无明显空白")

        print(f"\n📊 金额差异边界:")
        diff_threshold = boundary['detection_thresholds']['amount_diff_pct']
        print(f"  匹配成功最大差异: {diff_threshold['matched_max']:.2f}%")
        print(f"  未匹配最小差异: {diff_threshold['unmatched_min']:.2f}%")
        if diff_threshold['boundary_gap'] > 0:
            print(f"  ⚠️ 差异检测空白: {diff_threshold['matched_max']:.2f}% - {diff_threshold['unmatched_min']:.2f}%")

        print(f"\n⏰ 时间间隔边界:")
        time_threshold = boundary['detection_thresholds']['buy_time_gap']
        print(f"  匹配成功最大开仓间隔: {time_threshold['matched_max_seconds']:.1f} 秒")
        print(f"  未匹配最小开仓间隔: {time_threshold['unmatched_min_seconds']:.1f} 秒")
        if time_threshold['boundary_gap_seconds'] > 0:
            print(f"  ⚠️ 时间检测空白: {time_threshold['matched_max_seconds']:.1f}s - {time_threshold['unmatched_min_seconds']:.1f}s")

        print(f"\n💸 盈亏边界:")
        pnl_threshold = boundary['detection_thresholds']['net_pnl']
        print(f"  匹配成功最大净盈亏: {pnl_threshold['matched_max']:.2f} USDT")
        print(f"  未匹配最小净盈亏: {pnl_threshold['unmatched_min']:.2f} USDT")

        print(f"\n📈 关键统计对比:")
        print(f"  平均金额 - 匹配: {matched['amount_stats']['avg_amount']['avg']:.2f} vs 未匹配: {unmatched['amount_stats']['avg_amount']['avg']:.2f} USDT")
        print(f"  平均金额差异 - 匹配: {matched['amount_stats']['amount_diff_pct']['avg']:.2f}% vs 未匹配: {unmatched['amount_stats']['amount_diff_pct']['avg']:.2f}%")
        print(f"  平均开仓间隔 - 匹配: {matched['time_stats']['buy_time_gap_seconds']['avg']:.1f}s vs 未匹配: {unmatched['time_stats']['buy_time_gap_seconds']['avg']:.1f}s")

        print(f"\n🎯 算法边界建议:")
        if boundary['recommendations']:
            for i, rec in enumerate(boundary['recommendations'], 1):
                print(f"  {i}. {rec}")
        else:
            print("  ✅ 未发现明显的检测空白区间")

        print(f"\n🔍 极值分析:")
        print(f"  匹配成功中最难检测的案例:")
        print(f"    - 最小金额: {matched['amount_stats']['avg_amount']['min']:.2f} USDT")
        print(f"    - 最大金额差异: {matched['amount_stats']['amount_diff_pct']['max']:.2f}%")
        print(f"    - 最大时间间隔: {matched['time_stats']['buy_time_gap_seconds']['max']:.1f}s")

        print(f"  未匹配中最容易检测的案例:")
        print(f"    - 最大金额: {unmatched['amount_stats']['avg_amount']['max']:.2f} USDT")
        print(f"    - 最小金额差异: {unmatched['amount_stats']['amount_diff_pct']['min']:.2f}%")
        print(f"    - 最小时间间隔: {unmatched['time_stats']['buy_time_gap_seconds']['min']:.1f}s")


def main():
    """主函数"""
    print("🚀 开始算法边界分析...")
    print("="*80)

    analyzer = BoundaryAnalyzer()

    try:
        # 1. 加载数据
        analyzer.load_data()

        # 2. 生成边界分析报告
        report = analyzer.generate_boundary_report()

        # 3. 保存结果
        analyzer.save_results(report)

        # 4. 打印摘要
        analyzer.print_boundary_summary(report)

        print("\n✅ 算法边界分析完成！")

    except Exception as e:
        print(f"❌ 分析过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
