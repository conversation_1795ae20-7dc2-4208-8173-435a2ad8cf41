#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版对敲数据分析脚本
分析平台抓取数据与脚本生成测试数据的准确率
"""

import json
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Tuple, Any
import os

class SimpleWashTradingAnalyzer:
    def __init__(self):
        self.platform_data = None
        self.test_data = None
        self.analysis_results = {}
        
    def load_test_data(self, json_file: str) -> pd.DataFrame:
        """加载测试数据"""
        print(f"正在加载测试数据: {json_file}")
        
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        records = data.get('records', [])
        df = pd.DataFrame(records)
        
        if not df.empty:
            # 转换时间字段
            time_columns = ['buy_time', 'sell_time', 'start_time', 'end_time', 'created_at']
            for col in time_columns:
                if col in df.columns:
                    df[col] = pd.to_datetime(df[col])
        
        print(f"加载了 {len(df)} 条测试记录")
        return df
    
    def load_platform_data(self, json_file: str) -> pd.DataFrame:
        """加载平台数据"""
        print(f"正在加载平台数据: {json_file}")
        
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        df = pd.DataFrame(data)
        
        if not df.empty:
            # 转换时间字段
            time_columns = ['open_time', 'close_time', 'match_open_time', 'match_close_time', 'create_time']
            for col in time_columns:
                if col in df.columns:
                    df[col] = pd.to_datetime(df[col])
        
        print(f"加载了 {len(df)} 条平台记录")
        return df
    
    def calculate_amount_tolerance(self, amount_a: float, amount_b: float) -> float:
        """计算金额容差"""
        max_amount = max(amount_a, amount_b)
        
        if max_amount <= 100:
            return max_amount * 0.05  # 5%
        elif max_amount <= 1000:
            return max_amount * 0.03  # 3%
        elif max_amount <= 10000:
            return max_amount * 0.02  # 2%
        else:
            return max_amount * 0.01  # 1%
    
    def calculate_amount_match_score(self, amount_a: float, amount_b: float) -> float:
        """计算金额匹配分数"""
        amount_diff = abs(amount_a - amount_b)
        max_amount = max(amount_a, amount_b)
        
        if max_amount == 0:
            return 0.0
        
        tolerance = self.calculate_amount_tolerance(amount_a, amount_b)
        
        if amount_diff == 0:
            return 1.0
        elif amount_diff <= tolerance:
            # 容差内：0.80-1.0 的线性评分
            score = 1.0 - 0.2 * (amount_diff / tolerance)
        else:
            # 容差外：0-0.8 的指数衰减评分
            excess_ratio = (amount_diff - tolerance) / tolerance
            score = 0.80 * np.exp(-excess_ratio * 0.75)
        
        return max(0.0, min(1.0, score))
    
    def calculate_time_match_score(self, time_diff_seconds: float, time_window: float = 30.0) -> float:
        """计算时间匹配分数"""
        normalized_diff = time_diff_seconds / time_window
        
        if normalized_diff >= 1.0:
            return 0.0
        else:
            return np.exp(-2 * normalized_diff)  # 指数衰减
    
    def find_matches(self, time_window: float = 30.0) -> Dict[str, Any]:
        """查找匹配的交易对"""
        print("正在查找匹配的交易对...")
        
        matches = []
        potential_matches = []
        
        # 按币种分组分析
        test_coins = self.test_data['coin'].unique() if 'coin' in self.test_data.columns else []
        platform_symbols = self.platform_data['symbol'].unique() if 'symbol' in self.platform_data.columns else []
        
        # 币种映射
        coin_mapping = {
            'BTC': 'BTC-USDT',
            'ETH': 'ETH-USDT', 
            'DOG': 'DOGE-USDT'
        }
        
        for test_coin in test_coins:
            platform_symbol = coin_mapping.get(test_coin)
            if not platform_symbol or platform_symbol not in platform_symbols:
                continue
                
            print(f"分析 {test_coin} ({platform_symbol}) 的匹配...")
            
            # 获取该币种的数据
            test_coin_data = self.test_data[self.test_data['coin'] == test_coin]
            platform_coin_data = self.platform_data[self.platform_data['symbol'] == platform_symbol]
            
            # 只分析对敲嫌疑交易
            wash_test_data = test_coin_data[test_coin_data['test_type'] == 'wash_trading_suspect']
            
            print(f"  测试数据: {len(wash_test_data)} 条对敲嫌疑记录")
            print(f"  平台数据: {len(platform_coin_data)} 条记录")
            
            # 匹配分析
            for _, test_record in wash_test_data.iterrows():
                test_amount = test_record['usdt_amount']
                test_start_time = test_record['start_time']
                test_end_time = test_record['end_time']
                
                for _, platform_record in platform_coin_data.iterrows():
                    platform_amount = platform_record['open_amount']
                    platform_open_time = platform_record['open_time']
                    platform_close_time = platform_record['close_time']
                    
                    # 计算时间差
                    time_diff_open = abs((platform_open_time - test_start_time).total_seconds())
                    time_diff_close = abs((platform_close_time - test_end_time).total_seconds())
                    
                    # 使用较小的时间差作为主要匹配标准
                    primary_time_diff = min(time_diff_open, time_diff_close)
                    secondary_time_diff = max(time_diff_open, time_diff_close)
                    
                    # 计算金额匹配
                    amount_tolerance = self.calculate_amount_tolerance(test_amount, platform_amount)
                    amount_diff = abs(platform_amount - test_amount)
                    amount_match_score = self.calculate_amount_match_score(test_amount, platform_amount)
                    
                    # 时间匹配分数
                    primary_time_match_score = self.calculate_time_match_score(primary_time_diff, time_window)
                    secondary_time_match_score = self.calculate_time_match_score(secondary_time_diff, time_window)
                    
                    # 匹配条件
                    primary_time_match = primary_time_diff <= time_window
                    amount_match = amount_diff <= amount_tolerance
                    
                    # 综合评分
                    overall_score = (amount_match_score * 0.4 +
                                   primary_time_match_score * 0.4 +
                                   secondary_time_match_score * 0.2)
                    
                    match_info = {
                        'test_record_id': test_record['record_id'],
                        'platform_record_id': platform_record['id'],
                        'coin': test_coin,
                        'symbol': platform_symbol,
                        'test_amount': test_amount,
                        'platform_amount': platform_amount,
                        'amount_diff': amount_diff,
                        'amount_tolerance': amount_tolerance,
                        'amount_diff_percent': (amount_diff / max(test_amount, platform_amount)) * 100,
                        'time_diff_open_sec': time_diff_open,
                        'time_diff_close_sec': time_diff_close,
                        'primary_time_diff_sec': primary_time_diff,
                        'amount_match_score': amount_match_score,
                        'primary_time_match_score': primary_time_match_score,
                        'secondary_time_match_score': secondary_time_match_score,
                        'primary_time_match': primary_time_match,
                        'amount_match': amount_match,
                        'overall_score': overall_score,
                        'overall_match': primary_time_match and amount_match and overall_score >= 0.7,
                        'platform_score': platform_record.get('score', 0)
                    }
                    
                    # 潜在匹配：满足任一条件或综合分数较高
                    if primary_time_match or amount_match or overall_score >= 0.5:
                        potential_matches.append(match_info)
                    
                    # 精确匹配：满足标准
                    if match_info['overall_match']:
                        matches.append(match_info)
        
        return {
            'exact_matches': matches,
            'potential_matches': potential_matches,
            'match_count': len(matches),
            'potential_count': len(potential_matches),
            'test_total': len(self.test_data[self.test_data['test_type'] == 'wash_trading_suspect']),
            'platform_total': len(self.platform_data)
        }

    def analyze_accuracy(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """分析平台抓取准确率"""
        print("正在分析平台抓取准确率...")

        exact_matches = results['exact_matches']
        potential_matches = results['potential_matches']
        test_total = results['test_total']
        platform_total = results['platform_total']

        # 基础统计
        exact_accuracy = len(exact_matches) / test_total * 100 if test_total > 0 else 0
        potential_accuracy = len(potential_matches) / test_total * 100 if test_total > 0 else 0

        # 按币种分析
        coin_analysis = {}
        for match in exact_matches:
            coin = match['coin']
            if coin not in coin_analysis:
                coin_analysis[coin] = {
                    'exact_matches': 0,
                    'potential_matches': 0,
                    'test_records': 0,
                    'platform_records': 0
                }
            coin_analysis[coin]['exact_matches'] += 1

        for match in potential_matches:
            coin = match['coin']
            if coin not in coin_analysis:
                coin_analysis[coin] = {
                    'exact_matches': 0,
                    'potential_matches': 0,
                    'test_records': 0,
                    'platform_records': 0
                }
            coin_analysis[coin]['potential_matches'] += 1

        # 计算每个币种的测试记录数
        coin_mapping = {'BTC': 'BTC-USDT', 'ETH': 'ETH-USDT', 'DOG': 'DOGE-USDT'}
        for coin in coin_analysis.keys():
            test_coin_data = self.test_data[
                (self.test_data['coin'] == coin) &
                (self.test_data['test_type'] == 'wash_trading_suspect')
            ]
            coin_analysis[coin]['test_records'] = len(test_coin_data)

            platform_symbol = coin_mapping.get(coin)
            if platform_symbol:
                platform_coin_data = self.platform_data[self.platform_data['symbol'] == platform_symbol]
                coin_analysis[coin]['platform_records'] = len(platform_coin_data)

        # 匹配质量分析
        match_quality = {}
        if exact_matches:
            exact_df = pd.DataFrame(exact_matches)
            match_quality['exact'] = {
                'avg_amount_diff': exact_df['amount_diff'].mean(),
                'avg_amount_diff_percent': exact_df['amount_diff_percent'].mean(),
                'avg_time_diff': exact_df['primary_time_diff_sec'].mean(),
                'avg_overall_score': exact_df['overall_score'].mean(),
                'avg_platform_score': exact_df['platform_score'].mean()
            }

        if potential_matches:
            potential_df = pd.DataFrame(potential_matches)
            match_quality['potential'] = {
                'avg_amount_diff': potential_df['amount_diff'].mean(),
                'avg_amount_diff_percent': potential_df['amount_diff_percent'].mean(),
                'avg_time_diff': potential_df['primary_time_diff_sec'].mean(),
                'avg_overall_score': potential_df['overall_score'].mean(),
                'avg_platform_score': potential_df['platform_score'].mean()
            }

        return {
            'exact_accuracy_percent': exact_accuracy,
            'potential_accuracy_percent': potential_accuracy,
            'exact_match_count': len(exact_matches),
            'potential_match_count': len(potential_matches),
            'test_total': test_total,
            'platform_total': platform_total,
            'coin_analysis': coin_analysis,
            'match_quality': match_quality
        }

    def print_analysis_report(self, results: Dict[str, Any], accuracy: Dict[str, Any]):
        """打印分析报告"""
        print("\n" + "="*80)
        print("📊 对敲数据分析报告")
        print("="*80)

        print(f"\n📈 总体准确率:")
        print(f"  精确匹配准确率: {accuracy['exact_accuracy_percent']:.2f}% ({accuracy['exact_match_count']}/{accuracy['test_total']})")
        print(f"  潜在匹配准确率: {accuracy['potential_accuracy_percent']:.2f}% ({accuracy['potential_match_count']}/{accuracy['test_total']})")

        print(f"\n📊 数据概览:")
        print(f"  测试对敲嫌疑记录: {accuracy['test_total']} 条")
        print(f"  平台抓取记录: {accuracy['platform_total']} 条")
        print(f"  精确匹配: {accuracy['exact_match_count']} 条")
        print(f"  潜在匹配: {accuracy['potential_match_count']} 条")

        print(f"\n🪙 按币种分析:")
        for coin, data in accuracy['coin_analysis'].items():
            exact_rate = data['exact_matches'] / data['test_records'] * 100 if data['test_records'] > 0 else 0
            potential_rate = data['potential_matches'] / data['test_records'] * 100 if data['test_records'] > 0 else 0
            print(f"  {coin}:")
            print(f"    测试记录: {data['test_records']} 条")
            print(f"    平台记录: {data['platform_records']} 条")
            print(f"    精确匹配: {data['exact_matches']} 条 ({exact_rate:.1f}%)")
            print(f"    潜在匹配: {data['potential_matches']} 条 ({potential_rate:.1f}%)")

        print(f"\n📏 匹配质量分析:")
        if 'exact' in accuracy['match_quality']:
            exact_quality = accuracy['match_quality']['exact']
            print(f"  精确匹配:")
            print(f"    平均金额差异: {exact_quality['avg_amount_diff']:.2f} USDT ({exact_quality['avg_amount_diff_percent']:.2f}%)")
            print(f"    平均时间差异: {exact_quality['avg_time_diff']:.1f} 秒")
            print(f"    平均综合评分: {exact_quality['avg_overall_score']:.3f}")
            print(f"    平均平台评分: {exact_quality['avg_platform_score']:.2f}")

        if 'potential' in accuracy['match_quality']:
            potential_quality = accuracy['match_quality']['potential']
            print(f"  潜在匹配:")
            print(f"    平均金额差异: {potential_quality['avg_amount_diff']:.2f} USDT ({potential_quality['avg_amount_diff_percent']:.2f}%)")
            print(f"    平均时间差异: {potential_quality['avg_time_diff']:.1f} 秒")
            print(f"    平均综合评分: {potential_quality['avg_overall_score']:.3f}")
            print(f"    平均平台评分: {potential_quality['avg_platform_score']:.2f}")

    def save_results(self, results: Dict[str, Any], accuracy: Dict[str, Any], output_file: str):
        """保存分析结果"""
        output_data = {
            'analysis_timestamp': datetime.now().isoformat(),
            'summary': accuracy,
            'detailed_results': results,
            'exact_matches': results['exact_matches'],
            'potential_matches': results['potential_matches']
        }

        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2, default=str)

        print(f"\n💾 分析结果已保存到: {output_file}")

    def run_analysis(self, test_data_file: str, platform_data_file: str, output_file: str = None):
        """运行完整分析"""
        print("🚀 开始对敲数据分析...")

        # 加载数据
        self.test_data = self.load_test_data(test_data_file)
        self.platform_data = self.load_platform_data(platform_data_file)

        # 查找匹配
        results = self.find_matches()

        # 分析准确率
        accuracy = self.analyze_accuracy(results)

        # 打印报告
        self.print_analysis_report(results, accuracy)

        # 保存结果
        if output_file:
            self.save_results(results, accuracy, output_file)

        return results, accuracy


def main():
    """主函数"""
    # 文件路径
    test_data_file = "../../测试记录/测试跑的全部数据.json"
    platform_data_file = "../../测试记录/平台抓取对敲数据.json"

    # 检查文件是否存在
    if not os.path.exists(test_data_file):
        print(f"❌ 测试数据文件不存在: {test_data_file}")
        return

    if not os.path.exists(platform_data_file):
        print(f"❌ 平台数据文件不存在: {platform_data_file}")
        return

    # 创建分析器
    analyzer = SimpleWashTradingAnalyzer()

    try:
        # 加载数据
        analyzer.test_data = analyzer.load_test_data(test_data_file)
        analyzer.platform_data = analyzer.load_platform_data(platform_data_file)

        # 多种时间窗口分析
        time_windows = [30, 60, 120, 300, 600]  # 30秒到10分钟

        print("\n🔍 多时间窗口分析:")
        print("="*80)

        best_results = None
        best_accuracy = 0
        best_window = 30

        for window in time_windows:
            print(f"\n⏰ 时间窗口: {window} 秒")
            print("-" * 40)

            # 查找匹配
            results = analyzer.find_matches(time_window=window)
            accuracy = analyzer.analyze_accuracy(results)

            exact_rate = accuracy['exact_accuracy_percent']
            potential_rate = accuracy['potential_accuracy_percent']

            print(f"精确匹配率: {exact_rate:.2f}% ({accuracy['exact_match_count']}/{accuracy['test_total']})")
            print(f"潜在匹配率: {potential_rate:.2f}% ({accuracy['potential_match_count']}/{accuracy['test_total']})")

            if exact_rate > best_accuracy:
                best_accuracy = exact_rate
                best_results = results
                best_window = window

        print(f"\n🏆 最佳时间窗口: {best_window} 秒 (精确匹配率: {best_accuracy:.2f}%)")

        # 使用最佳时间窗口进行详细分析
        if best_results:
            print(f"\n📊 使用 {best_window} 秒时间窗口的详细分析:")
            print("="*80)

            final_accuracy = analyzer.analyze_accuracy(best_results)
            analyzer.print_analysis_report(best_results, final_accuracy)

            # 保存最佳结果
            output_file = f"analysis_results_best_{best_window}s.json"
            analyzer.save_results(best_results, final_accuracy, output_file)

        # 额外分析：时间分布
        print(f"\n📈 时间差分布分析:")
        print("-" * 40)

        # 重新运行30秒窗口分析以获取时间差分布
        results_30s = analyzer.find_matches(time_window=30)
        if results_30s['potential_matches']:
            potential_df = pd.DataFrame(results_30s['potential_matches'])

            time_ranges = [
                (0, 30, "0-30秒"),
                (30, 60, "30-60秒"),
                (60, 300, "1-5分钟"),
                (300, 1800, "5-30分钟"),
                (1800, float('inf'), "30分钟以上")
            ]

            for min_time, max_time, label in time_ranges:
                if max_time == float('inf'):
                    count = len(potential_df[potential_df['primary_time_diff_sec'] >= min_time])
                else:
                    count = len(potential_df[
                        (potential_df['primary_time_diff_sec'] >= min_time) &
                        (potential_df['primary_time_diff_sec'] < max_time)
                    ])
                percentage = count / len(potential_df) * 100 if len(potential_df) > 0 else 0
                print(f"  {label}: {count} 条 ({percentage:.1f}%)")

        print("\n✅ 多维度分析完成！")

    except Exception as e:
        print(f"❌ 分析过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
