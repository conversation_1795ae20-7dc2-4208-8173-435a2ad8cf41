# 检测失败案例摘要报告

## 📊 基本信息

- **失败案例总数**: 20 个配对
- **失败记录总数**: 40 条
- **数据来源**: 原始测试数据中的真正遗漏案例
- **生成时间**: 2025-08-07T09:37:53.733919

## 📋 失败案例列表

| Case ID | 币种 | 多头记录 | 空头记录 | 总记录 |
|---------|------|----------|----------|--------|
| 327 | DOG | 1 | 1 | 2 |
| 317 | ETH | 1 | 1 | 2 |
| 313 | BTC | 1 | 1 | 2 |
| 308 | ETH | 1 | 1 | 2 |
| 298 | BTC | 1 | 1 | 2 |
| 295 | BTC | 1 | 1 | 2 |
| 289 | BTC | 1 | 1 | 2 |
| 283 | BTC | 1 | 1 | 2 |
| 274 | BTC | 1 | 1 | 2 |
| 273 | DOG | 1 | 1 | 2 |
| 272 | ETH | 1 | 1 | 2 |
| 268 | BTC | 1 | 1 | 2 |
| 260 | ETH | 1 | 1 | 2 |
| 258 | DOG | 1 | 1 | 2 |
| 254 | ETH | 1 | 1 | 2 |
| 253 | BTC | 1 | 1 | 2 |
| 251 | ETH | 1 | 1 | 2 |
| 250 | BTC | 1 | 1 | 2 |
| 247 | BTC | 1 | 1 | 2 |
| 248 | ETH | 1 | 1 | 2 |


## 📈 统计分析

### 按币种分布
- **BTC**: 10 个配对
- **DOG**: 3 个配对
- **ETH**: 7 个配对


### 数据完整性检查
- **预期配对数**: 20
- **实际提取配对数**: 20
- **预期记录数**: 40 (每配对2条记录)
- **实际记录数**: 40

## 🎯 用途说明

这些失败案例可用于：

1. **算法调优验证**: 调整参数后用这些案例验证改进效果
2. **边界测试**: 作为边界条件的测试集
3. **回归测试**: 确保算法优化不会引入新问题
4. **特征分析**: 深入分析这些案例的共同特征

## 📁 文件说明

- **failed_detection_cases.json**: 完整的失败案例数据（原始测试格式）
- **failed_cases_summary.md**: 本摘要报告

---

**注意**: 这些案例都是经过边界分析确认的真正遗漏，不是合理排除的案例。
