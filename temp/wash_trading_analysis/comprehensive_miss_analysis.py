#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合漏检分析器：深入分析142个漏检案例的共同特征
对比之前的边界案例，调整算法参数
"""

import json
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
from typing import Dict, List, Any, Tuple
import statistics

class ComprehensiveMissAnalyzer:
    def __init__(self):
        self.new_miss_cases = []      # 142个新漏检案例
        self.old_miss_cases = []      # 之前的20个边界案例
        self.matched_cases = []       # 125个匹配成功案例
        
    def load_data(self):
        """加载所有相关数据"""
        print("📊 加载分析数据...")
        
        # 1. 加载142个新漏检案例
        try:
            with open('test_only_records.json', 'r', encoding='utf-8') as f:
                new_miss_data = json.load(f)
                self.new_miss_cases = new_miss_data['details']
            print(f"✅ 新漏检案例: {len(self.new_miss_cases)} 个")
        except FileNotFoundError:
            print("⚠️ 未找到新漏检案例文件")
        
        # 2. 加载之前的20个边界案例
        try:
            with open('failed_detection_cases.json', 'r', encoding='utf-8') as f:
                old_miss_data = json.load(f)
                # 转换为配对格式
                self.old_miss_cases = self.convert_old_cases_to_pairs(old_miss_data['records'])
            print(f"✅ 旧边界案例: {len(self.old_miss_cases)} 个")
        except FileNotFoundError:
            print("⚠️ 未找到旧边界案例文件")
        
        # 3. 加载125个匹配成功案例
        try:
            with open('wash_trading_match_analysis.json', 'r', encoding='utf-8') as f:
                match_data = json.load(f)
                self.matched_cases = self.extract_matched_pairs(match_data['matches'])
            print(f"✅ 匹配成功案例: {len(self.matched_cases)} 个")
        except FileNotFoundError:
            print("⚠️ 未找到匹配分析文件")
    
    def convert_old_cases_to_pairs(self, records: List[Dict]) -> List[Dict]:
        """将旧格式的记录转换为配对格式"""
        pairs = {}
        
        for record in records:
            test_case_id = record.get('test_case_id', '')
            if test_case_id.endswith('_long'):
                base_id = test_case_id[:-5]
                if base_id not in pairs:
                    pairs[base_id] = {'long': None, 'short': None}
                pairs[base_id]['long'] = record
            elif test_case_id.endswith('_short'):
                base_id = test_case_id[:-6]
                if base_id not in pairs:
                    pairs[base_id] = {'long': None, 'short': None}
                pairs[base_id]['short'] = record
        
        # 转换为统一格式
        converted_pairs = []
        for case_id, pair in pairs.items():
            if pair['long'] and pair['short']:
                long_amount = pair['long']['usdt_amount']
                short_amount = pair['short']['usdt_amount']
                amount_diff_pct = abs(long_amount - short_amount) / max(long_amount, short_amount) * 100
                
                converted_pairs.append({
                    'test_case_id': case_id,
                    'symbol': f"{pair['long']['coin']}-USDT",
                    'long_amount': long_amount,
                    'short_amount': short_amount,
                    'amount_diff_pct': amount_diff_pct,
                    'create_time': pair['long'].get('buy_time', ''),
                    'long_time': pair['long'].get('buy_time', ''),
                    'short_time': pair['short'].get('buy_time', ''),
                    'source': 'old_boundary'
                })
        
        return converted_pairs
    
    def extract_matched_pairs(self, matches: List[Dict]) -> List[Dict]:
        """从匹配数据中提取配对信息"""
        matched_pairs = []
        
        for match in matches:
            test_pair = match['test_pair']
            long_record = test_pair['long_record']
            short_record = test_pair['short_record']
            
            matched_pairs.append({
                'test_case_id': test_pair['test_case_id'],
                'symbol': long_record['symbol'],
                'long_amount': long_record['open_amount'],
                'short_amount': short_record['open_amount'],
                'amount_diff_pct': abs(long_record['open_amount'] - short_record['open_amount']) / max(long_record['open_amount'], short_record['open_amount']) * 100,
                'create_time': long_record['create_time'],
                'long_time': long_record['open_time'],
                'short_time': short_record['open_time'],
                'source': 'matched'
            })
        
        return matched_pairs
    
    def analyze_miss_characteristics(self) -> Dict:
        """分析漏检案例的共同特征"""
        print("🔍 分析漏检案例特征...")
        
        all_miss_cases = self.new_miss_cases + self.old_miss_cases
        
        # 标记数据源
        for case in self.new_miss_cases:
            case['source'] = 'new_miss'
        for case in self.old_miss_cases:
            if 'source' not in case:
                case['source'] = 'old_boundary'
        
        analysis = {
            'total_miss_count': len(all_miss_cases),
            'new_miss_count': len(self.new_miss_cases),
            'old_miss_count': len(self.old_miss_cases),
            'characteristics': {}
        }
        
        if not all_miss_cases:
            return analysis
        
        # 1. 金额特征分析
        amounts = []
        amount_diffs = []
        for case in all_miss_cases:
            avg_amount = (case['long_amount'] + case['short_amount']) / 2
            amounts.append(avg_amount)
            amount_diffs.append(case['amount_diff_pct'])
        
        analysis['characteristics']['amount'] = {
            'min': min(amounts),
            'max': max(amounts),
            'avg': sum(amounts) / len(amounts),
            'median': statistics.median(amounts),
            'std': statistics.stdev(amounts) if len(amounts) > 1 else 0
        }
        
        analysis['characteristics']['amount_diff'] = {
            'min': min(amount_diffs),
            'max': max(amount_diffs),
            'avg': sum(amount_diffs) / len(amount_diffs),
            'median': statistics.median(amount_diffs),
            'std': statistics.stdev(amount_diffs) if len(amount_diffs) > 1 else 0
        }
        
        # 2. 币种分布
        symbol_stats = {}
        for case in all_miss_cases:
            symbol = case['symbol']
            symbol_stats[symbol] = symbol_stats.get(symbol, 0) + 1
        
        analysis['characteristics']['symbol_distribution'] = symbol_stats
        
        # 3. 金额范围分布
        amount_ranges = {'<100': 0, '100-1000': 0, '1000-10000': 0, '>10000': 0}
        for amount in amounts:
            if amount < 100:
                amount_ranges['<100'] += 1
            elif amount < 1000:
                amount_ranges['100-1000'] += 1
            elif amount < 10000:
                amount_ranges['1000-10000'] += 1
            else:
                amount_ranges['>10000'] += 1
        
        analysis['characteristics']['amount_range_distribution'] = amount_ranges
        
        return analysis
    
    def compare_with_matched_cases(self) -> Dict:
        """对比漏检案例和匹配成功案例"""
        print("📊 对比漏检案例与匹配成功案例...")
        
        all_miss_cases = self.new_miss_cases + self.old_miss_cases
        
        comparison = {
            'miss_stats': {},
            'matched_stats': {},
            'differences': {}
        }
        
        # 分析漏检案例
        if all_miss_cases:
            miss_amounts = [(case['long_amount'] + case['short_amount']) / 2 for case in all_miss_cases]
            miss_diffs = [case['amount_diff_pct'] for case in all_miss_cases]
            
            comparison['miss_stats'] = {
                'count': len(all_miss_cases),
                'avg_amount': sum(miss_amounts) / len(miss_amounts),
                'avg_diff_pct': sum(miss_diffs) / len(miss_diffs),
                'min_amount': min(miss_amounts),
                'max_amount': max(miss_amounts),
                'min_diff_pct': min(miss_diffs),
                'max_diff_pct': max(miss_diffs)
            }
        
        # 分析匹配成功案例
        if self.matched_cases:
            matched_amounts = [(case['long_amount'] + case['short_amount']) / 2 for case in self.matched_cases]
            matched_diffs = [case['amount_diff_pct'] for case in self.matched_cases]
            
            comparison['matched_stats'] = {
                'count': len(self.matched_cases),
                'avg_amount': sum(matched_amounts) / len(matched_amounts),
                'avg_diff_pct': sum(matched_diffs) / len(matched_diffs),
                'min_amount': min(matched_amounts),
                'max_amount': max(matched_amounts),
                'min_diff_pct': min(matched_diffs),
                'max_diff_pct': max(matched_diffs)
            }
        
        # 计算差异
        if comparison['miss_stats'] and comparison['matched_stats']:
            miss_stats = comparison['miss_stats']
            matched_stats = comparison['matched_stats']
            
            comparison['differences'] = {
                'amount_ratio': miss_stats['avg_amount'] / matched_stats['avg_amount'],
                'diff_pct_ratio': miss_stats['avg_diff_pct'] / matched_stats['avg_diff_pct'],
                'amount_gap': miss_stats['avg_amount'] - matched_stats['avg_amount'],
                'diff_pct_gap': miss_stats['avg_diff_pct'] - matched_stats['avg_diff_pct']
            }
        
        return comparison
    
    def find_detection_boundaries(self) -> Dict:
        """找出检测边界条件"""
        print("🎯 寻找检测边界条件...")
        
        all_miss_cases = self.new_miss_cases + self.old_miss_cases
        
        boundaries = {
            'critical_thresholds': {},
            'overlap_analysis': {},
            'recommendations': []
        }
        
        if not all_miss_cases or not self.matched_cases:
            return boundaries
        
        # 1. 找出最容易检测的漏检案例（应该被检测但没被检测）
        miss_amounts = [(case['long_amount'] + case['short_amount']) / 2 for case in all_miss_cases]
        miss_diffs = [case['amount_diff_pct'] for case in all_miss_cases]
        
        matched_amounts = [(case['long_amount'] + case['short_amount']) / 2 for case in self.matched_cases]
        matched_diffs = [case['amount_diff_pct'] for case in self.matched_cases]
        
        # 2. 边界分析
        boundaries['critical_thresholds'] = {
            'miss_min_amount': min(miss_amounts),
            'miss_max_diff_pct': max(miss_diffs),
            'matched_min_amount': min(matched_amounts),
            'matched_max_diff_pct': max(matched_diffs),
            'amount_gap': min(miss_amounts) - min(matched_amounts),
            'diff_pct_gap': max(miss_diffs) - max(matched_diffs)
        }
        
        # 3. 重叠分析
        amount_overlap = len([a for a in miss_amounts if min(matched_amounts) <= a <= max(matched_amounts)])
        diff_overlap = len([d for d in miss_diffs if min(matched_diffs) <= d <= max(matched_diffs)])
        
        boundaries['overlap_analysis'] = {
            'amount_overlap_count': amount_overlap,
            'amount_overlap_pct': amount_overlap / len(miss_amounts) * 100,
            'diff_overlap_count': diff_overlap,
            'diff_overlap_pct': diff_overlap / len(miss_diffs) * 100
        }
        
        # 4. 生成建议
        if boundaries['critical_thresholds']['amount_gap'] > 0:
            boundaries['recommendations'].append(f"存在金额检测空白区间: {min(matched_amounts):.2f} - {min(miss_amounts):.2f} USDT")
        
        if boundaries['critical_thresholds']['diff_pct_gap'] > 0:
            boundaries['recommendations'].append(f"金额差异检测空白: {max(matched_diffs):.2f}% - {max(miss_diffs):.2f}%")
        
        if boundaries['overlap_analysis']['amount_overlap_pct'] > 50:
            boundaries['recommendations'].append(f"金额范围高度重叠({boundaries['overlap_analysis']['amount_overlap_pct']:.1f}%)，问题不在金额阈值")
        
        if boundaries['overlap_analysis']['diff_overlap_pct'] > 50:
            boundaries['recommendations'].append(f"差异范围高度重叠({boundaries['overlap_analysis']['diff_overlap_pct']:.1f}%)，问题不在差异阈值")
        
        return boundaries
    
    def generate_algorithm_recommendations(self, miss_analysis: Dict, comparison: Dict, boundaries: Dict) -> Dict:
        """基于分析结果生成算法调整建议"""
        print("🔧 生成算法调整建议...")
        
        recommendations = {
            'parameter_adjustments': {},
            'priority_actions': [],
            'expected_improvements': {}
        }
        
        # 基于边界分析的参数调整
        if boundaries.get('overlap_analysis', {}).get('amount_overlap_pct', 0) > 70:
            # 金额范围高度重叠，不是阈值问题
            recommendations['parameter_adjustments']['amount_tolerance'] = {
                'current_issue': '金额容差不是主要问题',
                'recommendation': '保持当前设置或微调',
                'adjustment': 'minimal'
            }
        else:
            # 需要调整金额容差
            miss_stats = comparison.get('miss_stats', {})
            if miss_stats.get('avg_amount', 0) < comparison.get('matched_stats', {}).get('avg_amount', 0):
                recommendations['parameter_adjustments']['amount_tolerance'] = {
                    'current_issue': '小额交易容差过严',
                    'recommendation': '放宽小额交易容差',
                    'adjustment': 'increase_small_amount_tolerance'
                }
        
        # 差异百分比调整
        if boundaries.get('overlap_analysis', {}).get('diff_overlap_pct', 0) > 70:
            recommendations['parameter_adjustments']['diff_tolerance'] = {
                'current_issue': '差异容差设置合理',
                'recommendation': '检查其他因素',
                'adjustment': 'investigate_other_factors'
            }
        else:
            miss_avg_diff = comparison.get('miss_stats', {}).get('avg_diff_pct', 0)
            matched_avg_diff = comparison.get('matched_stats', {}).get('avg_diff_pct', 0)
            
            if miss_avg_diff < matched_avg_diff:
                recommendations['parameter_adjustments']['diff_tolerance'] = {
                    'current_issue': '差异容差过严，高质量配对被排除',
                    'recommendation': '适度放宽差异容差',
                    'adjustment': 'increase_diff_tolerance'
                }
        
        # 权重调整建议
        total_miss = miss_analysis.get('total_miss_count', 0)
        if total_miss > 100:  # 漏检率过高
            recommendations['parameter_adjustments']['scoring_weights'] = {
                'current_issue': '综合评分权重可能不平衡',
                'recommendation': '调整评分权重，降低过严的维度权重',
                'adjustment': 'rebalance_weights'
            }
        
        # 优先级行动
        if total_miss > 100:
            recommendations['priority_actions'].append('立即调整算法参数，漏检率过高')
        
        if boundaries.get('overlap_analysis', {}).get('amount_overlap_pct', 0) > 80:
            recommendations['priority_actions'].append('重点检查非金额因素，如时间窗口、评分逻辑')
        
        # 预期改进效果
        if total_miss > 0:
            potential_improvement = min(total_miss * 0.7, total_miss - 20)  # 预期改进70%或减少到20个以内
            recommendations['expected_improvements'] = {
                'current_miss_count': total_miss,
                'expected_miss_reduction': potential_improvement,
                'target_miss_count': total_miss - potential_improvement,
                'expected_detection_rate': f"{((267 - (total_miss - potential_improvement)) / 267 * 100):.1f}%"
            }
        
        return recommendations

    def create_comparison_visualizations(self, miss_analysis: Dict, comparison: Dict):
        """创建对比可视化图表"""
        print("📈 生成对比可视化图表...")

        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'DejaVu Sans', 'SimHei']
        plt.rcParams['axes.unicode_minus'] = False

        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('漏检案例综合分析', fontsize=16, fontweight='bold')

        # 1. 漏检案例数量对比
        ax1.set_title('漏检案例数量对比', fontsize=14, fontweight='bold')

        categories = ['新漏检\n(142个)', '旧边界\n(20个)', '匹配成功\n(125个)']
        counts = [miss_analysis['new_miss_count'], miss_analysis['old_miss_count'], len(self.matched_cases)]
        colors = ['#DC143C', '#FF6347', '#2E8B57']

        bars = ax1.bar(categories, counts, color=colors, alpha=0.8)
        ax1.set_ylabel('案例数量')

        # 添加数值标签
        for bar, count in zip(bars, counts):
            ax1.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 2,
                    f'{count}', ha='center', va='bottom', fontweight='bold')

        # 2. 金额分布对比
        ax2.set_title('平均金额分布对比', fontsize=14, fontweight='bold')

        if comparison.get('miss_stats') and comparison.get('matched_stats'):
            miss_stats = comparison['miss_stats']
            matched_stats = comparison['matched_stats']

            categories = ['漏检案例', '匹配成功']
            amounts = [miss_stats['avg_amount'], matched_stats['avg_amount']]
            colors = ['#DC143C', '#2E8B57']

            bars = ax2.bar(categories, amounts, color=colors, alpha=0.8)
            ax2.set_ylabel('平均金额 (USDT)')

            for bar, amount in zip(bars, amounts):
                ax2.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 100,
                        f'{amount:.0f}', ha='center', va='bottom', fontweight='bold')

        # 3. 金额差异对比
        ax3.set_title('平均金额差异对比', fontsize=14, fontweight='bold')

        if comparison.get('miss_stats') and comparison.get('matched_stats'):
            categories = ['漏检案例', '匹配成功']
            diffs = [miss_stats['avg_diff_pct'], matched_stats['avg_diff_pct']]
            colors = ['#DC143C', '#2E8B57']

            bars = ax3.bar(categories, diffs, color=colors, alpha=0.8)
            ax3.set_ylabel('平均金额差异 (%)')

            for bar, diff in zip(bars, diffs):
                ax3.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 0.01,
                        f'{diff:.2f}%', ha='center', va='bottom', fontweight='bold')

        # 4. 币种分布
        ax4.set_title('漏检案例币种分布', fontsize=14, fontweight='bold')

        symbol_dist = miss_analysis['characteristics'].get('symbol_distribution', {})
        if symbol_dist:
            symbols = list(symbol_dist.keys())
            counts = list(symbol_dist.values())
            colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']

            wedges, texts, autotexts = ax4.pie(counts, labels=symbols, colors=colors,
                                              autopct='%1.1f%%', startangle=90)

            for autotext in autotexts:
                autotext.set_color('white')
                autotext.set_fontweight('bold')

        plt.tight_layout()
        plt.savefig('comprehensive_miss_analysis.png', dpi=300, bbox_inches='tight')
        plt.savefig('comprehensive_miss_analysis.pdf', bbox_inches='tight')
        print("✅ 可视化图表已保存: comprehensive_miss_analysis.png/pdf")

    def generate_comprehensive_report(self, miss_analysis: Dict, comparison: Dict,
                                    boundaries: Dict, recommendations: Dict) -> str:
        """生成综合分析报告"""

        report = f"""# 对敲检测漏检案例综合分析报告

## 📊 执行摘要

**分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

### 核心发现
- **总漏检案例**: {miss_analysis['total_miss_count']} 个
- **新发现漏检**: {miss_analysis['new_miss_count']} 个 (来自最新数据)
- **历史边界案例**: {miss_analysis['old_miss_count']} 个 (之前分析的边界案例)
- **匹配成功**: {len(self.matched_cases)} 个

### 关键问题
- **漏检率过高**: 新数据显示53.18%的漏检率
- **问题持续存在**: 历史边界问题未得到解决
- **检测能力不足**: 大量高质量对敲交易被漏检

---

## 🔍 漏检特征深度分析

### 金额特征
"""

        if miss_analysis['characteristics'].get('amount'):
            amount_stats = miss_analysis['characteristics']['amount']
            report += f"""
| 指标 | 数值 |
|------|------|
| 最小金额 | {amount_stats['min']:.2f} USDT |
| 最大金额 | {amount_stats['max']:.2f} USDT |
| 平均金额 | {amount_stats['avg']:.2f} USDT |
| 中位数金额 | {amount_stats['median']:.2f} USDT |
| 标准差 | {amount_stats['std']:.2f} USDT |
"""

        if miss_analysis['characteristics'].get('amount_diff'):
            diff_stats = miss_analysis['characteristics']['amount_diff']
            report += f"""
### 金额差异特征
| 指标 | 数值 |
|------|------|
| 最小差异 | {diff_stats['min']:.2f}% |
| 最大差异 | {diff_stats['max']:.2f}% |
| 平均差异 | {diff_stats['avg']:.2f}% |
| 中位数差异 | {diff_stats['median']:.2f}% |
| 标准差 | {diff_stats['std']:.2f}% |
"""

        # 币种和金额范围分布
        symbol_dist = miss_analysis['characteristics'].get('symbol_distribution', {})
        amount_range_dist = miss_analysis['characteristics'].get('amount_range_distribution', {})

        report += f"""
### 分布特征

#### 币种分布
"""
        for symbol, count in symbol_dist.items():
            pct = count / miss_analysis['total_miss_count'] * 100
            report += f"- **{symbol}**: {count} 个 ({pct:.1f}%)\n"

        report += f"""
#### 金额范围分布
"""
        for range_name, count in amount_range_dist.items():
            pct = count / miss_analysis['total_miss_count'] * 100
            report += f"- **{range_name} USDT**: {count} 个 ({pct:.1f}%)\n"

        # 对比分析
        if comparison.get('miss_stats') and comparison.get('matched_stats'):
            miss_stats = comparison['miss_stats']
            matched_stats = comparison['matched_stats']
            differences = comparison.get('differences', {})

            report += f"""

---

## 📈 漏检 vs 匹配成功对比

### 关键指标对比
| 指标 | 漏检案例 | 匹配成功 | 差异 |
|------|----------|----------|------|
| 平均金额 | {miss_stats['avg_amount']:.2f} USDT | {matched_stats['avg_amount']:.2f} USDT | {differences.get('amount_ratio', 0):.2f}x |
| 平均差异 | {miss_stats['avg_diff_pct']:.2f}% | {matched_stats['avg_diff_pct']:.2f}% | {differences.get('diff_pct_ratio', 0):.2f}x |
| 金额范围 | {miss_stats['min_amount']:.0f}-{miss_stats['max_amount']:.0f} | {matched_stats['min_amount']:.0f}-{matched_stats['max_amount']:.0f} | - |
| 差异范围 | {miss_stats['min_diff_pct']:.2f}%-{miss_stats['max_diff_pct']:.2f}% | {matched_stats['min_diff_pct']:.2f}%-{matched_stats['max_diff_pct']:.2f}% | - |

### 关键发现
"""

            if differences.get('amount_ratio', 0) < 1:
                report += "- ⚠️ **漏检案例平均金额更小**：小额交易检测能力不足\n"

            if differences.get('diff_pct_ratio', 0) < 1:
                report += "- ⚠️ **漏检案例金额差异更小**：高质量配对反而被漏检\n"

        # 边界分析
        if boundaries.get('critical_thresholds'):
            thresholds = boundaries['critical_thresholds']
            overlap = boundaries.get('overlap_analysis', {})

            report += f"""

---

## 🎯 检测边界分析

### 临界阈值
| 边界类型 | 漏检案例 | 匹配成功 | 边界间隙 |
|----------|----------|----------|----------|
| 最小金额 | {thresholds['miss_min_amount']:.2f} USDT | {thresholds['matched_min_amount']:.2f} USDT | {thresholds['amount_gap']:.2f} USDT |
| 最大差异 | {thresholds['miss_max_diff_pct']:.2f}% | {thresholds['matched_max_diff_pct']:.2f}% | {thresholds['diff_pct_gap']:.2f}% |

### 重叠分析
- **金额范围重叠**: {overlap.get('amount_overlap_pct', 0):.1f}% ({overlap.get('amount_overlap_count', 0)} 个案例)
- **差异范围重叠**: {overlap.get('diff_overlap_pct', 0):.1f}% ({overlap.get('diff_overlap_count', 0)} 个案例)

### 边界建议
"""

            for rec in boundaries.get('recommendations', []):
                report += f"- {rec}\n"

        # 算法调整建议
        if recommendations:
            report += f"""

---

## 🔧 算法优化建议

### 参数调整建议
"""

            param_adjustments = recommendations.get('parameter_adjustments', {})
            for param, details in param_adjustments.items():
                report += f"""
#### {param.replace('_', ' ').title()}
- **当前问题**: {details.get('current_issue', 'N/A')}
- **建议**: {details.get('recommendation', 'N/A')}
- **调整方向**: {details.get('adjustment', 'N/A')}
"""

            report += f"""
### 优先级行动
"""
            for action in recommendations.get('priority_actions', []):
                report += f"1. {action}\n"

            expected = recommendations.get('expected_improvements', {})
            if expected:
                report += f"""
### 预期改进效果
- **当前漏检**: {expected['current_miss_count']} 个
- **预期减少**: {expected['expected_miss_reduction']:.0f} 个
- **目标漏检**: {expected['target_miss_count']:.0f} 个
- **预期检测率**: {expected['expected_detection_rate']}
"""

        report += f"""

---

## 📋 结论与下一步

### 核心结论
1. **漏检问题严重**: 总计{miss_analysis['total_miss_count']}个漏检案例，需要立即优化
2. **问题根源复杂**: 不仅是阈值问题，可能涉及算法逻辑
3. **优化潜力巨大**: 通过参数调整预期可显著提升检测率

### 立即行动
1. **🚨 紧急**: 实施参数调整建议
2. **📊 验证**: 用漏检案例验证优化效果
3. **🔄 迭代**: 持续监控和优化

### 长期规划
1. **算法升级**: 考虑引入更先进的检测算法
2. **动态调整**: 建立自适应参数调整机制
3. **持续监控**: 建立漏检率监控体系

---

**报告生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**分析数据**: 新漏检{miss_analysis['new_miss_count']}个 + 历史边界{miss_analysis['old_miss_count']}个 + 匹配成功{len(self.matched_cases)}个
"""

        return report

    def save_results(self, miss_analysis: Dict, comparison: Dict, boundaries: Dict, recommendations: Dict):
        """保存分析结果"""
        print("💾 保存分析结果...")

        # 保存完整分析数据
        complete_analysis = {
            'analysis_time': datetime.now().isoformat(),
            'miss_analysis': miss_analysis,
            'comparison': comparison,
            'boundaries': boundaries,
            'recommendations': recommendations
        }

        with open('comprehensive_miss_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(complete_analysis, f, ensure_ascii=False, indent=2, default=str)

        # 生成并保存报告
        report = self.generate_comprehensive_report(miss_analysis, comparison, boundaries, recommendations)
        with open('comprehensive_miss_analysis_report.md', 'w', encoding='utf-8') as f:
            f.write(report)

        print("✅ 完整分析数据: comprehensive_miss_analysis.json")
        print("✅ 分析报告: comprehensive_miss_analysis_report.md")


def main():
    """主函数"""
    print("🚀 开始综合漏检分析...")
    print("="*80)

    analyzer = ComprehensiveMissAnalyzer()

    try:
        # 1. 加载数据
        analyzer.load_data()

        # 2. 分析漏检特征
        miss_analysis = analyzer.analyze_miss_characteristics()

        # 3. 对比匹配成功案例
        comparison = analyzer.compare_with_matched_cases()

        # 4. 寻找检测边界
        boundaries = analyzer.find_detection_boundaries()

        # 5. 生成算法建议
        recommendations = analyzer.generate_algorithm_recommendations(miss_analysis, comparison, boundaries)

        # 6. 创建可视化
        analyzer.create_comparison_visualizations(miss_analysis, comparison)

        # 7. 保存结果
        analyzer.save_results(miss_analysis, comparison, boundaries, recommendations)

        print("\n✅ 综合漏检分析完成！")
        print("\n📁 生成的文件:")
        print("  📊 comprehensive_miss_analysis.json - 完整分析数据")
        print("  📝 comprehensive_miss_analysis_report.md - 分析报告")
        print("  📈 comprehensive_miss_analysis.png/pdf - 可视化图表")

        # 打印关键发现
        print(f"\n🎯 关键发现:")
        print(f"  总漏检案例: {miss_analysis['total_miss_count']} 个")
        print(f"  新发现漏检: {miss_analysis['new_miss_count']} 个")
        print(f"  历史边界案例: {miss_analysis['old_miss_count']} 个")

        if recommendations.get('expected_improvements'):
            expected = recommendations['expected_improvements']
            print(f"  预期改进: 漏检从{expected['current_miss_count']}个减少到{expected['target_miss_count']:.0f}个")
            print(f"  目标检测率: {expected['expected_detection_rate']}")

    except Exception as e:
        print(f"❌ 分析过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
