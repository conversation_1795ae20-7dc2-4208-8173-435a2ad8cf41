#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据处理脚本：将测试数据按test_case_id配对，转换为平台格式，并去重
"""

import json
import pandas as pd
from datetime import datetime
from typing import Dict, List, Any
import hashlib

class DataProcessor:
    def __init__(self):
        self.test_data = None
        self.platform_data = None
        
    def load_test_data(self, file_path: str) -> List[Dict]:
        """加载测试数据"""
        print(f"📊 加载测试数据: {file_path}")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        records = data.get('records', [])
        print(f"✅ 加载了 {len(records)} 条测试记录")
        return records
    
    def load_platform_data(self, file_path: str) -> List[Dict]:
        """加载平台数据"""
        print(f"📊 加载平台数据: {file_path}")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"✅ 加载了 {len(data)} 条平台记录")
        return data
    
    def pair_test_data_by_case_id(self, records: List[Dict]) -> List[Dict]:
        """按test_case_id配对测试数据"""
        print("🔄 按test_case_id配对测试数据...")

        # 提取基础case_id（去掉_long/_short后缀）
        case_groups = {}
        for record in records:
            test_case_id = record.get('test_case_id')
            if not test_case_id:
                continue

            # 提取基础case_id（去掉_long/_short）
            if test_case_id.endswith('_long'):
                base_case_id = test_case_id[:-5]  # 去掉'_long'
                direction = 'long'
            elif test_case_id.endswith('_short'):
                base_case_id = test_case_id[:-6]  # 去掉'_short'
                direction = 'short'
            else:
                print(f"⚠️ 无法识别方向: {test_case_id}")
                continue

            if base_case_id not in case_groups:
                case_groups[base_case_id] = {'long': [], 'short': []}

            case_groups[base_case_id][direction].append(record)

        paired_records = []

        for base_case_id, directions in case_groups.items():
            long_records = directions['long']
            short_records = directions['short']

            if len(long_records) != 1 or len(short_records) != 1:
                print(f"⚠️ case_id {base_case_id} 配对不完整: long={len(long_records)}, short={len(short_records)}")
                continue

            long_record = long_records[0]
            short_record = short_records[0]

            # 验证是否为同一币种
            if long_record['coin'] != short_record['coin']:
                print(f"⚠️ case_id {base_case_id} 币种不匹配: {long_record['coin']} vs {short_record['coin']}")
                continue

            paired_records.append({
                'test_case_id': base_case_id,
                'long_record': long_record,
                'short_record': short_record
            })
            print(f"✅ 配对成功: {base_case_id} - {long_record['coin']} (Long: {long_record['usdt_amount']}, Short: {short_record['usdt_amount']})")

        print(f"📊 成功配对 {len(paired_records)} 个test_case")
        return paired_records
    
    def convert_to_platform_format(self, paired_records: List[Dict]) -> List[Dict]:
        """将配对的测试数据转换为平台格式"""
        print("🔄 转换为平台格式...")
        
        converted_records = []
        
        for pair in paired_records:
            long_record = pair['long_record']
            short_record = pair['short_record']
            test_case_id = pair['test_case_id']
            
            # 币种映射
            coin_mapping = {
                'BTC': 'BTC-USDT',
                'ETH': 'ETH-USDT',
                'DOG': 'DOGE-USDT'
            }
            
            symbol = coin_mapping.get(long_record['coin'], f"{long_record['coin']}-USDT")
            
            # 生成两条记录（多头和空头）
            # 多头记录
            long_platform_record = {
                # 平台格式字段
                'id': f"test_{test_case_id}_long",
                'symbol': symbol,
                'uid': long_record.get('user_id', 'test_user'),
                'order_id': long_record.get('record_id'),
                'open_amount': long_record.get('usdt_amount'),
                'close_amount': long_record.get('usdt_amount') + long_record.get('profit_loss', 0),
                'open_raw_amount': long_record.get('coin_amount', 0),
                'close_raw_amount': long_record.get('coin_amount', 0),
                'pnl': long_record.get('profit_loss', 0),
                'net_pnl': long_record.get('profit_loss', 0),
                'deduction': 0.0,
                'open_time': long_record.get('buy_time'),
                'close_time': long_record.get('sell_time'),
                
                # 匹配字段（指向空头）
                'match_uid': short_record.get('user_id', 'test_user'),
                'match_order_id': short_record.get('record_id'),
                'match_open_amount': short_record.get('usdt_amount'),
                'match_close_amount': short_record.get('usdt_amount') + short_record.get('profit_loss', 0),
                'match_open_raw_amount': short_record.get('coin_amount', 0),
                'match_close_raw_amount': short_record.get('coin_amount', 0),
                'match_pnl': short_record.get('profit_loss', 0),
                'match_net_pnl': short_record.get('profit_loss', 0),
                'match_deduction': 0.0,
                'match_open_time': short_record.get('buy_time'),
                'match_close_time': short_record.get('sell_time'),
                
                # 计算评分（简化）
                'score': 0.85,  # 测试数据默认高分
                'create_time': self.calculate_create_time(long_record, short_record),
                'brand': 'TEST',
                
                # 保留测试数据特有字段
                'test_case_id': test_case_id,
                'test_type': long_record.get('test_type'),
                'original_long_record': long_record,
                'original_short_record': short_record
            }
            
            # 空头记录
            short_platform_record = {
                # 平台格式字段
                'id': f"test_{test_case_id}_short",
                'symbol': symbol,
                'uid': short_record.get('user_id', 'test_user'),
                'order_id': short_record.get('record_id'),
                'open_amount': short_record.get('usdt_amount'),
                'close_amount': short_record.get('usdt_amount') + short_record.get('profit_loss', 0),
                'open_raw_amount': short_record.get('coin_amount', 0),
                'close_raw_amount': short_record.get('coin_amount', 0),
                'pnl': short_record.get('profit_loss', 0),
                'net_pnl': short_record.get('profit_loss', 0),
                'deduction': 0.0,
                'open_time': short_record.get('buy_time'),
                'close_time': short_record.get('sell_time'),
                
                # 匹配字段（指向多头）
                'match_uid': long_record.get('user_id', 'test_user'),
                'match_order_id': long_record.get('record_id'),
                'match_open_amount': long_record.get('usdt_amount'),
                'match_close_amount': long_record.get('usdt_amount') + long_record.get('profit_loss', 0),
                'match_open_raw_amount': long_record.get('coin_amount', 0),
                'match_close_raw_amount': long_record.get('coin_amount', 0),
                'match_pnl': long_record.get('profit_loss', 0),
                'match_net_pnl': long_record.get('profit_loss', 0),
                'match_deduction': 0.0,
                'match_open_time': long_record.get('buy_time'),
                'match_close_time': long_record.get('sell_time'),
                
                # 计算评分（简化）
                'score': 0.85,  # 测试数据默认高分
                'create_time': self.calculate_create_time(long_record, short_record),
                'brand': 'TEST',
                
                # 保留测试数据特有字段
                'test_case_id': test_case_id,
                'test_type': short_record.get('test_type'),
                'original_long_record': long_record,
                'original_short_record': short_record
            }
            
            converted_records.extend([long_platform_record, short_platform_record])
        
        print(f"✅ 转换完成，生成 {len(converted_records)} 条平台格式记录")
        return converted_records
    
    def calculate_create_time(self, long_record: Dict, short_record: Dict) -> str:
        """计算创建时间（最晚平仓时间后30秒）"""
        try:
            long_close = datetime.fromisoformat(long_record['sell_time'].replace('Z', '+00:00'))
            short_close = datetime.fromisoformat(short_record['sell_time'].replace('Z', '+00:00'))
            latest_close = max(long_close, short_close)
            
            # 加30秒作为平台检测时间
            create_time = latest_close.replace(microsecond=0).replace(tzinfo=None)
            return create_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
        except:
            return datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]

    def deduplicate_records(self, records: List[Dict], record_type: str = "platform") -> List[Dict]:
        """去重记录"""
        print(f"🔄 {record_type}数据去重...")

        if record_type == "platform":
            return self.deduplicate_platform_records(records)
        else:
            return self.deduplicate_test_records(records)

    def deduplicate_platform_records(self, records: List[Dict]) -> List[Dict]:
        """去重平台记录 - 处理A匹配B和B匹配A的情况"""
        seen_pairs = set()
        unique_records = []

        for record in records:
            order_id = str(record.get('order_id', ''))
            match_order_id = str(record.get('match_order_id', ''))
            symbol = str(record.get('symbol', ''))

            # 创建配对标识（较小的order_id在前，确保A-B和B-A生成相同的key）
            if order_id and match_order_id:
                pair_key = f"{symbol}|{min(order_id, match_order_id)}|{max(order_id, match_order_id)}"
            else:
                # 如果没有匹配信息，使用原有逻辑
                pair_key = f"{symbol}|{order_id}|{record.get('open_time', '')}|{record.get('close_time', '')}"

            if pair_key not in seen_pairs:
                seen_pairs.add(pair_key)
                unique_records.append(record)
            else:
                print(f"⚠️ 发现重复配对: {order_id} <-> {match_order_id}")

        print(f"✅ 平台数据去重完成: {len(records)} -> {len(unique_records)} 条记录")
        return unique_records

    def deduplicate_test_records(self, records: List[Dict]) -> List[Dict]:
        """去重测试记录"""
        seen_hashes = set()
        unique_records = []

        for record in records:
            # 测试数据：基于test_case_id, order_id
            key_fields = [
                str(record.get('test_case_id', '')),
                str(record.get('order_id', ''))
            ]

            # 生成哈希
            key_string = '|'.join(key_fields)
            record_hash = hashlib.md5(key_string.encode()).hexdigest()

            if record_hash not in seen_hashes:
                seen_hashes.add(record_hash)
                unique_records.append(record)
            else:
                print(f"⚠️ 发现重复记录: {key_string[:50]}...")

        print(f"✅ 测试数据去重完成: {len(records)} -> {len(unique_records)} 条记录")
        return unique_records

    def save_results(self, test_platform_format: List[Dict], platform_data: List[Dict],
                    output_dir: str = "."):
        """保存处理结果"""
        print("💾 保存处理结果...")

        # 保存转换后的测试数据
        test_output_file = f"{output_dir}/test_data_platform_format.json"
        with open(test_output_file, 'w', encoding='utf-8') as f:
            json.dump(test_platform_format, f, ensure_ascii=False, indent=2, default=str)
        print(f"✅ 测试数据（平台格式）已保存: {test_output_file}")

        # 保存去重后的平台数据
        platform_output_file = f"{output_dir}/platform_data_deduplicated.json"
        with open(platform_output_file, 'w', encoding='utf-8') as f:
            json.dump(platform_data, f, ensure_ascii=False, indent=2, default=str)
        print(f"✅ 平台数据（去重后）已保存: {platform_output_file}")

        # 保存合并数据
        combined_data = test_platform_format + platform_data
        combined_output_file = f"{output_dir}/combined_data.json"
        with open(combined_output_file, 'w', encoding='utf-8') as f:
            json.dump(combined_data, f, ensure_ascii=False, indent=2, default=str)
        print(f"✅ 合并数据已保存: {combined_output_file}")

        # 生成统计报告
        self.generate_summary_report(test_platform_format, platform_data, output_dir)

    def generate_summary_report(self, test_data: List[Dict], platform_data: List[Dict],
                               output_dir: str):
        """生成统计报告"""
        print("📊 生成统计报告...")

        # 统计信息
        test_symbols = {}
        platform_symbols = {}

        for record in test_data:
            symbol = record.get('symbol', 'Unknown')
            test_symbols[symbol] = test_symbols.get(symbol, 0) + 1

        for record in platform_data:
            symbol = record.get('symbol', 'Unknown')
            platform_symbols[symbol] = platform_symbols.get(symbol, 0) + 1

        report = {
            'processing_time': datetime.now().isoformat(),
            'summary': {
                'test_data_records': len(test_data),
                'platform_data_records': len(platform_data),
                'total_records': len(test_data) + len(platform_data)
            },
            'test_data_by_symbol': test_symbols,
            'platform_data_by_symbol': platform_symbols,
            'test_case_ids': list(set([r.get('test_case_id') for r in test_data if r.get('test_case_id')]))
        }

        report_file = f"{output_dir}/processing_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=str)

        print(f"✅ 统计报告已保存: {report_file}")

        # 打印摘要
        print("\n📋 处理摘要:")
        print(f"  测试数据记录: {len(test_data)} 条")
        print(f"  平台数据记录: {len(platform_data)} 条")
        print(f"  总记录数: {len(test_data) + len(platform_data)} 条")
        print(f"  测试案例数: {len(report['test_case_ids'])} 个")

        print("\n🪙 按币种分布:")
        all_symbols = set(list(test_symbols.keys()) + list(platform_symbols.keys()))
        for symbol in sorted(all_symbols):
            test_count = test_symbols.get(symbol, 0)
            platform_count = platform_symbols.get(symbol, 0)
            print(f"  {symbol}: 测试 {test_count} 条, 平台 {platform_count} 条")


def main():
    """主函数"""
    print("🚀 开始数据处理...")
    print("="*60)

    # 文件路径
    test_data_file = "../../测试记录/测试数据.json"
    platform_data_file = "../../测试记录/平台数据.json"

    processor = DataProcessor()

    try:
        # 1. 加载数据
        print("\n1️⃣ 加载原始数据")
        test_records = processor.load_test_data(test_data_file)
        platform_records = processor.load_platform_data(platform_data_file)

        # 2. 配对测试数据
        print("\n2️⃣ 配对测试数据")
        paired_test_data = processor.pair_test_data_by_case_id(test_records)

        # 3. 转换为平台格式
        print("\n3️⃣ 转换为平台格式")
        test_platform_format = processor.convert_to_platform_format(paired_test_data)

        # 4. 去重
        print("\n4️⃣ 数据去重")
        test_platform_format = processor.deduplicate_records(test_platform_format, "test")
        platform_records = processor.deduplicate_records(platform_records, "platform")

        # 5. 保存结果
        print("\n5️⃣ 保存结果")
        processor.save_results(test_platform_format, platform_records)

        print("\n✅ 数据处理完成！")

    except Exception as e:
        print(f"❌ 处理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
