#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据分离器：将匹配和未匹配的测试数据按原格式分别存放
"""

import json
from datetime import datetime
from typing import Dict, List, Any

class DataSeparator:
    def __init__(self):
        self.original_test_data = []
        self.analysis_results = {}
        
    def load_data(self):
        """加载原始数据和分析结果"""
        print("📊 加载数据...")
        
        # 加载原始测试数据
        with open('../../测试记录/测试跑的全部数据.json', 'r', encoding='utf-8') as f:
            original_data = json.load(f)
            self.original_test_data = original_data.get('records', [])
        
        # 加载分析结果
        with open('correct_wash_trading_analysis.json', 'r', encoding='utf-8') as f:
            self.analysis_results = json.load(f)
        
        print(f"✅ 原始测试数据: {len(self.original_test_data)} 条")
        print(f"✅ 分析结果已加载")
    
    def extract_test_case_ids(self) -> Dict[str, List[str]]:
        """提取匹配和未匹配的test_case_id"""
        matched_case_ids = set()
        unmatched_case_ids = set()
        
        # 提取匹配成功的test_case_id
        for match in self.analysis_results['matched_details']:
            case_id = match['test_pair']['test_case_id']
            matched_case_ids.add(case_id)
        
        # 提取未匹配的test_case_id
        for unmatched in self.analysis_results['unmatched_analysis']['details']:
            case_id = unmatched['test_case_id']
            unmatched_case_ids.add(case_id)
        
        print(f"📊 匹配成功的test_case_id: {len(matched_case_ids)} 个")
        print(f"📊 未匹配的test_case_id: {len(unmatched_case_ids)} 个")
        
        return {
            'matched': list(matched_case_ids),
            'unmatched': list(unmatched_case_ids)
        }
    
    def separate_original_data(self, case_ids: Dict[str, List[str]]) -> Dict[str, List[Dict]]:
        """根据test_case_id分离原始数据"""
        print("🔄 分离原始数据...")
        
        matched_records = []
        unmatched_records = []
        
        for record in self.original_test_data:
            test_case_id = record.get('test_case_id', '')
            
            # 提取基础case_id（去掉_long/_short后缀）
            if test_case_id.endswith('_long'):
                base_case_id = test_case_id[:-5]
            elif test_case_id.endswith('_short'):
                base_case_id = test_case_id[:-6]
            else:
                continue
            
            # 根据base_case_id分类
            if base_case_id in case_ids['matched']:
                matched_records.append(record)
            elif base_case_id in case_ids['unmatched']:
                unmatched_records.append(record)
        
        print(f"✅ 匹配成功的原始记录: {len(matched_records)} 条")
        print(f"✅ 未匹配的原始记录: {len(unmatched_records)} 条")
        
        return {
            'matched': matched_records,
            'unmatched': unmatched_records
        }
    
    def create_data_files(self, separated_data: Dict[str, List[Dict]]):
        """创建分离后的数据文件"""
        print("💾 创建数据文件...")
        
        # 71%匹配成功的数据（按原格式）
        matched_data = {
            "metadata": {
                "description": "匹配成功的测试数据 - 平台成功检测到的对敲交易",
                "total_records": len(separated_data['matched']),
                "total_pairs": len(separated_data['matched']) // 2,
                "match_rate": "71%",
                "generation_time": datetime.now().isoformat(),
                "note": "这些数据被平台成功检测为对敲交易"
            },
            "records": separated_data['matched']
        }
        
        # 29%未匹配的数据（按原格式）
        unmatched_data = {
            "metadata": {
                "description": "未匹配的测试数据 - 平台可能漏检的对敲交易",
                "total_records": len(separated_data['unmatched']),
                "total_pairs": len(separated_data['unmatched']) // 2,
                "miss_rate": "29%",
                "generation_time": datetime.now().isoformat(),
                "note": "这些数据可能被平台漏检，需要优化检测算法"
            },
            "records": separated_data['unmatched']
        }
        
        # 保存71%匹配成功的数据
        matched_file = '71%_matched_test_data.json'
        with open(matched_file, 'w', encoding='utf-8') as f:
            json.dump(matched_data, f, ensure_ascii=False, indent=2, default=str)
        print(f"✅ 71%匹配成功数据: {matched_file}")
        
        # 保存29%未匹配的数据
        unmatched_file = '29%_unmatched_test_data.json'
        with open(unmatched_file, 'w', encoding='utf-8') as f:
            json.dump(unmatched_data, f, ensure_ascii=False, indent=2, default=str)
        print(f"✅ 29%未匹配数据: {unmatched_file}")
        
        return matched_file, unmatched_file
    
    def generate_summary_report(self, separated_data: Dict[str, List[Dict]]):
        """生成分离数据的摘要报告"""
        print("📊 生成摘要报告...")
        
        def analyze_data(records: List[Dict], data_type: str):
            """分析数据特征"""
            analysis = {
                'total_records': len(records),
                'total_pairs': len(records) // 2,
                'by_coin': {},
                'by_test_type': {},
                'amount_stats': {
                    'amounts': [],
                    'min_amount': 0,
                    'max_amount': 0,
                    'avg_amount': 0
                }
            }
            
            for record in records:
                coin = record.get('coin', 'Unknown')
                test_type = record.get('test_type', 'Unknown')
                amount = record.get('usdt_amount', 0)
                
                # 按币种统计
                analysis['by_coin'][coin] = analysis['by_coin'].get(coin, 0) + 1
                
                # 按测试类型统计
                analysis['by_test_type'][test_type] = analysis['by_test_type'].get(test_type, 0) + 1
                
                # 金额统计
                if amount > 0:
                    analysis['amount_stats']['amounts'].append(amount)
            
            # 计算金额统计
            amounts = analysis['amount_stats']['amounts']
            if amounts:
                analysis['amount_stats']['min_amount'] = min(amounts)
                analysis['amount_stats']['max_amount'] = max(amounts)
                analysis['amount_stats']['avg_amount'] = sum(amounts) / len(amounts)
            
            return analysis
        
        matched_analysis = analyze_data(separated_data['matched'], 'matched')
        unmatched_analysis = analyze_data(separated_data['unmatched'], 'unmatched')
        
        summary_report = {
            'generation_time': datetime.now().isoformat(),
            'overview': {
                'total_original_records': len(self.original_test_data),
                'matched_records': matched_analysis['total_records'],
                'unmatched_records': unmatched_analysis['total_records'],
                'matched_pairs': matched_analysis['total_pairs'],
                'unmatched_pairs': unmatched_analysis['total_pairs'],
                'match_rate_percent': round(matched_analysis['total_pairs'] / (matched_analysis['total_pairs'] + unmatched_analysis['total_pairs']) * 100, 2)
            },
            'matched_data_analysis': matched_analysis,
            'unmatched_data_analysis': unmatched_analysis,
            'comparison': {
                'avg_amount_matched': matched_analysis['amount_stats']['avg_amount'],
                'avg_amount_unmatched': unmatched_analysis['amount_stats']['avg_amount'],
                'amount_ratio': unmatched_analysis['amount_stats']['avg_amount'] / matched_analysis['amount_stats']['avg_amount'] if matched_analysis['amount_stats']['avg_amount'] > 0 else 0
            }
        }
        
        # 保存摘要报告
        summary_file = 'data_separation_summary.json'
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary_report, f, ensure_ascii=False, indent=2, default=str)
        print(f"✅ 摘要报告: {summary_file}")
        
        return summary_report
    
    def print_summary(self, summary_report: Dict):
        """打印摘要信息"""
        overview = summary_report['overview']
        matched = summary_report['matched_data_analysis']
        unmatched = summary_report['unmatched_data_analysis']
        comparison = summary_report['comparison']
        
        print("\n" + "="*60)
        print("📋 数据分离摘要")
        print("="*60)
        
        print(f"📊 总体统计:")
        print(f"  原始记录总数: {overview['total_original_records']} 条")
        print(f"  匹配成功记录: {overview['matched_records']} 条 ({overview['matched_pairs']} 对)")
        print(f"  未匹配记录: {overview['unmatched_records']} 条 ({overview['unmatched_pairs']} 对)")
        print(f"  匹配率: {overview['match_rate_percent']}%")
        
        print(f"\n✅ 71%匹配成功数据特征:")
        print(f"  平均金额: {comparison['avg_amount_matched']:.2f} USDT")
        print(f"  币种分布: {matched['by_coin']}")
        print(f"  测试类型: {matched['by_test_type']}")
        
        print(f"\n⚠️ 29%未匹配数据特征:")
        print(f"  平均金额: {comparison['avg_amount_unmatched']:.2f} USDT")
        print(f"  币种分布: {unmatched['by_coin']}")
        print(f"  测试类型: {unmatched['by_test_type']}")
        
        print(f"\n📈 对比分析:")
        print(f"  未匹配数据平均金额是匹配数据的 {comparison['amount_ratio']:.1%}")
        
        print(f"\n💾 生成的文件:")
        print(f"  71%_matched_test_data.json - 匹配成功的数据")
        print(f"  29%_unmatched_test_data.json - 未匹配的数据")
        print(f"  data_separation_summary.json - 摘要报告")


def main():
    """主函数"""
    print("🚀 开始数据分离...")
    print("="*60)
    
    separator = DataSeparator()
    
    try:
        # 1. 加载数据
        separator.load_data()
        
        # 2. 提取test_case_id
        case_ids = separator.extract_test_case_ids()
        
        # 3. 分离原始数据
        separated_data = separator.separate_original_data(case_ids)
        
        # 4. 创建数据文件
        separator.create_data_files(separated_data)
        
        # 5. 生成摘要报告
        summary_report = separator.generate_summary_report(separated_data)
        
        # 6. 打印摘要
        separator.print_summary(summary_report)
        
        print("\n✅ 数据分离完成！")
        
    except Exception as e:
        print(f"❌ 分离过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
