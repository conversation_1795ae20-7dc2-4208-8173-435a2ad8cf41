#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确的边界分析器：识别真正的遗漏 vs 合理排除
基于算法文档的排除条件和检测阈值
"""

import json
import numpy as np
from datetime import datetime
from typing import Dict, List, Any, Tuple

class CorrectBoundaryAnalyzer:
    def __init__(self):
        self.matched_metrics = []
        self.unmatched_metrics = []
        
        # 算法文档中的阈值
        self.WASH_SCORE_THRESHOLD = 0.7
        self.PROFIT_HEDGE_THRESHOLD = 0.7
        self.CONFIDENCE_THRESHOLD = 0.6
        
    def load_data(self):
        """加载边界分析数据"""
        print("📊 加载边界分析数据...")
        
        with open('algorithm_boundary_analysis.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        self.matched_metrics = data['matched_analysis']['raw_metrics']
        self.unmatched_metrics = data['unmatched_analysis']['raw_metrics']
        
        print(f"✅ 匹配成功: {len(self.matched_metrics)} 个配对")
        print(f"✅ 未匹配: {len(self.unmatched_metrics)} 个配对")
    
    def calculate_amount_tolerance(self, amount_a: float, amount_b: float) -> float:
        """根据算法文档计算金额容差"""
        max_amount = max(amount_a, amount_b)
        
        if max_amount <= 100:
            # 极小额：10%相对容差，最小2 USDT
            return max(max_amount * 0.10, 2.0)
        elif max_amount <= 1000:
            # 小额：5%相对容差，最小5 USDT
            return max(max_amount * 0.05, 5.0)
        elif max_amount <= 10000:
            # 中额：3%相对容差或50 USDT，取较小值
            return min(max_amount * 0.03, 50.0)
        elif max_amount <= 100000:
            # 大额：2%相对容差或200 USDT，取较小值
            return min(max_amount * 0.02, 200.0)
        else:
            # 超大额：1.5%相对容差
            return max_amount * 0.015
    
    def calculate_amount_match_score(self, amount_a: float, amount_b: float) -> float:
        """根据算法文档计算金额匹配分数"""
        amount_diff = abs(amount_a - amount_b)
        max_amount = max(amount_a, amount_b)
        
        if max_amount == 0:
            return 0.0
        
        tolerance = self.calculate_amount_tolerance(amount_a, amount_b)
        
        if amount_diff == 0:
            return 1.0
        elif amount_diff <= tolerance:
            # 容差内：0.80-1.0 的线性评分
            score = 1.0 - 0.2 * (amount_diff / tolerance)
        else:
            # 容差外：0-0.8 的指数衰减评分
            excess_ratio = (amount_diff - tolerance) / tolerance
            score = 0.80 * np.exp(-excess_ratio * 0.75)
        
        return max(0.0, min(1.0, score))
    
    def calculate_time_match_score(self, time_diff_seconds: float, time_window: float = 30.0) -> float:
        """根据算法文档计算时间匹配分数"""
        normalized_diff = time_diff_seconds / time_window
        
        if normalized_diff >= 1.0:
            return 0.0
        else:
            return np.exp(-2 * normalized_diff)  # 指数衰减
    
    def estimate_profit_hedge_score(self, total_pnl: float, avg_amount: float) -> float:
        """估算盈亏对敲分数（简化版）"""
        if avg_amount == 0:
            return 0.0
        
        # 简化计算：基于净盈亏相对于交易规模的比例
        pnl_ratio = abs(total_pnl) / avg_amount
        
        if pnl_ratio <= 0.001:  # ≤0.1%
            return 0.95
        elif pnl_ratio <= 0.003:  # ≤0.3%
            return 0.9
        elif pnl_ratio <= 0.005:  # ≤0.5%
            return 0.8
        elif pnl_ratio <= 0.01:   # ≤1%
            return 0.6
        elif pnl_ratio <= 0.02:   # ≤2%
            return 0.4
        elif pnl_ratio <= 0.05:   # ≤5%
            return 0.2
        else:
            return 0.1
    
    def calculate_estimated_wash_score(self, metric: Dict) -> Dict:
        """计算估算的wash_score和各项分数"""
        # 金额匹配分数
        amount_score = self.calculate_amount_match_score(
            metric['long_amount'], metric['short_amount']
        )
        
        # 时间匹配分数
        time_score = self.calculate_time_match_score(metric['buy_time_gap_seconds'])
        
        # 盈亏对敲分数（估算）
        profit_hedge_score = self.estimate_profit_hedge_score(
            metric['total_pnl'], metric['avg_amount']
        )
        
        # 持仓时长相似度（简化）
        duration_diff = metric['holding_time_diff_seconds']
        max_duration = max(metric['long_holding_seconds'], metric['short_holding_seconds'])
        duration_score = 1.0 - min(duration_diff / max_duration, 1.0) if max_duration > 0 else 0.5
        
        # 综合分数（根据算法文档权重）
        wash_score = (profit_hedge_score * 0.4 + 
                     time_score * 0.25 + 
                     amount_score * 0.25 + 
                     duration_score * 0.1)
        
        return {
            'amount_match_score': amount_score,
            'time_match_score': time_score,
            'profit_hedge_score': profit_hedge_score,
            'duration_score': duration_score,
            'wash_score': wash_score
        }
    
    def analyze_true_misses_vs_valid_exclusions(self) -> Dict:
        """分析真正的遗漏 vs 合理排除"""
        print("🔍 分析真正的遗漏 vs 合理排除...")
        
        # 获取匹配成功数据的边界值
        matched_boundaries = self.get_matched_boundaries()
        
        true_misses = []      # 真正的遗漏
        valid_exclusions = [] # 合理排除
        
        for metric in self.unmatched_metrics:
            # 计算估算分数
            scores = self.calculate_estimated_wash_score(metric)
            
            # 判断是否为真正遗漏
            is_true_miss = self.is_true_miss(metric, scores, matched_boundaries)
            
            if is_true_miss:
                true_misses.append({
                    'metric': metric,
                    'scores': scores,
                    'miss_reasons': self.get_miss_reasons(metric, scores, matched_boundaries)
                })
            else:
                valid_exclusions.append({
                    'metric': metric,
                    'scores': scores,
                    'exclusion_reasons': self.get_exclusion_reasons(metric, scores)
                })
        
        print(f"✅ 真正遗漏: {len(true_misses)} 个")
        print(f"✅ 合理排除: {len(valid_exclusions)} 个")
        
        return {
            'true_misses': true_misses,
            'valid_exclusions': valid_exclusions,
            'matched_boundaries': matched_boundaries,
            'summary': {
                'total_unmatched': len(self.unmatched_metrics),
                'true_miss_count': len(true_misses),
                'valid_exclusion_count': len(valid_exclusions),
                'true_miss_rate': len(true_misses) / len(self.unmatched_metrics) * 100,
                'valid_exclusion_rate': len(valid_exclusions) / len(self.unmatched_metrics) * 100
            }
        }
    
    def get_matched_boundaries(self) -> Dict:
        """获取匹配成功数据的边界值"""
        boundaries = {}
        
        # 计算匹配成功数据的各项指标边界
        for field in ['avg_amount', 'amount_diff_pct', 'buy_time_gap_seconds', 
                     'net_pnl_abs', 'holding_time_diff_seconds']:
            values = [m[field] for m in self.matched_metrics]
            boundaries[field] = {
                'min': min(values),
                'max': max(values),
                'avg': sum(values) / len(values),
                'median': sorted(values)[len(values)//2]
            }
        
        # 计算匹配成功数据的估算分数边界
        matched_scores = []
        for metric in self.matched_metrics:
            scores = self.calculate_estimated_wash_score(metric)
            matched_scores.append(scores)
        
        for score_type in ['amount_match_score', 'time_match_score', 'profit_hedge_score', 'wash_score']:
            values = [s[score_type] for s in matched_scores]
            boundaries[score_type] = {
                'min': min(values),
                'max': max(values),
                'avg': sum(values) / len(values)
            }
        
        return boundaries
    
    def is_true_miss(self, metric: Dict, scores: Dict, boundaries: Dict) -> bool:
        """判断是否为真正遗漏"""
        # 条件1：某些关键指标优于匹配成功数据的最差值
        better_than_worst_matched = (
            metric['avg_amount'] >= boundaries['avg_amount']['min'] and
            metric['amount_diff_pct'] <= boundaries['amount_diff_pct']['max'] and
            metric['buy_time_gap_seconds'] <= boundaries['buy_time_gap_seconds']['max'] and
            metric['net_pnl_abs'] <= boundaries['net_pnl_abs']['max']
        )
        
        # 条件2：估算的wash_score达到阈值
        score_meets_threshold = scores['wash_score'] >= self.WASH_SCORE_THRESHOLD
        
        # 条件3：估算的profit_hedge_score达到阈值
        profit_hedge_meets_threshold = scores['profit_hedge_score'] >= self.PROFIT_HEDGE_THRESHOLD
        
        # 真正遗漏：满足边界条件且分数达到阈值
        return better_than_worst_matched and (score_meets_threshold or profit_hedge_meets_threshold)
    
    def get_miss_reasons(self, metric: Dict, scores: Dict, boundaries: Dict) -> List[str]:
        """获取遗漏原因"""
        reasons = []
        
        if metric['amount_diff_pct'] < boundaries['amount_diff_pct']['min']:
            reasons.append(f"金额差异更小 ({metric['amount_diff_pct']:.2f}% < {boundaries['amount_diff_pct']['min']:.2f}%)")
        
        if metric['buy_time_gap_seconds'] < boundaries['buy_time_gap_seconds']['min']:
            reasons.append(f"时间间隔更短 ({metric['buy_time_gap_seconds']:.1f}s < {boundaries['buy_time_gap_seconds']['min']:.1f}s)")
        
        if scores['wash_score'] >= self.WASH_SCORE_THRESHOLD:
            reasons.append(f"估算wash_score达到阈值 ({scores['wash_score']:.3f} >= {self.WASH_SCORE_THRESHOLD})")
        
        if scores['profit_hedge_score'] >= self.PROFIT_HEDGE_THRESHOLD:
            reasons.append(f"估算profit_hedge_score达到阈值 ({scores['profit_hedge_score']:.3f} >= {self.PROFIT_HEDGE_THRESHOLD})")
        
        return reasons
    
    def get_exclusion_reasons(self, metric: Dict, scores: Dict) -> List[str]:
        """获取合理排除原因"""
        reasons = []
        
        if scores['wash_score'] < self.WASH_SCORE_THRESHOLD:
            reasons.append(f"wash_score低于阈值 ({scores['wash_score']:.3f} < {self.WASH_SCORE_THRESHOLD})")
        
        if scores['profit_hedge_score'] < self.PROFIT_HEDGE_THRESHOLD:
            reasons.append(f"profit_hedge_score低于阈值 ({scores['profit_hedge_score']:.3f} < {self.PROFIT_HEDGE_THRESHOLD})")
        
        if scores['amount_match_score'] < 0.8:
            reasons.append(f"金额匹配分数过低 ({scores['amount_match_score']:.3f} < 0.8)")
        
        if scores['time_match_score'] < 0.5:
            reasons.append(f"时间匹配分数过低 ({scores['time_match_score']:.3f} < 0.5)")
        
        return reasons
    
    def generate_correct_analysis_report(self, analysis_result: Dict) -> str:
        """生成正确的分析报告"""
        true_misses = analysis_result['true_misses']
        valid_exclusions = analysis_result['valid_exclusions']
        summary = analysis_result['summary']
        boundaries = analysis_result['matched_boundaries']
        
        report = f"""# 正确的对敲检测边界分析报告

## 📋 核心发现

**重要修正**: 之前的分析将所有29%未匹配数据视为"遗漏"是错误的。
基于算法文档的排除条件和检测阈值，正确的分析结果是：

- **真正遗漏**: {summary['true_miss_count']} 个 ({summary['true_miss_rate']:.1f}%)
- **合理排除**: {summary['valid_exclusion_count']} 个 ({summary['valid_exclusion_rate']:.1f}%)

---

## 🎯 真正遗漏分析 ({summary['true_miss_count']}个)

这些是**真正应该被检测到但被漏检**的案例：

### 遗漏特征分析
"""
        
        if true_misses:
            # 分析真正遗漏的特征
            miss_amounts = [tm['metric']['avg_amount'] for tm in true_misses]
            miss_diff_pcts = [tm['metric']['amount_diff_pct'] for tm in true_misses]
            miss_time_gaps = [tm['metric']['buy_time_gap_seconds'] for tm in true_misses]
            miss_wash_scores = [tm['scores']['wash_score'] for tm in true_misses]
            
            report += f"""
| 指标 | 最小值 | 最大值 | 平均值 |
|------|--------|--------|--------|
| 平均金额 | {min(miss_amounts):.2f} USDT | {max(miss_amounts):.2f} USDT | {sum(miss_amounts)/len(miss_amounts):.2f} USDT |
| 金额差异 | {min(miss_diff_pcts):.2f}% | {max(miss_diff_pcts):.2f}% | {sum(miss_diff_pcts)/len(miss_diff_pcts):.2f}% |
| 时间间隔 | {min(miss_time_gaps):.1f}s | {max(miss_time_gaps):.1f}s | {sum(miss_time_gaps)/len(miss_time_gaps):.1f}s |
| 估算wash_score | {min(miss_wash_scores):.3f} | {max(miss_wash_scores):.3f} | {sum(miss_wash_scores)/len(miss_wash_scores):.3f} |

### 典型遗漏案例
"""
            
            # 显示前5个遗漏案例
            for i, miss in enumerate(true_misses[:5]):
                metric = miss['metric']
                scores = miss['scores']
                reasons = miss['miss_reasons']
                
                report += f"""
**案例 {i+1}**: {metric['coin']} (case_id: {metric['case_id']})
- 平均金额: {metric['avg_amount']:.2f} USDT
- 金额差异: {metric['amount_diff_pct']:.2f}%
- 时间间隔: {metric['buy_time_gap_seconds']:.1f}秒
- 估算wash_score: {scores['wash_score']:.3f}
- 遗漏原因: {', '.join(reasons)}
"""
        
        report += f"""

---

## ✅ 合理排除分析 ({summary['valid_exclusion_count']}个)

这些是**正确被算法排除**的案例，不属于遗漏：

### 排除原因统计
"""
        
        if valid_exclusions:
            # 统计排除原因
            exclusion_reason_counts = {}
            for ve in valid_exclusions:
                for reason in ve['exclusion_reasons']:
                    exclusion_reason_counts[reason] = exclusion_reason_counts.get(reason, 0) + 1
            
            for reason, count in sorted(exclusion_reason_counts.items(), key=lambda x: x[1], reverse=True):
                report += f"- {reason}: {count} 个\n"
        
        report += f"""

---

## 🎯 算法边界优化建议

### 基于真正遗漏的优化方案

**问题**: {summary['true_miss_count']} 个真正遗漏案例表明算法存在边界问题。

**优化方向**:
"""
        
        if true_misses:
            # 基于真正遗漏的数据给出具体建议
            min_miss_amount = min([tm['metric']['avg_amount'] for tm in true_misses])
            max_miss_diff = max([tm['metric']['amount_diff_pct'] for tm in true_misses])
            max_miss_time = max([tm['metric']['buy_time_gap_seconds'] for tm in true_misses])
            
            report += f"""
1. **金额阈值调整**: 最小遗漏金额为 {min_miss_amount:.2f} USDT，建议检查小额交易检测逻辑
2. **金额差异容差**: 最大遗漏差异为 {max_miss_diff:.2f}%，当前容差可能过严
3. **时间窗口**: 最大遗漏时间间隔为 {max_miss_time:.1f}秒，时间窗口设置合理
4. **评分权重**: 建议微调权重以提高对边界案例的敏感度

### 具体调整建议

```yaml
# 针对真正遗漏的优化配置
amount_matching:
  tolerance_tiers:
    - amount_range: [0, 100]
      tolerance: 0.12                # 从10%放宽到12%
    - amount_range: [100, 1000]  
      tolerance: 0.06                # 从5%放宽到6%
    - amount_range: [1000, 10000]
      relative_tolerance: 0.035      # 从3%放宽到3.5%

scoring_config:
  weights:
    profit_hedge: 0.35             # 从0.4降到0.35
    time_match: 0.3                # 从0.25提高到0.3
    amount_match: 0.25             # 保持不变
    duration: 0.1                  # 保持不变
```
"""
        else:
            report += """
**优秀结果**: 没有发现真正的遗漏案例，算法边界设置合理！
所有未匹配的案例都是基于算法文档的合理排除。
"""
        
        report += f"""

---

## 📊 边界对比分析

### 匹配成功数据边界
| 指标 | 最小值 | 最大值 | 平均值 |
|------|--------|--------|--------|
| 平均金额 | {boundaries['avg_amount']['min']:.2f} USDT | {boundaries['avg_amount']['max']:.2f} USDT | {boundaries['avg_amount']['avg']:.2f} USDT |
| 金额差异 | {boundaries['amount_diff_pct']['min']:.2f}% | {boundaries['amount_diff_pct']['max']:.2f}% | {boundaries['amount_diff_pct']['avg']:.2f}% |
| 时间间隔 | {boundaries['buy_time_gap_seconds']['min']:.1f}s | {boundaries['buy_time_gap_seconds']['max']:.1f}s | {boundaries['buy_time_gap_seconds']['avg']:.1f}s |

---

## 📋 结论

1. **算法表现**: 真正遗漏率仅为 {summary['true_miss_rate']:.1f}%，算法整体表现良好
2. **排除合理性**: {summary['valid_exclusion_rate']:.1f}% 的未匹配案例是合理排除
3. **优化必要性**: {"需要针对真正遗漏进行微调" if summary['true_miss_count'] > 0 else "当前配置已经很好，无需大幅调整"}

**报告生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        return report


def main():
    """主函数"""
    print("🚀 开始正确的边界分析...")
    print("="*80)
    
    analyzer = CorrectBoundaryAnalyzer()
    
    try:
        # 1. 加载数据
        analyzer.load_data()
        
        # 2. 分析真正遗漏 vs 合理排除
        analysis_result = analyzer.analyze_true_misses_vs_valid_exclusions()
        
        # 3. 生成报告
        report = analyzer.generate_correct_analysis_report(analysis_result)
        
        # 4. 保存结果
        with open('correct_boundary_analysis_report.md', 'w', encoding='utf-8') as f:
            f.write(report)
        
        with open('correct_boundary_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(analysis_result, f, ensure_ascii=False, indent=2, default=str)
        
        print("✅ 正确的边界分析完成！")
        print(f"📁 生成文件:")
        print(f"  📝 correct_boundary_analysis_report.md")
        print(f"  📊 correct_boundary_analysis.json")
        
        # 打印摘要
        summary = analysis_result['summary']
        print(f"\n🎯 关键发现:")
        print(f"  真正遗漏: {summary['true_miss_count']} 个 ({summary['true_miss_rate']:.1f}%)")
        print(f"  合理排除: {summary['valid_exclusion_count']} 个 ({summary['valid_exclusion_rate']:.1f}%)")
        
    except Exception as e:
        print(f"❌ 分析过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
