#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对敲匹配分析器：比较测试数据和平台数据的匹配情况
重点关注：测试有的平台没有的（可能漏检）
"""

import json
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Tuple
import hashlib

class WashTradingMatchAnalyzer:
    def __init__(self):
        self.test_data = []
        self.platform_data = []
        self.matches = []
        self.test_only = []  # 测试有，平台没有
        self.platform_only = []  # 平台有，测试没有
        
    def load_data(self, test_file: str, platform_file: str):
        """加载测试数据和平台数据"""
        print("📊 加载数据...")
        
        # 加载测试数据
        with open(test_file, 'r', encoding='utf-8') as f:
            self.test_data = json.load(f)
        print(f"✅ 测试数据: {len(self.test_data)} 条")
        
        # 加载平台数据
        with open(platform_file, 'r', encoding='utf-8') as f:
            self.platform_data = json.load(f)
        print(f"✅ 平台数据: {len(self.platform_data)} 条")
    
    def extract_test_pairs(self) -> List[Dict]:
        """提取测试数据中的配对信息"""
        print("🔄 提取测试配对...")
        
        pairs = {}
        for record in self.test_data:
            test_case_id = record.get('test_case_id')
            if not test_case_id:
                continue
                
            if test_case_id not in pairs:
                pairs[test_case_id] = {'long': None, 'short': None}
            
            # 根据order_id判断多空方向
            order_id = record.get('order_id', '')
            if 'long' in order_id:
                pairs[test_case_id]['long'] = record
            elif 'short' in order_id:
                pairs[test_case_id]['short'] = record
        
        # 转换为配对列表
        test_pairs = []
        for case_id, pair in pairs.items():
            if pair['long'] and pair['short']:
                test_pairs.append({
                    'test_case_id': case_id,
                    'long_record': pair['long'],
                    'short_record': pair['short'],
                    'symbol': pair['long']['symbol'],
                    'long_amount': pair['long']['open_amount'],
                    'short_amount': pair['short']['open_amount'],
                    'long_time': pair['long']['open_time'],
                    'short_time': pair['short']['open_time'],
                    'create_time': pair['long']['create_time']  # 使用计算的创建时间
                })
        
        print(f"✅ 提取到 {len(test_pairs)} 个测试配对")
        return test_pairs
    
    def calculate_match_score(self, test_pair: Dict, platform_record: Dict) -> Tuple[float, Dict]:
        """计算匹配分数"""
        score_details = {}
        
        # 1. 币种匹配 (必须匹配)
        if test_pair['symbol'] != platform_record.get('symbol'):
            return 0.0, {'reason': 'symbol_mismatch'}
        score_details['symbol_match'] = True
        
        # 2. 金额匹配
        test_amounts = [test_pair['long_amount'], test_pair['short_amount']]
        platform_amount = platform_record.get('open_amount', 0)
        platform_match_amount = platform_record.get('match_open_amount', 0)
        
        # 检查测试金额是否与平台金额匹配
        amount_matches = []
        for test_amount in test_amounts:
            # 与主记录金额匹配
            diff1 = abs(test_amount - platform_amount) / max(test_amount, platform_amount) if max(test_amount, platform_amount) > 0 else 0
            # 与匹配记录金额匹配
            diff2 = abs(test_amount - platform_match_amount) / max(test_amount, platform_match_amount) if max(test_amount, platform_match_amount) > 0 else 0
            
            min_diff = min(diff1, diff2)
            amount_matches.append(min_diff)
        
        best_amount_match = min(amount_matches)
        amount_score = max(0, 1 - best_amount_match * 5)  # 20%差异得0分
        score_details['amount_diff'] = best_amount_match
        score_details['amount_score'] = amount_score
        
        # 3. 时间匹配
        try:
            test_create_time = datetime.fromisoformat(test_pair['create_time'].replace(' ', 'T'))
            platform_create_time = datetime.fromisoformat(platform_record.get('create_time', '').replace(' ', 'T'))
            
            time_diff = abs((platform_create_time - test_create_time).total_seconds())
            time_score = max(0, 1 - time_diff / 300)  # 5分钟内满分，超过5分钟0分
            
            score_details['time_diff_seconds'] = time_diff
            score_details['time_score'] = time_score
        except:
            time_score = 0
            score_details['time_diff_seconds'] = float('inf')
            score_details['time_score'] = 0
        
        # 综合分数
        if amount_score < 0.5:  # 金额差异太大
            total_score = 0
        else:
            total_score = amount_score * 0.7 + time_score * 0.3
        
        score_details['total_score'] = total_score
        return total_score, score_details
    
    def find_matches(self, test_pairs: List[Dict]) -> Dict:
        """寻找匹配"""
        print("🔍 寻找匹配...")
        
        matches = []
        unmatched_test = []
        matched_platform_ids = set()
        
        for test_pair in test_pairs:
            best_match = None
            best_score = 0
            best_details = {}
            
            for platform_record in self.platform_data:
                platform_id = platform_record.get('id')
                if platform_id in matched_platform_ids:
                    continue
                
                score, details = self.calculate_match_score(test_pair, platform_record)
                
                if score > best_score and score >= 0.6:  # 最低匹配阈值
                    best_score = score
                    best_match = platform_record
                    best_details = details
            
            if best_match:
                matches.append({
                    'test_pair': test_pair,
                    'platform_record': best_match,
                    'match_score': best_score,
                    'match_details': best_details
                })
                matched_platform_ids.add(best_match.get('id'))
            else:
                unmatched_test.append(test_pair)
        
        # 未匹配的平台记录
        unmatched_platform = [
            record for record in self.platform_data 
            if record.get('id') not in matched_platform_ids
        ]
        
        print(f"✅ 找到匹配: {len(matches)} 个")
        print(f"⚠️ 测试未匹配: {len(unmatched_test)} 个")
        print(f"ℹ️ 平台未匹配: {len(unmatched_platform)} 个")
        
        return {
            'matches': matches,
            'test_only': unmatched_test,
            'platform_only': unmatched_platform
        }
    
    def analyze_test_only(self, test_only: List[Dict]) -> Dict:
        """分析测试有但平台没有的记录"""
        print("🔍 分析测试独有记录...")
        
        analysis = {
            'total_count': len(test_only),
            'by_symbol': {},
            'by_amount_range': {},
            'by_time_range': {},
            'details': []
        }
        
        for test_pair in test_only:
            symbol = test_pair['symbol']
            amounts = [test_pair['long_amount'], test_pair['short_amount']]
            avg_amount = sum(amounts) / len(amounts)
            create_time = test_pair['create_time']
            
            # 按币种统计
            if symbol not in analysis['by_symbol']:
                analysis['by_symbol'][symbol] = 0
            analysis['by_symbol'][symbol] += 1
            
            # 按金额范围统计
            if avg_amount < 100:
                range_key = '<100'
            elif avg_amount < 1000:
                range_key = '100-1000'
            elif avg_amount < 10000:
                range_key = '1000-10000'
            else:
                range_key = '>10000'
            
            if range_key not in analysis['by_amount_range']:
                analysis['by_amount_range'][range_key] = 0
            analysis['by_amount_range'][range_key] += 1
            
            # 详细信息
            analysis['details'].append({
                'test_case_id': test_pair['test_case_id'],
                'symbol': symbol,
                'long_amount': test_pair['long_amount'],
                'short_amount': test_pair['short_amount'],
                'amount_diff_pct': abs(test_pair['long_amount'] - test_pair['short_amount']) / max(test_pair['long_amount'], test_pair['short_amount']) * 100,
                'create_time': create_time,
                'long_time': test_pair['long_time'],
                'short_time': test_pair['short_time']
            })
        
        return analysis
    
    def generate_report(self, results: Dict):
        """生成分析报告"""
        print("📊 生成分析报告...")
        
        matches = results['matches']
        test_only = results['test_only']
        platform_only = results['platform_only']
        
        # 基础统计
        total_test_pairs = len(matches) + len(test_only)
        total_platform_records = len(matches) + len(platform_only)
        
        match_rate = len(matches) / total_test_pairs * 100 if total_test_pairs > 0 else 0
        
        report = {
            'summary': {
                'total_test_pairs': total_test_pairs,
                'total_platform_records': total_platform_records,
                'matched_pairs': len(matches),
                'match_rate_percent': round(match_rate, 2),
                'test_only_count': len(test_only),
                'platform_only_count': len(platform_only)
            },
            'matches': matches,
            'test_only_analysis': self.analyze_test_only(test_only),
            'platform_only': platform_only,
            'generation_time': datetime.now().isoformat()
        }
        
        return report
    
    def save_results(self, report: Dict, output_dir: str = "."):
        """保存分析结果"""
        print("💾 保存分析结果...")
        
        # 保存完整报告
        report_file = f"{output_dir}/wash_trading_match_analysis.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=str)
        print(f"✅ 完整报告: {report_file}")
        
        # 保存测试独有记录
        test_only_file = f"{output_dir}/test_only_records.json"
        with open(test_only_file, 'w', encoding='utf-8') as f:
            json.dump(report['test_only_analysis'], f, ensure_ascii=False, indent=2, default=str)
        print(f"✅ 测试独有记录: {test_only_file}")
        
        # 保存平台独有记录
        platform_only_file = f"{output_dir}/platform_only_records.json"
        with open(platform_only_file, 'w', encoding='utf-8') as f:
            json.dump(report['platform_only'], f, ensure_ascii=False, indent=2, default=str)
        print(f"✅ 平台独有记录: {platform_only_file}")
        
        # 打印摘要
        self.print_summary(report)
    
    def print_summary(self, report: Dict):
        """打印分析摘要"""
        summary = report['summary']
        test_analysis = report['test_only_analysis']
        
        print("\n" + "="*60)
        print("📋 对敲匹配分析摘要")
        print("="*60)
        
        print(f"📊 总体统计:")
        print(f"  测试配对总数: {summary['total_test_pairs']}")
        print(f"  平台记录总数: {summary['total_platform_records']}")
        print(f"  成功匹配: {summary['matched_pairs']} 个")
        print(f"  匹配率: {summary['match_rate_percent']}%")
        
        print(f"\n🔍 重点关注 - 测试有，平台没有:")
        print(f"  总数: {summary['test_only_count']} 个")
        if test_analysis['by_symbol']:
            print(f"  按币种分布:")
            for symbol, count in test_analysis['by_symbol'].items():
                print(f"    {symbol}: {count} 个")
        
        print(f"\nℹ️ 平台独有记录: {summary['platform_only_count']} 个")
        
        if summary['test_only_count'] > 0:
            print(f"\n⚠️ 可能存在漏检的测试案例，需要进一步分析！")


def main():
    """主函数"""
    print("🚀 开始对敲匹配分析...")
    print("="*60)
    
    analyzer = WashTradingMatchAnalyzer()
    
    try:
        # 1. 加载数据
        analyzer.load_data(
            'test_data_platform_format.json',
            'platform_data_deduplicated.json'
        )
        
        # 2. 提取测试配对
        test_pairs = analyzer.extract_test_pairs()
        
        # 3. 寻找匹配
        results = analyzer.find_matches(test_pairs)
        
        # 4. 生成报告
        report = analyzer.generate_report(results)
        
        # 5. 保存结果
        analyzer.save_results(report)
        
        print("\n✅ 对敲匹配分析完成！")
        
    except Exception as e:
        print(f"❌ 分析过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
