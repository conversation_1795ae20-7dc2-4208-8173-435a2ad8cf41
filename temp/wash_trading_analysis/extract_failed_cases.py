#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提取检测失败的20个案例的完整测试数据
"""

import json
from datetime import datetime

def extract_failed_detection_cases():
    """提取检测失败案例的完整数据"""
    print("🔍 提取检测失败的20个案例...")
    
    # 1. 加载边界分析结果，获取失败的case_id列表
    with open('correct_boundary_analysis.json', 'r', encoding='utf-8') as f:
        boundary_data = json.load(f)
    
    failed_case_ids = []
    for miss in boundary_data['true_misses']:
        case_id = miss['metric']['case_id']
        failed_case_ids.append(case_id)
    
    print(f"✅ 找到 {len(failed_case_ids)} 个失败案例的case_id")
    print(f"失败的case_id列表: {failed_case_ids}")
    
    # 2. 加载原始测试数据
    with open('../../测试记录/测试跑的全部数据.json', 'r', encoding='utf-8') as f:
        original_data = json.load(f)
        all_test_records = original_data.get('records', [])
    
    print(f"✅ 加载原始测试数据: {len(all_test_records)} 条记录")
    
    # 3. 提取失败案例的完整记录
    failed_records = []
    
    for record in all_test_records:
        test_case_id = record.get('test_case_id', '')
        
        # 提取基础case_id（去掉_long/_short后缀）
        if test_case_id.endswith('_long'):
            base_case_id = test_case_id[:-5]
        elif test_case_id.endswith('_short'):
            base_case_id = test_case_id[:-6]
        else:
            continue
        
        # 如果是失败案例，则添加到结果中
        if base_case_id in failed_case_ids:
            failed_records.append(record)
    
    print(f"✅ 提取到失败案例记录: {len(failed_records)} 条")
    
    # 4. 按case_id分组统计
    case_stats = {}
    for record in failed_records:
        test_case_id = record.get('test_case_id', '')
        if test_case_id.endswith('_long'):
            base_case_id = test_case_id[:-5]
        elif test_case_id.endswith('_short'):
            base_case_id = test_case_id[:-6]
        else:
            continue
        
        if base_case_id not in case_stats:
            case_stats[base_case_id] = {'long': 0, 'short': 0, 'coin': ''}
        
        if test_case_id.endswith('_long'):
            case_stats[base_case_id]['long'] += 1
        elif test_case_id.endswith('_short'):
            case_stats[base_case_id]['short'] += 1
        
        case_stats[base_case_id]['coin'] = record.get('coin', '')
    
    # 5. 创建失败案例数据文件
    failed_data = {
        "metadata": {
            "description": "检测失败的对敲交易案例 - 真正遗漏的20个配对",
            "total_records": len(failed_records),
            "total_pairs": len(failed_case_ids),
            "generation_time": datetime.now().isoformat(),
            "source": "基于correct_boundary_analysis.json中的true_misses提取",
            "note": "这些案例应该被检测为对敲交易但被算法漏检，需要优化算法参数",
            "case_statistics": case_stats
        },
        "failed_case_summary": [
            {
                "case_id": case_id,
                "coin": case_stats[case_id]['coin'],
                "long_records": case_stats[case_id]['long'],
                "short_records": case_stats[case_id]['short'],
                "total_records": case_stats[case_id]['long'] + case_stats[case_id]['short']
            }
            for case_id in failed_case_ids if case_id in case_stats
        ],
        "records": failed_records
    }
    
    # 6. 保存失败案例数据
    output_file = 'failed_detection_cases.json'
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(failed_data, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"✅ 失败案例数据已保存: {output_file}")
    
    # 7. 生成摘要报告
    generate_failed_cases_summary(failed_data, failed_case_ids)
    
    return failed_data

def generate_failed_cases_summary(failed_data, failed_case_ids):
    """生成失败案例摘要报告"""
    
    summary_report = f"""# 检测失败案例摘要报告

## 📊 基本信息

- **失败案例总数**: {len(failed_case_ids)} 个配对
- **失败记录总数**: {failed_data['metadata']['total_records']} 条
- **数据来源**: 原始测试数据中的真正遗漏案例
- **生成时间**: {failed_data['metadata']['generation_time']}

## 📋 失败案例列表

| Case ID | 币种 | 多头记录 | 空头记录 | 总记录 |
|---------|------|----------|----------|--------|
"""
    
    for case_summary in failed_data['failed_case_summary']:
        summary_report += f"| {case_summary['case_id']} | {case_summary['coin']} | {case_summary['long_records']} | {case_summary['short_records']} | {case_summary['total_records']} |\n"
    
    summary_report += f"""

## 📈 统计分析

### 按币种分布
"""
    
    coin_stats = {}
    for case_summary in failed_data['failed_case_summary']:
        coin = case_summary['coin']
        coin_stats[coin] = coin_stats.get(coin, 0) + 1
    
    for coin, count in sorted(coin_stats.items()):
        summary_report += f"- **{coin}**: {count} 个配对\n"
    
    summary_report += f"""

### 数据完整性检查
- **预期配对数**: {len(failed_case_ids)}
- **实际提取配对数**: {len(failed_data['failed_case_summary'])}
- **预期记录数**: {len(failed_case_ids) * 2} (每配对2条记录)
- **实际记录数**: {failed_data['metadata']['total_records']}

## 🎯 用途说明

这些失败案例可用于：

1. **算法调优验证**: 调整参数后用这些案例验证改进效果
2. **边界测试**: 作为边界条件的测试集
3. **回归测试**: 确保算法优化不会引入新问题
4. **特征分析**: 深入分析这些案例的共同特征

## 📁 文件说明

- **failed_detection_cases.json**: 完整的失败案例数据（原始测试格式）
- **failed_cases_summary.md**: 本摘要报告

---

**注意**: 这些案例都是经过边界分析确认的真正遗漏，不是合理排除的案例。
"""
    
    # 保存摘要报告
    with open('failed_cases_summary.md', 'w', encoding='utf-8') as f:
        f.write(summary_report)
    
    print(f"✅ 摘要报告已保存: failed_cases_summary.md")

def print_extraction_summary(failed_data):
    """打印提取摘要"""
    print("\n" + "="*60)
    print("📋 检测失败案例提取摘要")
    print("="*60)
    
    print(f"📊 提取结果:")
    print(f"  失败配对数: {failed_data['metadata']['total_pairs']} 个")
    print(f"  失败记录数: {failed_data['metadata']['total_records']} 条")
    
    print(f"\n📈 按币种分布:")
    coin_stats = {}
    for case_summary in failed_data['failed_case_summary']:
        coin = case_summary['coin']
        coin_stats[coin] = coin_stats.get(coin, 0) + 1
    
    for coin, count in sorted(coin_stats.items()):
        print(f"  {coin}: {count} 个配对")
    
    print(f"\n📁 生成文件:")
    print(f"  📊 failed_detection_cases.json - 完整失败案例数据")
    print(f"  📝 failed_cases_summary.md - 摘要报告")
    
    print(f"\n🎯 用途:")
    print(f"  1. 算法参数调优验证")
    print(f"  2. 边界条件测试")
    print(f"  3. 回归测试")
    print(f"  4. 特征分析")

def main():
    """主函数"""
    print("🚀 开始提取检测失败案例...")
    print("="*60)
    
    try:
        # 提取失败案例
        failed_data = extract_failed_detection_cases()
        
        # 打印摘要
        print_extraction_summary(failed_data)
        
        print("\n✅ 检测失败案例提取完成！")
        
    except Exception as e:
        print(f"❌ 提取过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
