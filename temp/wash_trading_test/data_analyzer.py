#!/usr/bin/env python3
"""
对敲检测数据分析和报告生成器
分析测试结果，生成对敲检测相关的统计报告
"""

import json
import csv
import sqlite3
import math
from typing import Dict, List, Any, Tuple, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass
import statistics

from trade_recorder import TradeRecorder, TradeRecord


@dataclass
class WashTradingAnalysis:
    """对敲分析结果"""
    pair_id: str
    record_a: TradeRecord
    record_b: TradeRecord
    time_diff_seconds: float
    amount_diff_usdt: float
    amount_diff_percent: float
    time_match_score: float
    amount_match_score: float
    profit_hedge_score: float
    wash_score: float
    risk_level: str
    is_wash_trading: bool


class WashTradingDataAnalyzer:
    """对敲检测数据分析器"""
    
    def __init__(self, db_path: str = "wash_trading_test.db"):
        """初始化分析器"""
        self.db_path = db_path
        self.recorder = TradeRecorder(db_path)
        self.analysis_results = []
    
    def calculate_time_match_score(self, time_diff_seconds: float, time_window: float = 30.0) -> float:
        """计算时间匹配分数
        
        Args:
            time_diff_seconds: 时间差（秒）
            time_window: 时间窗口（秒）
            
        Returns:
            时间匹配分数 (0-1)
        """
        if time_diff_seconds >= time_window:
            return 0.0
        
        normalized_diff = time_diff_seconds / time_window
        return math.exp(-2 * normalized_diff)  # 指数衰减
    
    def calculate_amount_match_score(self, amount_a: float, amount_b: float) -> float:
        """计算金额匹配分数
        
        Args:
            amount_a: 金额A
            amount_b: 金额B
            
        Returns:
            金额匹配分数 (0-1)
        """
        if amount_a == 0 or amount_b == 0:
            return 0.0
        
        amount_diff = abs(amount_a - amount_b)
        max_amount = max(amount_a, amount_b)
        
        # 计算容差
        if max_amount <= 100:
            tolerance = max(max_amount * 0.10, 2.0)
        elif max_amount <= 1000:
            tolerance = max(max_amount * 0.05, 5.0)
        elif max_amount <= 10000:
            tolerance = min(max_amount * 0.03, 50.0)
        elif max_amount <= 100000:
            tolerance = min(max_amount * 0.02, 200.0)
        else:
            tolerance = max_amount * 0.015
        
        if amount_diff == 0:
            return 1.0
        elif amount_diff <= tolerance:
            return 1.0 - 0.2 * (amount_diff / tolerance)
        else:
            excess_ratio = (amount_diff - tolerance) / tolerance
            return 0.80 * math.exp(-excess_ratio * 0.75)

    def calculate_amount_tolerance(self, amount_a: float, amount_b: float) -> float:
        """计算金额容差

        Args:
            amount_a: 金额A
            amount_b: 金额B

        Returns:
            容差值
        """
        max_amount = max(amount_a, amount_b)

        if max_amount <= 100:
            return max_amount * 0.05  # 5%
        elif max_amount <= 1000:
            return max_amount * 0.03  # 3%
        elif max_amount <= 10000:
            return max_amount * 0.02  # 2%
        else:
            return max_amount * 0.01  # 1%

    def calculate_profit_hedge_score(self, profit_a: float, profit_b: float,
                                   amount_a: float, amount_b: float) -> float:
        """计算盈亏对敲分数（基于算法文档的完整实现）

        Args:
            profit_a: 盈亏A
            profit_b: 盈亏B
            amount_a: 交易金额A
            amount_b: 交易金额B

        Returns:
            盈亏对敲分数 (0-1)
        """
        # 基础检查
        total_profit = profit_a + profit_b
        profit_sum = abs(profit_a) + abs(profit_b)

        # 特殊情况：总盈亏为0（完全抵消，包括无盈亏情况）
        if abs(total_profit) == 0:
            return 0.8  # 给予较高分数，这也是对敲特征

        # 判断是否同边
        is_same_side = (profit_a > 0 and profit_b > 0) or (profit_a < 0 and profit_b < 0)

        if not is_same_side:
            # 异边（一盈一亏）：经典对敲模式
            if profit_sum == 0:
                return 0.5  # 无盈亏数据
            return 1.0 - abs(total_profit) / profit_sum
        else:
            # 同边（双盈/双亏）：优化后的接近0 + 相对波动率
            abs_a = abs(profit_a)
            abs_b = abs(profit_b)
            min_abs = min(abs_a, abs_b)

            # 交易量规模
            scale = amount_a + amount_b
            if scale == 0:
                return 0.5  # 无法计算

            # 优化的接近0程度评分（放宽阈值）
            min_relative = min_abs / scale
            if min_relative <= 0.001:      # ≤0.1%，几乎无盈亏
                closeness_score = 0.95
            elif min_relative <= 0.003:    # ≤0.3%，很接近0
                closeness_score = 0.9
            elif min_relative <= 0.005:    # ≤0.5%，接近0
                closeness_score = 0.8
            elif min_relative <= 0.01:     # ≤1%，比较接近0
                closeness_score = 0.6
            elif min_relative <= 0.02:     # ≤2%，稍微接近0
                closeness_score = 0.4
            elif min_relative <= 0.05:     # ≤5%，不太接近0
                closeness_score = 0.2
            else:                          # >5%，远离0
                closeness_score = 0.1

            # 相对波动率评分
            rate_a = abs_a / amount_a if amount_a > 0 else 0
            rate_b = abs_b / amount_b if amount_b > 0 else 0

            min_rate = min(rate_a, rate_b)
            max_rate = max(rate_a, rate_b)

            # 比值越小，差异越大，越可能是对敲
            if max_rate > 0:
                rate_ratio = min_rate / max_rate

                if rate_ratio <= 0.1:        # 差异巨大（小方是大方的10%以下）
                    volatility_score = 0.9
                elif rate_ratio <= 0.2:      # 差异很大（小方是大方的20%以下）
                    volatility_score = 0.8
                elif rate_ratio <= 0.5:      # 差异较大（小方是大方的50%以下）
                    volatility_score = 0.6
                elif rate_ratio <= 0.8:      # 差异一般（小方是大方的80%以下）
                    volatility_score = 0.4
                else:                        # 差异很小（比值>0.8）
                    volatility_score = 0.2
            else:
                volatility_score = 0.5

            # 权重：接近0(60%) + 相对波动率(40%)
            return 0.6 * closeness_score + 0.4 * volatility_score
    
    def calculate_wash_score(self, time_match_score: float, amount_match_score: float,
                           profit_hedge_score: float, duration_similarity: float = 0.5) -> float:
        """计算综合对敲分数
        
        Args:
            time_match_score: 时间匹配分数
            amount_match_score: 金额匹配分数
            profit_hedge_score: 盈亏对敲分数
            duration_similarity: 持仓时长相似度
            
        Returns:
            综合对敲分数 (0-1)
        """
        # 权重配置
        weights = {
            'profit_hedge': 0.4,
            'time_match': 0.25,
            'amount_match': 0.25,
            'duration': 0.1
        }
        
        return (profit_hedge_score * weights['profit_hedge'] +
                time_match_score * weights['time_match'] +
                amount_match_score * weights['amount_match'] +
                duration_similarity * weights['duration'])
    
    def classify_risk_level(self, wash_score: float, time_match_score: float,
                          profit_hedge_score: float) -> str:
        """分类风险等级
        
        Args:
            wash_score: 综合对敲分数
            time_match_score: 时间匹配分数
            profit_hedge_score: 盈亏对敲分数
            
        Returns:
            风险等级
        """
        if profit_hedge_score > 0.9 and time_match_score > 0.8:
            return 'Critical'
        elif wash_score > 0.85:
            return 'High'
        elif wash_score > 0.7:
            return 'Medium'
        elif wash_score > 0.5:
            return 'Low'
        else:
            return 'Minimal'
    
    def pair_long_short_records(self, records: List[TradeRecord]) -> List[Tuple[TradeRecord, TradeRecord]]:
        """将多头和空头记录配对

        Args:
            records: 交易记录列表

        Returns:
            配对的(long_record, short_record)列表
        """
        # 按test_case_id分组
        case_groups = {}
        for record in records:
            # 从record_id中提取test_case_id和方向
            parts = record.record_id.split('_')
            if len(parts) >= 2:
                test_case_id = parts[0]
                direction = parts[1]  # 'long' or 'short'

                if test_case_id not in case_groups:
                    case_groups[test_case_id] = {'long': None, 'short': None}

                case_groups[test_case_id][direction] = record

        # 配对long和short
        pairs = []
        for test_case_id, group in case_groups.items():
            if group['long'] and group['short']:
                # 确保是同一币种
                if group['long'].coin == group['short'].coin:
                    pairs.append((group['long'], group['short']))
                    print(f"✅ 配对成功: {test_case_id} {group['long'].coin} - Long: {group['long'].usdt_amount}U, Short: {group['short'].usdt_amount}U")

        print(f"📊 成功配对 {len(pairs)} 个多空对")
        return pairs

    def find_potential_wash_trading_pairs(self, time_window: float = 30.0) -> List[WashTradingAnalysis]:
        """查找潜在的对敲交易对

        Args:
            time_window: 时间窗口（秒）

        Returns:
            对敲分析结果列表
        """
        # 加载交易记录
        records = self.recorder.load_from_database()
        successful_records = [r for r in records if r.trade_success]

        print(f"📊 分析 {len(successful_records)} 条成功交易记录")

        # 先配对多空记录
        long_short_pairs = self.pair_long_short_records(successful_records)

        analysis_results = []
        pair_id = 1

        # 分析每个多空配对
        for long_record, short_record in long_short_pairs:
            coin = long_record.coin

            # 计算时间差（多空开仓时间差）
            try:
                time_long = datetime.fromisoformat(long_record.buy_time)
                time_short = datetime.fromisoformat(short_record.buy_time)
                time_diff = abs((time_long - time_short).total_seconds())
            except:
                continue

            # 只分析时间窗口内的交易对
            if time_diff <= time_window:
                # 计算各项分数
                time_match_score = self.calculate_time_match_score(time_diff, time_window)
                amount_match_score = self.calculate_amount_match_score(
                    long_record.usdt_amount, short_record.usdt_amount
                )

                # 使用真实的盈亏数据
                profit_long = long_record.profit_loss if long_record.profit_loss is not None else 0.0
                profit_short = short_record.profit_loss if short_record.profit_loss is not None else 0.0

                profit_hedge_score = self.calculate_profit_hedge_score(
                    profit_long, profit_short, long_record.usdt_amount, short_record.usdt_amount
                )
                wash_score = self.calculate_wash_score(
                    time_match_score, amount_match_score, profit_hedge_score
                )

                # 分类风险等级
                risk_level = self.classify_risk_level(
                    wash_score, time_match_score, profit_hedge_score
                )

                # 判断是否为对敲
                is_wash_trading = wash_score > 0.7 and profit_hedge_score > 0.7

                # 计算金额差异
                amount_diff_usdt = abs(long_record.usdt_amount - short_record.usdt_amount)
                max_amount = max(long_record.usdt_amount, short_record.usdt_amount)
                amount_diff_percent = (amount_diff_usdt / max_amount * 100) if max_amount > 0 else 0

                analysis = WashTradingAnalysis(
                    pair_id=f"{coin}_{pair_id}",
                    record_a=long_record,
                    record_b=short_record,
                    time_diff_seconds=time_diff,
                    amount_diff_usdt=amount_diff_usdt,
                    amount_diff_percent=amount_diff_percent,
                    time_match_score=time_match_score,
                    amount_match_score=amount_match_score,
                    profit_hedge_score=profit_hedge_score,
                    wash_score=wash_score,
                    risk_level=risk_level,
                    is_wash_trading=is_wash_trading
                )

                analysis_results.append(analysis)
                pair_id += 1
        
        # 按对敲分数排序
        analysis_results.sort(key=lambda x: x.wash_score, reverse=True)
        self.analysis_results = analysis_results
        
        return analysis_results

    def match_with_platform_data(self, test_pairs: List[WashTradingAnalysis],
                                platform_data_file: str, time_window: float = 30.0) -> List[Dict[str, Any]]:
        """将测试配对与平台数据匹配（多维度匹配：时间+金额+币名）

        Args:
            test_pairs: 测试数据的多空配对分析结果
            platform_data_file: 平台数据文件路径
            time_window: 时间窗口（秒），默认30秒

        Returns:
            匹配结果列表
        """
        import json
        from datetime import datetime

        # 加载平台数据
        try:
            with open(platform_data_file, 'r', encoding='utf-8') as f:
                platform_data = json.load(f)
        except Exception as e:
            print(f"❌ 无法加载平台数据: {e}")
            return []

        print(f"📊 加载了 {len(platform_data)} 条平台记录")

        matches = []
        potential_matches = []

        # 币种映射
        coin_mapping = {
            'BTC': 'BTC-USDT',
            'ETH': 'ETH-USDT',
            'DOG': 'DOGE-USDT'
        }

        for test_pair in test_pairs:
            coin = test_pair.record_a.coin
            platform_symbol = coin_mapping.get(coin)

            if not platform_symbol:
                continue

            # 获取测试配对的信息
            test_amount = max(test_pair.record_a.usdt_amount, test_pair.record_b.usdt_amount)

            # 获取测试配对的最晚平仓时间（数据完整的时间点）
            try:
                long_close_time = datetime.fromisoformat(test_pair.record_a.sell_time)
                short_close_time = datetime.fromisoformat(test_pair.record_b.sell_time)
                test_latest_close_time = max(long_close_time, short_close_time)
            except:
                continue

            # 在平台数据中查找匹配
            for platform_record in platform_data:
                if platform_record.get('symbol') != platform_symbol:
                    continue

                platform_amount = platform_record.get('open_amount', 0)

                # 1. 币名匹配 ✓（已通过symbol过滤）

                # 2. 金额匹配检查
                amount_tolerance = self.calculate_amount_tolerance(test_amount, platform_amount)
                amount_diff = abs(test_amount - platform_amount)
                amount_match = amount_diff <= amount_tolerance
                amount_match_score = self.calculate_amount_match_score(test_amount, platform_amount)

                # 3. 时间匹配检查（平台创建时间应该在最晚平仓时间后30秒内）
                try:
                    platform_create_time = datetime.fromisoformat(platform_record.get('create_time', ''))
                    time_diff = (platform_create_time - test_latest_close_time).total_seconds()
                    # 平台时间应该晚于平仓时间，但在时间窗口内
                    time_match = 0 <= time_diff <= time_window
                    # 对于评分，使用绝对时间差
                    abs_time_diff = abs(time_diff) if time_diff >= 0 else time_diff + time_window
                    time_match_score = self.calculate_time_match_score(abs_time_diff, time_window) if time_match else 0.0
                except:
                    time_diff = float('inf')
                    time_match = False
                    time_match_score = 0.0

                # 综合匹配评分
                overall_match_score = (amount_match_score * 0.5 + time_match_score * 0.5)

                # 匹配条件：三个维度都要满足基本要求
                is_exact_match = amount_match and time_match and overall_match_score >= 0.7
                is_potential_match = (amount_match or time_match) and overall_match_score >= 0.3

                match_info = {
                    'test_pair_id': test_pair.pair_id,
                    'platform_record_id': platform_record.get('id'),
                    'coin': coin,
                    'symbol': platform_symbol,
                    'test_amount': test_amount,
                    'platform_amount': platform_amount,
                    'amount_diff': amount_diff,
                    'amount_tolerance': amount_tolerance,
                    'amount_diff_percent': (amount_diff / max(test_amount, platform_amount)) * 100,
                    'amount_match': amount_match,
                    'amount_match_score': amount_match_score,
                    'time_diff_seconds': time_diff,
                    'time_match': time_match,
                    'time_match_score': time_match_score,
                    'overall_match_score': overall_match_score,
                    'test_wash_score': test_pair.wash_score,
                    'test_profit_hedge_score': test_pair.profit_hedge_score,
                    'platform_score': platform_record.get('score', 0),
                    'test_time_diff': test_pair.time_diff_seconds,
                    'test_profit_long': test_pair.record_a.profit_loss,
                    'test_profit_short': test_pair.record_b.profit_loss,
                    'test_latest_close_time': test_latest_close_time.isoformat(),
                    'platform_create_time': platform_record.get('create_time', ''),
                    'is_exact_match': is_exact_match,
                    'is_potential_match': is_potential_match
                }

                if is_exact_match:
                    matches.append(match_info)
                elif is_potential_match:
                    potential_matches.append(match_info)

        # 按综合匹配分数排序
        matches.sort(key=lambda x: x['overall_match_score'], reverse=True)
        potential_matches.sort(key=lambda x: x['overall_match_score'], reverse=True)

        print(f"✅ 找到 {len(matches)} 个精确匹配，{len(potential_matches)} 个潜在匹配")
        return {
            'exact_matches': matches,
            'potential_matches': potential_matches,
            'total_exact': len(matches),
            'total_potential': len(potential_matches)
        }
    
    def generate_analysis_report(self, analysis_results: List[WashTradingAnalysis] = None) -> Dict[str, Any]:
        """生成分析报告
        
        Args:
            analysis_results: 分析结果列表
            
        Returns:
            分析报告
        """
        if analysis_results is None:
            analysis_results = self.analysis_results
        
        if not analysis_results:
            return {'error': '没有分析结果'}
        
        # 基本统计
        total_pairs = len(analysis_results)
        wash_trading_pairs = [a for a in analysis_results if a.is_wash_trading]
        wash_trading_count = len(wash_trading_pairs)
        
        # 风险等级分布
        risk_distribution = {}
        for analysis in analysis_results:
            risk = analysis.risk_level
            if risk not in risk_distribution:
                risk_distribution[risk] = 0
            risk_distribution[risk] += 1
        
        # 币种分布
        coin_distribution = {}
        for analysis in analysis_results:
            coin = analysis.record_a.coin
            if coin not in coin_distribution:
                coin_distribution[coin] = {'total': 0, 'wash_trading': 0}
            coin_distribution[coin]['total'] += 1
            if analysis.is_wash_trading:
                coin_distribution[coin]['wash_trading'] += 1
        
        # 分数统计
        wash_scores = [a.wash_score for a in analysis_results]
        time_scores = [a.time_match_score for a in analysis_results]
        amount_scores = [a.amount_match_score for a in analysis_results]
        
        # 时间分布
        time_ranges = {
            '0-5s': 0, '5-15s': 0, '15-30s': 0, '30-60s': 0, '60s+': 0
        }
        for analysis in analysis_results:
            time_diff = analysis.time_diff_seconds
            if time_diff <= 5:
                time_ranges['0-5s'] += 1
            elif time_diff <= 15:
                time_ranges['5-15s'] += 1
            elif time_diff <= 30:
                time_ranges['15-30s'] += 1
            elif time_diff <= 60:
                time_ranges['30-60s'] += 1
            else:
                time_ranges['60s+'] += 1
        
        report = {
            'analysis_metadata': {
                'generated_at': datetime.now().isoformat(),
                'total_pairs_analyzed': total_pairs,
                'wash_trading_pairs_found': wash_trading_count,
                'wash_trading_rate': wash_trading_count / total_pairs * 100 if total_pairs > 0 else 0
            },
            'risk_distribution': risk_distribution,
            'coin_distribution': coin_distribution,
            'score_statistics': {
                'wash_score': {
                    'min': min(wash_scores) if wash_scores else 0,
                    'max': max(wash_scores) if wash_scores else 0,
                    'avg': statistics.mean(wash_scores) if wash_scores else 0,
                    'median': statistics.median(wash_scores) if wash_scores else 0
                },
                'time_match_score': {
                    'min': min(time_scores) if time_scores else 0,
                    'max': max(time_scores) if time_scores else 0,
                    'avg': statistics.mean(time_scores) if time_scores else 0
                },
                'amount_match_score': {
                    'min': min(amount_scores) if amount_scores else 0,
                    'max': max(amount_scores) if amount_scores else 0,
                    'avg': statistics.mean(amount_scores) if amount_scores else 0
                }
            },
            'time_distribution': time_ranges,
            'top_wash_trading_pairs': [
                {
                    'pair_id': a.pair_id,
                    'coin': a.record_a.coin,
                    'amount_a': a.record_a.usdt_amount,
                    'amount_b': a.record_b.usdt_amount,
                    'time_diff_seconds': a.time_diff_seconds,
                    'wash_score': a.wash_score,
                    'risk_level': a.risk_level
                }
                for a in analysis_results[:20]  # 前20个
            ]
        }
        
        return report
    
    def export_analysis_results(self, filename: str, format: str = 'json'):
        """导出分析结果
        
        Args:
            filename: 文件名
            format: 格式 ('json' 或 'csv')
        """
        if not self.analysis_results:
            print("❌ 没有分析结果可导出")
            return
        
        if format.lower() == 'json':
            report = self.generate_analysis_report()
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
        elif format.lower() == 'csv':
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = [
                    'pair_id', 'coin', 'amount_a', 'amount_b', 'time_diff_seconds',
                    'amount_diff_usdt', 'amount_diff_percent', 'time_match_score',
                    'amount_match_score', 'profit_hedge_score', 'wash_score',
                    'risk_level', 'is_wash_trading'
                ]
                
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                
                for analysis in self.analysis_results:
                    writer.writerow({
                        'pair_id': analysis.pair_id,
                        'coin': analysis.record_a.coin,
                        'amount_a': analysis.record_a.usdt_amount,
                        'amount_b': analysis.record_b.usdt_amount,
                        'time_diff_seconds': analysis.time_diff_seconds,
                        'amount_diff_usdt': analysis.amount_diff_usdt,
                        'amount_diff_percent': analysis.amount_diff_percent,
                        'time_match_score': analysis.time_match_score,
                        'amount_match_score': analysis.amount_match_score,
                        'profit_hedge_score': analysis.profit_hedge_score,
                        'wash_score': analysis.wash_score,
                        'risk_level': analysis.risk_level,
                        'is_wash_trading': analysis.is_wash_trading
                    })
        
        print(f"✅ 分析结果已导出到 {filename}")
    
    def print_analysis_summary(self):
        """打印分析摘要"""
        if not self.analysis_results:
            print("❌ 没有分析结果")
            return
        
        report = self.generate_analysis_report()
        
        print("📊 对敲检测分析报告")
        print("=" * 60)
        print(f"分析时间: {report['analysis_metadata']['generated_at']}")
        print(f"总交易对数: {report['analysis_metadata']['total_pairs_analyzed']}")
        print(f"疑似对敲对数: {report['analysis_metadata']['wash_trading_pairs_found']}")
        print(f"对敲检出率: {report['analysis_metadata']['wash_trading_rate']:.1f}%")
        print()
        
        print("🚨 风险等级分布:")
        for risk, count in report['risk_distribution'].items():
            percentage = count / report['analysis_metadata']['total_pairs_analyzed'] * 100
            print(f"  {risk}: {count} ({percentage:.1f}%)")
        print()
        
        print("🪙 币种分布:")
        for coin, data in report['coin_distribution'].items():
            wash_rate = data['wash_trading'] / data['total'] * 100 if data['total'] > 0 else 0
            print(f"  {coin}: {data['total']} 总对数, {data['wash_trading']} 疑似对敲 ({wash_rate:.1f}%)")
        print()
        
        print("📈 分数统计:")
        scores = report['score_statistics']
        print(f"  综合对敲分数: 平均 {scores['wash_score']['avg']:.3f}, 最高 {scores['wash_score']['max']:.3f}")
        print(f"  时间匹配分数: 平均 {scores['time_match_score']['avg']:.3f}")
        print(f"  金额匹配分数: 平均 {scores['amount_match_score']['avg']:.3f}")
        print()
        
        print("⏰ 时间分布:")
        for time_range, count in report['time_distribution'].items():
            percentage = count / report['analysis_metadata']['total_pairs_analyzed'] * 100
            print(f"  {time_range}: {count} ({percentage:.1f}%)")


def main():
    """主函数"""
    print("📊 对敲检测数据分析器")
    print("=" * 40)
    
    analyzer = WashTradingDataAnalyzer()
    
    while True:
        print("\n请选择:")
        print("1. 分析潜在对敲交易对")
        print("2. 查看分析摘要")
        print("3. 导出分析结果 (JSON)")
        print("4. 导出分析结果 (CSV)")
        print("5. 自定义时间窗口分析")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-5): ").strip()
        
        if choice == '0':
            break
        elif choice == '1':
            print("🔍 开始分析潜在对敲交易对...")
            results = analyzer.find_potential_wash_trading_pairs()
            print(f"✅ 分析完成，找到 {len(results)} 个交易对")
            analyzer.print_analysis_summary()
        elif choice == '2':
            analyzer.print_analysis_summary()
        elif choice == '3':
            filename = input("请输入JSON文件名 (默认: wash_trading_analysis.json): ").strip()
            if not filename:
                filename = "wash_trading_analysis.json"
            analyzer.export_analysis_results(filename, 'json')
        elif choice == '4':
            filename = input("请输入CSV文件名 (默认: wash_trading_analysis.csv): ").strip()
            if not filename:
                filename = "wash_trading_analysis.csv"
            analyzer.export_analysis_results(filename, 'csv')
        elif choice == '5':
            try:
                time_window = float(input("请输入时间窗口（秒，默认30）: ") or "30")
                print(f"🔍 使用 {time_window} 秒时间窗口分析...")
                results = analyzer.find_potential_wash_trading_pairs(time_window)
                print(f"✅ 分析完成，找到 {len(results)} 个交易对")
                analyzer.print_analysis_summary()
            except ValueError:
                print("❌ 请输入有效数字")
        else:
            print("❌ 无效选择")


def test_new_pairing_logic():
    """测试新的配对逻辑"""
    print("🧪 测试新的多空配对逻辑")
    print("="*50)

    analyzer = WashTradingDataAnalyzer()

    # 1. 分析多空配对
    print("1️⃣ 分析多空配对...")
    results = analyzer.find_potential_wash_trading_pairs(time_window=30.0)
    print(f"✅ 找到 {len(results)} 个配对")

    # 2. 30秒时间窗口精确匹配
    platform_data_file = "../../测试记录/平台抓取对敲数据.json"

    print(f"\n2️⃣ 30秒时间窗口精确匹配分析...")
    match_results = analyzer.match_with_platform_data(results, platform_data_file, time_window=30.0)

    # 如果30秒没有精确匹配，尝试稍微放宽到60秒
    if len(match_results['exact_matches']) == 0:
        print(f"\n⏰ 30秒无精确匹配，尝试60秒时间窗口...")
        match_results_60 = analyzer.match_with_platform_data(results, platform_data_file, time_window=60.0)

        if len(match_results_60['exact_matches']) > 0:
            print(f"✅ 60秒窗口找到 {len(match_results_60['exact_matches'])} 个精确匹配")
            match_results = match_results_60
        else:
            print(f"⚠️ 60秒窗口仍无精确匹配，使用30秒结果")

    exact_matches = match_results['exact_matches']
    potential_matches = match_results['potential_matches']

    # 3. 输出结果
    print(f"\n📊 多维度匹配结果:")
    print(f"  测试配对数: {len(results)}")
    print(f"  精确匹配数: {len(exact_matches)}")
    print(f"  潜在匹配数: {len(potential_matches)}")
    print(f"  精确匹配率: {len(exact_matches)/len(results)*100:.1f}%" if results else "0%")
    print(f"  潜在匹配率: {len(potential_matches)/len(results)*100:.1f}%" if results else "0%")

    # 4. 详细精确匹配信息
    if exact_matches:
        print(f"\n🎯 精确匹配详情:")
        for i, match in enumerate(exact_matches[:3]):  # 显示前3个
            print(f"  精确匹配 {i+1}:")
            print(f"    币种: {match['coin']}")
            print(f"    测试金额: {match['test_amount']:.2f} USDT")
            print(f"    平台金额: {match['platform_amount']:.2f} USDT")
            print(f"    金额差异: {match['amount_diff']:.2f} USDT ({match['amount_diff_percent']:.2f}%)")
            print(f"    时间差异: {match['time_diff_seconds']:.1f} 秒")
            print(f"    金额匹配: {'✅' if match['amount_match'] else '❌'} (分数: {match['amount_match_score']:.3f})")
            print(f"    时间匹配: {'✅' if match['time_match'] else '❌'} (分数: {match['time_match_score']:.3f})")
            print(f"    综合匹配分数: {match['overall_match_score']:.3f}")
            print(f"    测试对敲分数: {match['test_wash_score']:.3f}")
            print(f"    平台分数: {match['platform_score']:.2f}")
            print(f"    多头盈亏: {match['test_profit_long']:.2f}")
            print(f"    空头盈亏: {match['test_profit_short']:.2f}")
            print()

    # 5. 潜在匹配信息
    if potential_matches:
        print(f"\n🔍 潜在匹配详情（前3个）:")
        for i, match in enumerate(potential_matches[:3]):
            print(f"  潜在匹配 {i+1}:")
            print(f"    币种: {match['coin']}")
            print(f"    金额匹配: {'✅' if match['amount_match'] else '❌'} (差异: {match['amount_diff_percent']:.2f}%)")
            print(f"    时间匹配: {'✅' if match['time_match'] else '❌'} (差异: {match['time_diff_seconds']:.1f}秒)")
            print(f"    综合匹配分数: {match['overall_match_score']:.3f}")
            print()

    # 5. 保存结果
    output_file = "new_pairing_analysis.json"
    import json
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump({
            'test_pairs': [
                {
                    'pair_id': r.pair_id,
                    'coin': r.record_a.coin,
                    'long_amount': r.record_a.usdt_amount,
                    'short_amount': r.record_b.usdt_amount,
                    'time_diff': r.time_diff_seconds,
                    'wash_score': r.wash_score,
                    'profit_hedge_score': r.profit_hedge_score,
                    'long_profit': r.record_a.profit_loss,
                    'short_profit': r.record_b.profit_loss
                } for r in results
            ],
            'exact_matches': exact_matches,
            'potential_matches': potential_matches,
            'summary': {
                'total_test_pairs': len(results),
                'exact_matches': len(exact_matches),
                'potential_matches': len(potential_matches),
                'exact_match_rate_percent': len(exact_matches)/len(results)*100 if results else 0,
                'potential_match_rate_percent': len(potential_matches)/len(results)*100 if results else 0
            }
        }, f, ensure_ascii=False, indent=2, default=str)

    print(f"💾 结果已保存到: {output_file}")


if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        test_new_pairing_logic()
    else:
        main()
