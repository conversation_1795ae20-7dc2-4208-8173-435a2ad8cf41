#!/usr/bin/env python3
"""
并发执行引擎
实现同时开仓，按时间平仓的并发交易执行模式
"""

import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

from test_data_generator import TestCase
from unified_trader import UnifiedTrader
from trade_recorder import TradeRecorder
from position_manager import PositionManager, position_manager


@dataclass
class TradeTask:
    """交易任务"""
    test_case: TestCase
    group_id: str
    open_time: datetime
    reverse_time: datetime  # 开反向订单时间
    close_time: datetime    # 平仓时间
    status: str = "pending"  # pending, long_opened, both_opened, closed, failed
    long_order_id: Optional[str] = None   # 多头订单ID
    short_order_id: Optional[str] = None  # 空头订单ID
    close_long_order_id: Optional[str] = None  # 平多头订单ID
    close_short_order_id: Optional[str] = None # 平空头订单ID
    long_result: Optional[Dict[str, Any]] = None
    short_result: Optional[Dict[str, Any]] = None
    close_long_result: Optional[Dict[str, Any]] = None
    close_short_result: Optional[Dict[str, Any]] = None


class ConcurrentExecutionEngine:
    """并发执行引擎"""
    
    def __init__(self):
        """初始化并发执行引擎"""
        self.position_manager = position_manager
        self.is_running = False
        self.start_time = None
        self.total_tests = 0
        self.current_test = 0
    
    def execute_batch_concurrent(self, test_cases: List[TestCase],
                                 progress_callback: Optional[callable] = None) -> List[Dict[str, Any]]:
        """分批并发执行测试：每批3个不同币种，完成一批再执行下一批

        Args:
            test_cases: 测试用例列表
            progress_callback: 进度回调函数

        Returns:
            执行结果列表
        """
        results = []
        self.is_running = True
        self.start_time = datetime.now()
        self.total_tests = len(test_cases)

        print(f"🚀 开始分批并发执行 {len(test_cases)} 个测试用例")
        print("=" * 60)

        # 初始化盈亏追踪
        from unified_trader import UnifiedTrader
        balance_tracker = UnifiedTrader()

        # 记录总体开始余额
        print("💰 记录测试开始前总体账户状态...")
        initial_balance = balance_tracker.get_account_balance()
        if initial_balance.get('success'):
            initial_pnl = float(initial_balance['unrealised_pnl'])
            initial_equity = float(initial_balance['equity'])
            print(f"  初始权益: {initial_equity:,.2f} USDT")
            print(f"  初始未实现盈亏: {initial_pnl:,.3f} USDT")
        else:
            initial_pnl = 0
            initial_equity = 0
            print(f"  ❌ 获取初始余额失败")

        try:
            # 将测试用例分批，每批最多3个不同币种
            batches = self._create_batches(test_cases)

            for batch_num, batch in enumerate(batches, 1):
                if not self.is_running:
                    break

                print(f"\n🔄 执行第 {batch_num}/{len(batches)} 批 ({len(batch)} 个测试用例)")
                print("-" * 50)

                # 执行当前批次
                batch_results = self._execute_single_batch(batch, progress_callback)
                results.extend(batch_results)

                print(f"✅ 第 {batch_num} 批完成，成功: {sum(1 for r in batch_results if r['success'])}/{len(batch_results)}")

                # 每10批次或每100个测试用例报告一次总体盈亏
                if batch_num % 10 == 0 or batch_num == len(batches):
                    self._report_overall_pnl(batch_num, len(batches), len(results),
                                            initial_pnl, initial_equity, balance_tracker)

                # 批次间休息（等当前批次完全结束后再开始下一批）
                if batch_num < len(batches):
                    print(f"⏳ 第 {batch_num} 批已完全结束，准备开始第 {batch_num + 1} 批...")
                    print("⏳ 批次间休息 5秒...")
                    time.sleep(5)

        except KeyboardInterrupt:
            print("\n⏹️  用户中断测试执行")
            self.is_running = False
        except Exception as e:
            print(f"\n❌ 并发执行错误: {e}")
            import traceback
            traceback.print_exc()
        finally:
            # 清理所有仓位
            for coin in ['BTC', 'ETH', 'DOG']:
                self.position_manager.close_all_positions(coin)

        # 最终盈亏报告
        print(f"\n📊 最终测试报告:")
        print(f"✅ 并发执行完成，成功: {sum(1 for r in results if r.get('success'))}/{len(results)}")
        self._report_final_pnl(initial_pnl, initial_equity, balance_tracker, len(results))

        return results

    def _report_overall_pnl(self, batch_num: int, total_batches: int, completed_tests: int,
                           initial_pnl: float, initial_equity: float, balance_tracker):
        """报告总体盈亏情况"""
        try:
            current_balance = balance_tracker.get_account_balance()
            if current_balance.get('success'):
                current_pnl = float(current_balance['unrealised_pnl'])
                current_equity = float(current_balance['equity'])

                # 计算盈亏变化
                total_pnl_change = current_pnl - initial_pnl
                equity_change = current_equity - initial_equity
                avg_pnl_per_test = total_pnl_change / completed_tests if completed_tests > 0 else 0

                print(f"\n📊 进度报告 - 第 {batch_num}/{total_batches} 批次完成 ({completed_tests} 个测试)")
                print(f"💰 当前权益: {current_equity:,.2f} USDT (变化: {equity_change:+,.3f})")
                print(f"📈 当前未实现盈亏: {current_pnl:,.3f} USDT")
                print(f"📊 总盈亏变化: {total_pnl_change:+,.3f} USDT")
                print(f"📋 平均每笔盈亏: {avg_pnl_per_test:+,.4f} USDT")
                print("-" * 50)
            else:
                print(f"❌ 获取当前余额失败: {current_balance.get('message')}")
        except Exception as e:
            print(f"❌ 盈亏报告失败: {e}")

    def _report_final_pnl(self, initial_pnl: float, initial_equity: float,
                         balance_tracker, total_tests: int):
        """报告最终盈亏情况"""
        try:
            final_balance = balance_tracker.get_account_balance()
            if final_balance.get('success'):
                final_pnl = float(final_balance['unrealised_pnl'])
                final_equity = float(final_balance['equity'])

                # 计算最终盈亏
                total_pnl_change = final_pnl - initial_pnl
                equity_change = final_equity - initial_equity
                avg_pnl_per_test = total_pnl_change / total_tests if total_tests > 0 else 0

                print(f"💰 最终权益: {final_equity:,.2f} USDT")
                print(f"📈 最终未实现盈亏: {final_pnl:,.3f} USDT")
                print(f"📊 总盈亏变化: {total_pnl_change:+,.3f} USDT")
                print(f"📋 平均每笔盈亏: {avg_pnl_per_test:+,.4f} USDT")

                if total_pnl_change > 0:
                    print(f"🎉 测试盈利: +{total_pnl_change:.3f} USDT")
                elif total_pnl_change < 0:
                    print(f"📉 测试亏损: {total_pnl_change:.3f} USDT")
                else:
                    print(f"⚖️  测试盈亏平衡")

                print("=" * 60)
            else:
                print(f"❌ 获取最终余额失败: {final_balance.get('message')}")
        except Exception as e:
            print(f"❌ 最终盈亏报告失败: {e}")

    def _create_batches(self, test_cases: List[TestCase]) -> List[List[TestCase]]:
        """将测试用例分批，每批最多3个不同币种

        Args:
            test_cases: 测试用例列表

        Returns:
            分批后的测试用例列表
        """
        batches = []
        current_batch = []
        used_coins = set()

        for test_case in test_cases:
            # 如果当前批次已有3个币种，或者当前币种已在批次中，开始新批次
            if len(current_batch) >= 3 or test_case.coin in used_coins:
                if current_batch:
                    batches.append(current_batch)
                current_batch = []
                used_coins = set()

            current_batch.append(test_case)
            used_coins.add(test_case.coin)

        # 添加最后一批
        if current_batch:
            batches.append(current_batch)

        return batches

    def _execute_single_batch(self, batch: List[TestCase],
                             progress_callback: Optional[callable] = None) -> List[Dict[str, Any]]:
        """执行单个批次的测试用例（新流程）

        当前批次内的执行流程：
        1. 开仓阶段：3个币种间隔1秒开多头，再间隔0-60秒开空头
        2. 等待阶段：全部开仓完成后，统一等待3秒
        3. 平仓阶段：平仓第一笔单（立即），平仓第二笔单（0-60秒随机等待）

        Args:
            batch: 当前批次的测试用例
            progress_callback: 进度回调函数

        Returns:
            当前批次的执行结果
        """
        print(f"🔄 当前批次包含 {len(batch)} 个币种:")
        for i, case in enumerate(batch, 1):
            print(f"  {i}. {case.coin} {case.usdt_amount}U")

        # 记录批次开始前的未实现盈亏
        from unified_trader import UnifiedTrader
        balance_tracker = UnifiedTrader()

        print("💰 记录批次开始前未实现盈亏...")
        start_balance = balance_tracker.get_account_balance()
        if start_balance.get('success'):
            start_unrealised_pnl = float(start_balance['unrealised_pnl'])
            print(f"  开始未实现盈亏: {start_unrealised_pnl:,.3f} USDT")
        else:
            print(f"  ❌ 获取开始余额失败: {start_balance.get('message')}")
            start_unrealised_pnl = 0

        # 开仓阶段
        print("\n📈 开仓阶段：开多头 → 开空头")
        trade_tasks = self._execute_opening_phase(batch)

        # 等待阶段
        print("\n⏳ 等待阶段：全部开仓完成，统一等待3秒...")
        time.sleep(3)

        # 平仓阶段
        print("\n🔄 平仓阶段：确保每组开多少单就平多少单")
        results = self._execute_closing_phase(trade_tasks, progress_callback)

        # 确保所有仓位都已清理
        print("\n🧹 清理当前批次的所有仓位...")
        for case in batch:
            self.position_manager.close_all_positions(case.coin)

        print(f"✅ 当前批次所有 {len(batch)} 个币种已完全平仓")

        # 记录批次结束后的未实现盈亏
        print("💰 记录批次结束后未实现盈亏...")
        end_balance = balance_tracker.get_account_balance()
        if end_balance.get('success'):
            end_unrealised_pnl = float(end_balance['unrealised_pnl'])

            # 计算批次实际盈亏（直接使用未实现盈亏的变化）
            batch_actual_pnl = end_unrealised_pnl - start_unrealised_pnl

            print(f"  结束未实现盈亏: {end_unrealised_pnl:,.3f} USDT")
            print(f"  📊 批次实际盈亏: {batch_actual_pnl:,.3f} USDT")

            # 更新每个交易记录的实际盈亏
            self._update_trade_records_with_actual_pnl(results, batch_actual_pnl, len(batch))
        else:
            print(f"  ❌ 获取结束余额失败: {end_balance.get('message')}")

        return results

    def _update_trade_records_with_actual_pnl(self, results: List[Dict[str, Any]],
                                             batch_pnl: float, batch_size: int):
        """更新交易记录的实际盈亏数据"""
        if batch_size == 0:
            return

        # 平均分配批次盈亏到每个交易
        avg_pnl_per_trade = batch_pnl / batch_size

        print(f"  📝 更新交易记录: 平均每笔盈亏 {avg_pnl_per_trade:,.3f} USDT")

        for result in results:
            if result.get('success') and result.get('trade_record'):
                trade_record = result['trade_record']

                # 更新交易记录的盈亏字段
                trade_record.profit_loss = avg_pnl_per_trade

                # 更新数据库中的记录
                try:
                    from trade_recorder import TradeRecorder
                    recorder = TradeRecorder()
                    recorder._update_profit_loss(trade_record.record_id, avg_pnl_per_trade)
                except Exception as e:
                    print(f"    ❌ 更新记录 {trade_record.record_id} 失败: {e}")

    def _add_amount_variation(self, base_amount: float, coin: str) -> float:
        """为金额添加0-3%的随机变动"""
        import random

        # 添加0-3%的随机变动
        variation_percent = random.uniform(0, 3.0)  # 0% 到 +3%
        variation = base_amount * (variation_percent / 100)
        final_amount = base_amount + variation

        # 根据币种特点调整精度
        if coin == 'DOG':
            return round(final_amount, 2)  # DOG保留2位小数
        elif coin == 'ETH':
            return round(final_amount, 1)  # ETH保留1位小数
        else:  # BTC
            return round(final_amount, 0)  # BTC取整数

    def _execute_opening_phase(self, batch: List[TestCase]) -> List[TradeTask]:
        """开仓阶段：3个币种间隔1秒开多头，再间隔0-60秒开空头"""
        import random

        trade_tasks = []
        current_time = datetime.now()

        # 第一步：间隔1秒开多头
        print("  📈 第一步：间隔1秒开多头...")
        for i, test_case in enumerate(batch):
            if not self.is_running:
                break

            # 计算开多头时间
            open_long_time = current_time + timedelta(seconds=i * 1)

            # 为每个币种生成随机的开空头等待时间（2-60秒）
            short_wait_seconds = random.randint(2, 60)
            open_short_time = open_long_time + timedelta(seconds=short_wait_seconds)

            group_id = f"{test_case.coin}_{test_case.case_id}_{open_long_time.strftime('%H%M%S')}"

            trade_task = TradeTask(
                test_case=test_case,
                group_id=group_id,
                open_time=open_long_time,
                reverse_time=open_short_time,
                close_time=None  # 平仓时间在平仓阶段确定
            )

            trade_tasks.append(trade_task)
            print(f"    📋 {test_case.coin} {test_case.usdt_amount}U - 开多头: {open_long_time.strftime('%H:%M:%S')}, 开空头: {open_short_time.strftime('%H:%M:%S')} (等待{short_wait_seconds}秒)")

        # 执行开多头
        for task in trade_tasks:
            if not self.is_running:
                break

            # 等待到开多头时间
            wait_time = (task.open_time - datetime.now()).total_seconds()
            if wait_time > 0:
                print(f"    ⏳ 等待 {wait_time:.1f}秒 开多头 {task.test_case.coin}...")
                time.sleep(wait_time)

            # 执行开多头
            self._execute_open_long_position(task)

        # 第二步：按各自时间开空头
        print("  📉 第二步：按各自时间开空头...")
        self._execute_open_short_positions(trade_tasks)

        return trade_tasks

    def _concurrent_open_long_positions(self, batch: List[TestCase]) -> List[TradeTask]:
        """第一阶段：并发开多头仓位（当前批次）"""
        trade_tasks = []
        current_time = datetime.now()

        # 创建交易任务（只处理当前批次）
        for i, test_case in enumerate(batch):
            if not self.is_running:
                break

            # 计算各个阶段的时间
            open_time = current_time + timedelta(seconds=i * 1)  # 间隔1秒开多头
            reverse_time = open_time + timedelta(seconds=test_case.wait_seconds)  # 等待后开空头
            close_time = reverse_time + timedelta(seconds=test_case.wait_seconds)  # 再等待后平仓

            group_id = f"{test_case.coin}_{test_case.case_id}_{open_time.strftime('%H%M%S')}"

            trade_task = TradeTask(
                test_case=test_case,
                group_id=group_id,
                open_time=open_time,
                reverse_time=reverse_time,
                close_time=close_time
            )

            trade_tasks.append(trade_task)
            print(f"  📋 计划 #{i+1}: {test_case.coin} {test_case.usdt_amount}U")
            print(f"      开多头: {open_time.strftime('%H:%M:%S')}")
            print(f"      开空头: {reverse_time.strftime('%H:%M:%S')}")
            print(f"      平仓: {close_time.strftime('%H:%M:%S')}")

        # 按开仓时间执行开多头操作（只处理当前批次的币种）
        for task in trade_tasks:
            if not self.is_running:
                break

            # 等待到开仓时间
            wait_time = (task.open_time - datetime.now()).total_seconds()
            if wait_time > 0:
                print(f"⏳ 等待 {wait_time:.1f}秒 开多头 {task.test_case.coin}...")
                time.sleep(wait_time)

            # 执行开多头
            self._execute_open_long_position(task)

        return trade_tasks

    def _execute_open_short_positions(self, trade_tasks: List[TradeTask]):
        """按各自时间开空头"""
        # 获取成功开多头的任务
        long_opened_tasks = [task for task in trade_tasks if task.status == "long_opened"]
        if not long_opened_tasks:
            print("    ❌ 没有成功开多头的任务")
            return

        # 按开空头时间排序
        long_opened_tasks.sort(key=lambda x: x.reverse_time)

        print(f"    📊 待开空头任务: {len(long_opened_tasks)} 个")
        for task in long_opened_tasks:
            wait_seconds = (task.reverse_time - datetime.now()).total_seconds()
            print(f"      📋 {task.test_case.coin}: {wait_seconds:.1f}秒后开空头")

        # 并发监控，到时间就开空头（带间隔）
        short_interval = 1.0  # 开空头间隔1秒
        last_short_time = None

        while long_opened_tasks and self.is_running:
            current_time = datetime.now()

            # 检查是否有任务到了开空头时间
            ready_to_short = []
            for task in long_opened_tasks:
                if current_time >= task.reverse_time:
                    ready_to_short.append(task)

            # 按币种排序，确保开空头顺序一致
            ready_to_short.sort(key=lambda x: x.test_case.coin)

            # 执行开空头（带间隔）
            for task in ready_to_short:
                # 确保开空头间隔
                if last_short_time:
                    elapsed = (datetime.now() - last_short_time).total_seconds()
                    if elapsed < short_interval:
                        wait_time = short_interval - elapsed
                        print(f"    ⏳ 开空头间隔等待 {wait_time:.1f}秒...")
                        time.sleep(wait_time)

                # 执行开空头
                self._execute_open_short_position(task)
                last_short_time = datetime.now()

            # 从待开空头列表中移除已完成的任务
            for task in ready_to_short:
                if task in long_opened_tasks:
                    long_opened_tasks.remove(task)

            # 如果还有待开空头任务，短暂休眠后继续检查
            if long_opened_tasks:
                time.sleep(0.5)  # 每0.5秒检查一次

    def _execute_open_long_position(self, task: TradeTask):
        """执行开多头仓位操作"""
        coin = task.test_case.coin
        base_usdt_amount = task.test_case.usdt_amount

        # 为多头订单的USDT金额添加0-3%的随机变动
        long_usdt_amount = self._add_amount_variation(base_usdt_amount, coin)
        task.long_usdt_amount = long_usdt_amount  # 保存多头USDT金额

        print(f"📈 开多头: {coin} {long_usdt_amount}U")

        # 检查仓位管理器
        if not self.position_manager.start_new_group(coin, task.group_id):
            print(f"❌ {coin} 仓位冲突，跳过")
            task.status = "failed"
            return

        try:
            # 执行买入操作（开多头）
            trader = UnifiedTrader()
            long_result = trader.buy(coin, long_usdt_amount)

            if long_result.get('success'):
                task.long_order_id = long_result.get('order_id')
                task.long_result = long_result
                task.status = "long_opened"

                # 更新仓位管理器
                self.position_manager.open_long_position(coin, task.long_order_id, long_usdt_amount)

                print(f"✅ {coin} 开多头成功: {task.long_order_id}")
            else:
                print(f"❌ {coin} 开多头失败: {long_result.get('error')}")
                task.status = "failed"
                self.position_manager.close_all_positions(coin)

        except Exception as e:
            print(f"❌ {coin} 开多头异常: {e}")
            task.status = "failed"
            self.position_manager.close_all_positions(coin)

    def _execute_closing_phase(self, trade_tasks: List[TradeTask],
                              progress_callback: Optional[callable] = None) -> List[Dict[str, Any]]:
        """平仓阶段：平仓第一笔单（立即），平仓第二笔单（0-60秒随机等待）"""
        import random

        results = []

        # 获取所有成功开多空仓位的任务
        both_opened_tasks = [task for task in trade_tasks if task.status == "both_opened"]
        if not both_opened_tasks:
            print("  ❌ 没有成功开多空仓位的任务")
            return results

        print(f"  📊 待平仓任务: {len(both_opened_tasks)} 个")
        print("  🔄 所有币种立即开始平仓（并行）...")

        # 使用线程池并行执行所有币种的平仓
        import threading

        def close_single_coin(task):
            """单个币种的平仓函数"""
            try:
                result = self._execute_close_all_positions(task)
                results.append(result)

                # 进度回调
                if progress_callback:
                    progress_callback(len(results), len(both_opened_tasks), result)
            except Exception as e:
                print(f"❌ {task.test_case.coin} 平仓异常: {e}")
                results.append({
                    'test_case': task.test_case,
                    'execution_time': 0,
                    'trade_result': {'success': False, 'error': 'close_failed'},
                    'trade_record': None,
                    'success': False
                })

        # 为每个币种创建线程并立即启动
        threads = []
        for task in both_opened_tasks:
            thread = threading.Thread(target=close_single_coin, args=(task,))
            threads.append(thread)
            thread.start()
            print(f"    🚀 {task.test_case.coin} 立即开始平仓")

        # 等待所有线程完成
        for thread in threads:
            thread.join()

        # 检查平仓结果，确保所有仓位都已平仓
        failed_closes = [r for r in results if not r.get('success')]
        if failed_closes:
            print(f"🚨 严重警告: {len(failed_closes)} 个币种平仓失败！")
            for failed in failed_closes:
                coin = failed['test_case'].coin
                print(f"  ❌ {coin}: {failed.get('error', 'unknown_error')}")
            print(f"🛑 由于存在未平仓位，系统将暂停执行，请手动检查！")

            # 设置停止标志，阻止后续批次执行
            self.is_running = False

        print(f"  ✅ 所有币种平仓完成")

        # 处理失败的任务
        for task in trade_tasks:
            if task.status == "failed":
                result = {
                    'test_case': task.test_case,
                    'execution_time': 0,
                    'trade_result': {'success': False, 'error': 'open_failed'},
                    'trade_record': None,
                    'success': False
                }
                results.append(result)

        return results



    def _execute_open_short_position(self, task: TradeTask):
        """执行开空头仓位操作"""
        coin = task.test_case.coin
        base_usdt_amount = task.test_case.usdt_amount

        # 为空头订单的USDT金额添加0-3%的随机变动（与多头不同）
        short_usdt_amount = self._add_amount_variation(base_usdt_amount, coin)
        task.short_usdt_amount = short_usdt_amount  # 保存空头USDT金额

        print(f"📉 开空头: {coin} {short_usdt_amount}U")

        try:
            # 执行卖出操作（开空头）
            trader = UnifiedTrader()
            short_result = trader.sell(coin, short_usdt_amount)

            if short_result.get('success'):
                task.short_order_id = short_result.get('order_id')
                task.short_result = short_result
                task.status = "both_opened"  # 多空都开了

                # 更新仓位管理器
                self.position_manager.open_short_position(coin, task.short_order_id, short_usdt_amount)

                print(f"✅ {coin} 开空头成功: {task.short_order_id}")
            else:
                print(f"❌ {coin} 开空头失败: {short_result.get('error')}")
                task.status = "failed"
                self.position_manager.close_all_positions(coin)

        except Exception as e:
            print(f"❌ {coin} 开空头异常: {e}")
            task.status = "failed"
            self.position_manager.close_all_positions(coin)



    def _execute_close_all_positions(self, task: TradeTask) -> Dict[str, Any]:
        """执行平仓操作（平掉多空两个仓位）"""
        coin = task.test_case.coin
        base_amount = task.test_case.usdt_amount

        # 使用开仓时的实际USDT金额
        long_usdt_amount = getattr(task, 'long_usdt_amount', base_amount)
        short_usdt_amount = getattr(task, 'short_usdt_amount', base_amount)

        print(f"🔄 平仓: {coin} 多头{long_usdt_amount}U 空头{short_usdt_amount}U")

        try:
            trader = UnifiedTrader()

            # 记录平多头前的账户状态
            print(f"  📉 平多头: {coin} {long_usdt_amount}U")
            long_before_balance = trader.get_account_balance()
            long_before_pnl = float(long_before_balance['unrealised_pnl']) if long_before_balance.get('success') else 0

            close_long_result = trader.close_long(coin, long_usdt_amount)

            # 记录平多头后的账户状态
            long_after_balance = trader.get_account_balance()
            long_after_pnl = float(long_after_balance['unrealised_pnl']) if long_after_balance.get('success') else 0
            long_pnl = long_after_pnl - long_before_pnl  # 多头订单盈亏

            # 检查平多头是否真正成功（必须有订单ID）
            close_long_order_id = close_long_result.get('order_id')
            if close_long_result.get('success') and close_long_order_id:
                task.close_long_order_id = close_long_order_id
                task.close_long_result = close_long_result
                print(f"  ✅ 平多头成功: {close_long_order_id} (盈亏: {long_pnl:+.3f}U)")

                # 更新仓位管理器
                self.position_manager.close_long_position(coin)

                # 记录多头订单
                recorder = TradeRecorder()
                long_trade_result = {
                    'coin': coin,
                    'order_type': 'long',
                    'usdt_amount': long_usdt_amount,
                    'open_order_id': task.long_order_id,
                    'close_order_id': close_long_order_id,
                    'pnl': long_pnl,
                    'execution_time': (datetime.now() - task.open_time).total_seconds()
                }
                long_record = recorder.record_single_order(
                    trade_result=long_trade_result,
                    test_case_id=f"{task.test_case.case_id}_long",
                    test_type=task.test_case.test_type
                )
                long_close_success = True
            else:
                # 平仓失败或没有订单ID，尝试重试
                print(f"  ❌ 平多头失败: {close_long_result.get('error', 'no_order_id')}")
                print(f"  � 尝试重新平多头...")

                # 重试平仓
                retry_result = self._retry_close_position(trader, coin, long_usdt_amount, 'long')
                if retry_result['success']:
                    task.close_long_order_id = retry_result['order_id']
                    print(f"  ✅ 重试平多头成功: {retry_result['order_id']}")
                    long_close_success = True
                else:
                    print(f"  🚨 重试失败，{coin} 多头仓位未能平仓，停止继续操作")
                    long_close_success = False

                    # 平仓失败，直接返回错误，不继续平空头
                    return {
                        'test_case': task.test_case,
                        'execution_time': 0,
                        'error': 'long_close_failed_after_retry',
                        'success': False
                    }

            # 随机间隔2-60秒后平空头仓位
            import random
            close_interval = random.randint(2, 60)
            print(f"  ⏳ 等待 {close_interval}秒 后平空头...")
            time.sleep(close_interval)

            # 记录平空头前的账户状态
            print(f"  📈 平空头: {coin} {short_usdt_amount}U")
            short_before_balance = trader.get_account_balance()
            short_before_pnl = float(short_before_balance['unrealised_pnl']) if short_before_balance.get('success') else 0

            close_short_result = trader.close_short(coin, short_usdt_amount)

            # 记录平空头后的账户状态
            short_after_balance = trader.get_account_balance()
            short_after_pnl = float(short_after_balance['unrealised_pnl']) if short_after_balance.get('success') else 0
            short_pnl = short_after_pnl - short_before_pnl  # 空头订单盈亏

            # 检查平空头是否真正成功（必须有订单ID）
            close_short_order_id = close_short_result.get('order_id')
            if close_short_result.get('success') and close_short_order_id:
                task.close_short_order_id = close_short_order_id
                task.close_short_result = close_short_result
                task.status = "closed"
                print(f"  ✅ 平空头成功: {close_short_order_id} (盈亏: {short_pnl:+.3f}U)")

                # 更新仓位管理器
                self.position_manager.close_short_position(coin)

                # 记录空头订单
                short_trade_result = {
                    'coin': coin,
                    'order_type': 'short',
                    'usdt_amount': short_usdt_amount,
                    'open_order_id': task.short_order_id,
                    'close_order_id': close_short_order_id,
                    'pnl': short_pnl,
                    'execution_time': (datetime.now() - task.open_time).total_seconds()
                }
                short_record = recorder.record_single_order(
                    trade_result=short_trade_result,
                    test_case_id=f"{task.test_case.case_id}_short",
                    test_type=task.test_case.test_type
                )
                short_close_success = True
            else:
                # 平仓失败或没有订单ID，尝试重试
                print(f"  ❌ 平空头失败: {close_short_result.get('error', 'no_order_id')}")
                print(f"  � 尝试重新平空头...")

                # 重试平仓
                retry_result = self._retry_close_position(trader, coin, short_usdt_amount, 'short')
                if retry_result['success']:
                    task.close_short_order_id = retry_result['order_id']
                    print(f"  ✅ 重试平空头成功: {retry_result['order_id']}")
                    short_close_success = True
                else:
                    print(f"  🚨 重试失败，{coin} 空头仓位未能平仓")
                    short_close_success = False

            # 检查是否都成功（只有多头平仓成功才会到这里）
            both_success = (long_close_success and short_close_success)

            if both_success:
                print(f"✅ {coin} 完全平仓成功 (多头: {long_pnl:+.3f}U, 空头: {short_pnl:+.3f}U)")

                return {
                    'test_case': task.test_case,
                    'execution_time': (datetime.now() - task.open_time).total_seconds(),
                    'long_pnl': long_pnl,
                    'short_pnl': short_pnl,
                    'total_pnl': long_pnl + short_pnl,
                    'success': True
                }
            else:
                print(f"❌ {coin} 平仓失败 - 存在未平仓位，系统将停止后续操作")
                task.status = "failed"

                # 平仓失败是严重错误，需要人工干预
                print(f"🚨 紧急提醒: {coin} 仓位未完全平仓，请手动检查并处理！")

                return {
                    'test_case': task.test_case,
                    'execution_time': 0,
                    'error': 'partial_close_failed',
                    'long_success': long_close_success,
                    'short_success': short_close_success,
                    'success': False
                }

        except Exception as e:
            print(f"❌ {coin} 平仓异常: {e}")
            task.status = "failed"
        finally:
            # 确保清理仓位
            self.position_manager.close_all_positions(coin)

        # 失败情况
        return {
            'test_case': task.test_case,
            'execution_time': 0,
            'trade_result': {'success': False, 'error': 'close_failed'},
            'trade_record': None,
            'success': False
        }

    def _retry_close_position(self, trader, coin: str, usdt_amount: float,
                             position_type: str, max_retries: int = 3) -> Dict[str, Any]:
        """重试平仓操作"""
        for attempt in range(max_retries):
            print(f"    🔄 第 {attempt + 1}/{max_retries} 次重试平{position_type}...")

            try:
                if position_type == 'long':
                    result = trader.close_long(coin, usdt_amount)
                else:
                    result = trader.close_short(coin, usdt_amount)

                # 检查是否有订单ID
                order_id = result.get('order_id')
                if result.get('success') and order_id:
                    print(f"    ✅ 重试成功，获得订单ID: {order_id}")
                    return {
                        'success': True,
                        'order_id': order_id,
                        'attempt': attempt + 1
                    }
                else:
                    print(f"    ❌ 第 {attempt + 1} 次重试失败: {result.get('error', 'no_order_id')}")

            except Exception as e:
                print(f"    ❌ 第 {attempt + 1} 次重试异常: {e}")

            # 重试间隔
            if attempt < max_retries - 1:
                import time
                time.sleep(2)  # 等待2秒后重试

        print(f"    🚨 所有重试都失败了")
        return {
            'success': False,
            'error': 'all_retries_failed',
            'attempts': max_retries
        }

    def stop_execution(self):
        """停止执行"""
        self.is_running = False
        print("⏹️  正在停止并发执行...")


if __name__ == "__main__":
    # 测试并发执行引擎
    from test_data_generator import WashTradingTestGenerator
    
    engine = ConcurrentExecutionEngine()
    generator = WashTradingTestGenerator()
    
    # 生成测试用例（3个币种各一个）
    test_cases = generator.generate_test_batch_with_position_management(3)

    print(f"生成了 {len(test_cases)} 个测试用例（3个币种各一个）")
    
    # 执行测试
    results = engine.execute_batch_concurrent(test_cases)
    
    print(f"\n✅ 并发执行完成，成功: {sum(1 for r in results if r['success'])}/{len(results)}")
