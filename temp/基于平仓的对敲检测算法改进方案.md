# 基于平仓的对敲检测算法改进方案

## 1. 问题分析

### 1.1 当前基于开仓的问题
- **实时性差**: 需要等待仓位完全平仓才能进行检测
- **数据完整性要求高**: 要求开仓和平仓都在查询窗口内
- **流式处理困难**: 不适合实时数据流场景
- **检测延迟**: 无法在平仓时立即发现对敲行为

### 1.2 改为基于平仓的优势
- **实时检测**: 每次平仓时立即触发检测
- **即时风险评估**: 平仓时已知完整的盈亏信息
- **更好的流式处理**: 适合实时数据流场景
- **降低数据要求**: 只需要平仓时的完整信息

## 2. 核心改进策略

### 2.1 检测触发机制改变
```
原有方式: 批量处理 → 仓位构建 → 开仓时间匹配 → 检测
改进方式: 平仓事件 → 实时触发 → 平仓时间匹配 → 即时检测
```

### 2.2 时间窗口调整
- **开仓时间窗口**: 保留用于历史分析
- **平仓时间窗口**: 新增，用于实时检测
- **双重验证**: 开仓和平仓时间都在合理范围内

### 2.3 数据流改进
```
平仓事件 → 查找时间窗口内的反向平仓 → 匹配验证 → 风险评分 → 实时告警
```

## 3. 算法实现改进

### 3.1 新增平仓触发检测器
```python
class CloseBasedWashTradingDetector:
    """基于平仓的对敲检测器"""
    
    def __init__(self, config):
        self.config = config
        self.close_time_window = config.get('close_time_window', 30)  # 平仓时间窗口
        self.recent_closes = {}  # 最近平仓记录缓存
        
    def on_position_close(self, close_event):
        """平仓事件处理"""
        # 1. 记录平仓事件
        # 2. 查找时间窗口内的反向平仓
        # 3. 进行对敲检测
        # 4. 实时告警
```

### 3.2 双重时间验证机制
```python
def enhanced_time_matching(self, pos_a, pos_b):
    """增强的时间匹配机制"""
    # 开仓时间匹配（历史验证）
    open_time_score = self.calculate_open_time_match(pos_a, pos_b)
    
    # 平仓时间匹配（实时验证）
    close_time_score = self.calculate_close_time_match(pos_a, pos_b)
    
    # 综合时间匹配分数
    return {
        'open_time_score': open_time_score,
        'close_time_score': close_time_score,
        'combined_score': (open_time_score * 0.4 + close_time_score * 0.6)
    }
```

### 3.3 实时缓存机制
```python
class RecentCloseCache:
    """最近平仓缓存"""
    
    def __init__(self, window_seconds=300):  # 5分钟缓存窗口
        self.window_seconds = window_seconds
        self.cache = defaultdict(list)  # {contract_name: [close_events]}
        
    def add_close_event(self, close_event):
        """添加平仓事件"""
        contract = close_event.contract_name
        self.cache[contract].append(close_event)
        self._cleanup_expired(contract)
        
    def find_potential_matches(self, close_event, time_window=30):
        """查找潜在匹配"""
        # 在缓存中查找反向平仓事件
```

## 4. 配置参数调整

### 4.1 新增配置项
```yaml
close_based_detection:
  enabled: true
  close_time_window: 30          # 平仓时间窗口（秒）
  cache_window: 300              # 缓存窗口（秒）
  real_time_alert: true          # 实时告警
  
time_matching:
  open_weight: 0.4               # 开仓时间权重
  close_weight: 0.6              # 平仓时间权重
  
scoring_weights:
  profit_hedge: 0.4              # 盈亏对敲权重
  time_match: 0.25               # 时间匹配权重（综合）
  amount_match: 0.25             # 金额匹配权重
  duration: 0.1                  # 持仓时长权重
```

### 4.2 风险等级调整
```yaml
risk_levels:
  critical:
    wash_score_min: 0.9
    profit_hedge_min: 0.9
    close_time_match_min: 0.8     # 新增平仓时间匹配要求
    
  high:
    wash_score_min: 0.85
    close_time_match_min: 0.6
```

## 5. 实现步骤

### 5.1 第一阶段：基础架构
1. 创建平仓事件监听器
2. 实现最近平仓缓存机制
3. 开发双重时间验证逻辑

### 5.2 第二阶段：检测逻辑
1. 实现基于平仓的同账户检测
2. 实现基于平仓的跨账户检测
3. 集成现有评分算法

### 5.3 第三阶段：优化和集成
1. 性能优化和测试
2. 与现有系统集成
3. 实时告警机制

## 6. 兼容性考虑

### 6.1 向后兼容
- 保留原有基于开仓的检测逻辑
- 支持双模式运行（开仓+平仓）
- 配置开关控制检测模式

### 6.2 渐进式迁移
- 先并行运行两种模式
- 对比检测效果
- 逐步切换到平仓模式

## 7. 预期效果

### 7.1 性能提升
- **实时性**: 从分钟级延迟降低到秒级
- **检测率**: 提高15-25%的检测覆盖率
- **误报率**: 通过双重验证降低误报

### 7.2 业务价值
- **风险控制**: 更快发现和阻止对敲行为
- **合规性**: 满足实时监控要求
- **用户体验**: 减少正常用户的误判影响
