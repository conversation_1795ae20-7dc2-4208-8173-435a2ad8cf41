#!/usr/bin/env python3
import sqlite3
import json
from datetime import datetime

def check_database():
    """检查数据库内容"""
    print("🔍 检查数据库内容...")
    
    try:
        conn = sqlite3.connect('wash_trading_test.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # 获取所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print(f"📊 数据库中的表: {len(tables)} 个")
        
        for table in tables:
            table_name = table[0]
            print(f"\n📋 表: {table_name}")
            
            # 获取表结构
            cursor.execute(f"PRAGMA table_info({table_name});")
            columns = cursor.fetchall()
            
            print(f"  列: {[col[1] for col in columns]}")
            
            # 获取记录数
            cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
            count = cursor.fetchone()[0]
            print(f"  记录数: {count}")
            
            # 显示最新的几条记录
            if count > 0:
                cursor.execute(f"SELECT * FROM {table_name} LIMIT 3;")
                rows = cursor.fetchall()
                
                print(f"  示例记录:")
                for i, row in enumerate(rows):
                    print(f"    记录 {i+1}: {dict(row)}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查数据库失败: {e}")

if __name__ == "__main__":
    check_database()
